import { Component, Input, OnInit } from '@angular/core';
import { InvestorsService } from '@core/services/investors.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogRef,
  NbIconModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
declare const google: any;

@Component({
  selector: 'app-investor-payments-delete',
  templateUrl: './investor-payments-delete.component.html',
  styleUrls: ['./investor-payments-delete.component.scss'],
  standalone: true,
  imports: [NbCardModule, NbIconModule, NbButtonModule],
})
export class InvestorPaymentsDeleteComponent implements OnInit {
  @Input() paymentId: number;
  loading = false;

  constructor(
    protected dialogRef: NbDialogRef<any>,
    public spinner: NgxSpinnerService,
    private investorService: InvestorsService,
    private toast: NbToastrService,
  ) {
    this.paymentId = 0;
  }

  async ngOnInit(): Promise<void> { }

  close(): void {
    this.dialogRef.close(false);
  }

  delete(): void {
    this.loading = true;
    this.investorService.deletePayment(this.paymentId).subscribe(
      (res: any) => {
        this.loading = false;
        this.toast.success('Payment item deleted successfully.', 'Success!');
        this.dialogRef.close(res);
      },
      (err: any) => {
        this.loading = false;
        this.toast.danger('Something went wrong please try again.', 'Error!');
        this.dialogRef.close(false);
      },
    );
  }
}
