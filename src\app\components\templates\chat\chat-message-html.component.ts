/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { DocumentService } from '@core/services/document.service';
import { SharedService } from '@core/services/shared.service';
import { NbButtonModule, NbIconModule } from '@nebular/theme';

/**
 * Chat message component.
 */
@Component({
  selector: 'app-chat-message-html',
  templateUrl: 'chat-message-html.component.html',
  styleUrls: ['chat-message-html.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, NbIconModule, NbButtonModule],
})
export class NbChatMessageHtmlComponent {
  /**
   * Message sender
   * @type {string}
   */
  @Input() sender!: string;

  /**
   * Note Or Task
   * @type {boolean}
   */
  @Input() noteType = false;

  /**
   * Message Title if input is a task`
   * @type {string}
   */
  @Input() messageTitle!: string;

  /**
   * Message type
   * @type {boolean}
   */
  @Input() metaData!: any;

  /**
   * Message sender
   * @type {string}
   */
  @Input() message!: string;

  /**
   * Message send date
   * @type {Date}
   */
  @Input() date!: Date;

  /**
   * Message send date format, default 'shortTime'
   * @type {string}
   */
  @Input() dateFormat = 'shortTime';

  @Input() taskInformation!: any;
  @Input() reply!: boolean;

  @Output() taskCompletion = new EventEmitter<any>();
  @Output() taskEdit = new EventEmitter<any>();
  userId: number;
  isAdmin: boolean;

  constructor(
    private documentService: DocumentService,
    public sanitizer: DomSanitizer,
    private sharedService: SharedService,
  ) {
    this.userId = this.sharedService.getUserIdValue.userId;
    this.isAdmin = this.sharedService.isAdmin();
  }

  async downloadFile(file: any): Promise<void> {
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  completeTask() {
    this.taskCompletion.emit(this.taskInformation);
  }
  editTask() {
    this.taskEdit.emit(this.taskInformation);
  }
}
