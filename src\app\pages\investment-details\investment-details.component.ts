import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, computed, OnInit, signal, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NbTabsetComponent } from '@components/molecules/tabset/tabset.component';
import { ApiResponse } from '@core/models/interface/api-response.interface';
import { InvestmentDetailsResponse } from '@core/models/response/investment-details.response';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import { InvestmentApplyComponent } from '@features/investments/investment-apply/investment-apply.component';
import { InvestmentDocumentsComponent } from '@features/investments/investment-document/investment-documents/investment-documents.component';
import { InvestmentFinancialsComponent } from '@features/investments/investment-financials/investment-financials.component';
import { InvestmentKeyDataComponent } from '@features/investments/investment-key-data/investment-key-data.component';
import { InvestmentOverviewComponent } from '@features/investments/investment-overview/investment-overview.component';
import { NbAccordionModule, NbCardModule, NbIconModule, NbTabsetModule } from '@nebular/theme';
@Component({
  selector: 'app-investment-details',
  templateUrl: './investment-details.component.html',
  styleUrls: ['./investment-details.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbTabsetModule,
    NbAccordionModule,
    InvestmentOverviewComponent,
    InvestmentFinancialsComponent,
    InvestmentKeyDataComponent,
    InvestmentDocumentsComponent,
    InvestmentApplyComponent,
  ],
})
export class InvestmentDetailsComponent implements OnInit {
  @ViewChild('tabset') tabsetEl!: NbTabsetComponent;
  apiResponse = signal<ApiResponse<InvestmentDetailsResponse> | null>(null);
  // Computed signals for easier access
  investmentDetails = computed(() => {
    const response = this.apiResponse();
    return response?.success ? response.payload : null;
  });
  investmentId: any;
  entityName: any;
  tabId = 1;
  userId: any;
  showApply: any = false;
  investorId: number | undefined;

  constructor(
    private route: Router,
    private sharedService: SharedService,
    private investmentService: InvestmentService,
    private investorsService: InvestorsService,
    protected cd: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.investmentId = this.sharedService.getFormParamValue.investmentId;
    this.userId = this.sharedService.getFormParamValue.userId;

    const changeTab = this.sharedService.getFormParamValue.changeTab;

    if (changeTab) {
      this.sharedService.setFormParamValue({
        investmentId: this.investmentId,
        userId: this.userId,
        changeTab: false,
      });
      this.changeTab(null);
    }

    if (this.investmentId) {
      this.getInvestmentDetail();
    }
  }

  private getInvestmentDetail(): void {
    if (!this.investmentId) {
      return;
    }

    // Determine investorId once
    const investorId = this.isInvestor() ? this.investorsService.accountValue.investorId : undefined;

    this.investmentService.getInvestmentDetail(this.investmentId, investorId).subscribe({
      next: (data: ApiResponse<InvestmentDetailsResponse>) => {
        this.apiResponse.set(data);
        if (data.success && data.payload) {
          this.showApply = data.payload.showApply;
          this.entityName = data.payload.title;
        }
      },
      error: (error) => {
        console.error('Error loading investment details:', error);
        this.apiResponse.set({
          payload: null as any,
          error: 'Failed to load investment details',
          success: false,
        });
      },
    });
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isManager(): boolean {
    return this.sharedService.isManager();
  }

  investmentChange(event: any): void {
    // this.entityName = event.title;
    this.investmentId = this.sharedService.getFormParamValue.investmentId;
    this.getInvestmentDetail();
    // this.showApply = event.showApply;
  }

  tabClick(event: any): void {
    this.tabId = Number(event.tabId);
    // this.getInvestmentDetail();
  }

  changeTab(event: any): void {
    setTimeout(() => {
      if (this.tabsetEl.tabs) {
        const activateTab = this.tabsetEl.tabs.find((t: any) => t.tabId === (this.tabId + 1).toString());
        if (activateTab) {
          this.tabsetEl.selectTab(activateTab);
        }
        this.cd.detectChanges();
      }
    }, 1000);
  }
}
