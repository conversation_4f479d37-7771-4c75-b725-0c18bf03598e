<div class="flex flex-wrap -mx-2 my-[15px]">
  <div class="w-2/12 px-2">
    <nb-select
      placeholder="Please Select "
      fullWidth
      size="large"
      shape="semi-round"
      status="basic"
      [(ngModel)]="notesTaskFilter"
      (selectedChange)="filterNotesTasks()"
      name="investmentId"
    >
      <nb-option *ngFor="let filter of filterSelection" [value]="filter.name">
        {{ filter.name }}
      </nb-option>
    </nb-select>
  </div>
  <div class="w-7/12 px-2">
    <p-selectButton
      class="allRoundedSelect"
      [options]="notesOptions"
      [(ngModel)]="notesFilter"
      optionLabel="label"
      (onChange)="filterNotesTasks()"
      optionValue="value"
      styleClass="shadow-none"
    >
    </p-selectButton>
  </div>
  <div class="w-3/12 px-2" style="display: inline-flex; justify-content: flex-end">
    <div class="search-filter fullHeight" fullWidth>
      <nb-form-field class="fullHeight">
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input
          class="fullHeight"
          type="text"
          placeholder="Search"
          nbInput
          [(ngModel)]="globalSearchValue"
          (input)="filterNotesTasks()"
        />
      </nb-form-field>
    </div>
    <!-- (input)="filterGlobal($event)"  -->
  </div>
</div>

<div class="flex flex-wrap -mx-2 my-[15px]">
  <div class="w-full px-2">
    <nb-card
      style="
        box-shadow: 0px 0px 4px 1px #d3d3d36b;
        border-radius: 12px;
        margin-bottom: 0px;
        border-bottom-left-radius: 0px;
      "
    >
      <nb-card-body [nbSpinner]="loadingData">
        <div class="flex flex-wrap -mx-2">
          <div class="xl:w-6/12 px-2 lg:w-6/12 px-2 md:w-6/12 px-2 sm:w-full px-2 w-full px-2">
            <input
              *ngIf="selectedNoteTaskOption === 'task'"
              type="text"
              placeholder="Title"
              nbInput
              [(ngModel)]="taskTitle"
            />
            <app-ckeditor [(ngModel)]="editorContent"> </app-ckeditor>
            <div class="files-list" *ngIf="pendingDocuments && pendingDocuments.length > 0">
              <div class="single-file" *ngFor="let file of pendingDocuments; let i = index">
                <div class="info">
                  <div class="attachment">
                    <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
                  </div>
                  <div class="name">
                    {{ file?.name }}
                  </div>
                  <div class="delete">
                    <nb-icon class="file-delete" icon="close-circle" (click)="clearDocuments(file)"> </nb-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="xl:w-6/12 px-2 lg:w-6/12 px-2 md:w-6/12 px-2 sm:w-full px-2">
            <div class="flex flex-wrap -mx-2">
              <div *ngIf="selectedNoteTaskOption === 'task'" class="lg:w-4/12 px-2 my-[15px]">
                <!-- <label> <strong>Due Date</strong> <strong class="text-lime required"> &nbsp; *
                                    </strong></label> -->
                <nb-form-field class="fullHeight">
                  <input
                    type="text"
                    placeholder="Select Date"
                    nbInput
                    fieldSize="large"
                    fullWidth
                    rInputMask="99/99/9999"
                    id="dueDate"
                    name="dueDate"
                    [nbDatepicker]="dateTimePicker"
                    [(ngModel)]="taskDate"
                    class="fullHeight"
                  />
                  <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>
                </nb-form-field>
                <nb-datepicker #dateTimePicker (dateChange)="dateChange($event)"></nb-datepicker>
              </div>
              <div *ngIf="selectedNoteTaskOption === 'task'" class="lg:w-4/12 px-2 my-[15px]">
                <!-- <label>
                                    <strong> Assigned to </strong> <strong class="text-lime required"> &nbsp; *
                                    </strong>
                                </label> -->
                <div class="scrollable-container fullHeight">
                  <nb-select
                    fullWidth
                    size="large"
                    placeholder="Assign To"
                    style="overflow: visible"
                    name="assignedTo"
                    [(ngModel)]="taskAssignedTo"
                    class="fullHeight"
                  >
                    <nb-option *ngFor="let user of originatorUsers" [value]="user.userId">
                      {{ user.contactName }}
                    </nb-option>
                  </nb-select>
                </div>
              </div>
              <div
                [class]="
                  selectedNoteTaskOption === 'task'
                    ? 'lg:w-4/12 px-2 my-[15px] submitSectionEnd'
                    : 'lg:w-full px-2 my-[15px] submitSection'
                "
              >
                <input
                  type="file"
                  style="display: none"
                  multiple
                  #fileDropRef
                  id="fileDropRef"
                  (change)="fileBrowseHandler($event)"
                  accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
                />

                <nb-icon
                  icon="attachFileIcon"
                  style="
                    height: 34px;
                    width: 34px;
                    min-height: 34px;
                    min-width: 34px;
                    margin-top: 10px;
                    cursor: pointer;
                  "
                  pack="custom"
                  (click)="fileDropRef.click()"
                >
                </nb-icon>
                <div [class]="selectedNoteTaskOption === 'task' ? 'attachmentNumberTask' : 'attachmentNumber'">
                  {{ pendingDocuments.length > 0 ? pendingDocuments.length : null }}
                </div>
                <button
                  class="button float-right"
                  nbButton
                  size="large"
                  status="primary"
                  [disabled]="updating"
                  [nbSpinner]="loadingData"
                  (click)="updateValue()"
                >
                  UPDATE
                </button>
              </div>
            </div>
          </div>
          <nb-progress-bar style="width: 100%" *ngIf="progress" [value]="progress" status="primary">
            Uploading {{ progress }}%
          </nb-progress-bar>
        </div>
      </nb-card-body>
    </nb-card>
    <div>
      <p-selectButton
        [options]="noteTaskOptions"
        [(ngModel)]="selectedNoteTaskOption"
        optionLabel="label"
        class="roundedSelect"
        optionValue="value"
        (onChange)="setConfig()"
        styleClass="shadow-none"
      >
        <ng-template let-item>
          <div>
            <nb-icon [icon]="item.icon" pack="custom" style="width: 50px"></nb-icon>
            {{ item.label }}
          </div>
        </ng-template>
      </p-selectButton>
    </div>
  </div>
</div>
<app-chat
  [noMessagesPlaceholder]="isInternalNote ? 'No notes yet.' : 'No messages yet.'"
  size="large"
  [nbSpinner]="loadingData"
  [scrollTop]="scrollTop"
>
  <app-chat-message
    *ngFor="let msg of messages"
    [metaData]="msg"
    [type]="'html'"
    [message]="msg.message"
    [reply]="msg.isSender"
    [sender]="msg.userName"
    [date]="msg.dateCreated"
    [files]="msg.files"
    [avatar]="msg.userName"
    [avatarColor]="msg.avatarColor"
    [dateFormat]="msg.date"
    [noteType]="msg.task"
    [messageTitle]="msg.messageTitle"
    [taskInformation]="msg.taskInformation"
    (taskCompletion)="toggleTaskCompletion($event)"
    (taskEdit)="editTask($event)"
  >
  </app-chat-message>
</app-chat>
