import { HTTP_INTERCEPTORS } from '@angular/common/http';
import {
  MSAL_GUARD_CONFIG,
  MSAL_INSTANCE,
  MSAL_INTERCEPTOR_CONFIG,
  MsalBroadcastService,
  MsalGuard,
  MsalGuardConfiguration,
  MsalInterceptor,
  MsalInterceptorConfiguration,
  MsalService,
} from '@azure/msal-angular';
import {
  InteractionType,
  IPublicClientApplication,
  LogLevel,
  PopupRequest,
  PublicClientApplication,
} from '@azure/msal-browser';
import { environment } from '@environments/environment';

export const loginEntraRequest: PopupRequest = {
  scopes: ['openid', 'profile', 'User.Read'], // Graph basic profile
  prompt: 'select_account', // always show account‑picker;
};

export const loginSilentEntraRequest: PopupRequest = {
  scopes: ['openid', 'profile', 'User.Read'], // Graph basic profile
  prompt: 'none', // silent SSO
};
const redirectUrl = '/login';

const isIE = window.navigator.userAgent.indexOf('MSIE ') > -1 || window.navigator.userAgent.indexOf('Trident/') > -1;

export function MSALInstanceFactory(): IPublicClientApplication {
  if (typeof window === 'undefined' || !window.crypto?.subtle) {
    throw new Error('MSAL requires browser environment with Web Crypto support.');
  }

  return new PublicClientApplication({
    auth: {
      clientId: environment.azure.clientId,
      authority: `https://login.microsoftonline.com/common`, // multi-tenant
      // authority: `https://login.microsoftonline.com/${environment.azure.tenantId}`, // single tenant
      // knownAuthorities: [`login.microsoftonline.com`],
      redirectUri: `${window.location.origin}${redirectUrl}`,
      postLogoutRedirectUri: `${window.location.origin}${redirectUrl}`,
    },
    cache: {
      cacheLocation: 'localStorage',
      storeAuthStateInCookie: isIE,
    },
    system: {
      loggerOptions: {
        loggerCallback: (level, message, containsPii) => {
          if (containsPii) {
            return;
          }

          switch (level) {
            case LogLevel.Error:
              console.error(message);
              return;
            case LogLevel.Info:
              console.info(message);
              return;
            case LogLevel.Verbose:
              console.debug(message);
              return;
            case LogLevel.Warning:
              console.warn(message);
              return;
          }
        },
        logLevel: LogLevel.Verbose,
        piiLoggingEnabled: false,
      },
    },
  });
}

// MsalInterceptor automatically attaches bearer tokens to any URL you register in protectedResourceMap.
export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, string[]>();

  protectedResourceMap.set('https://graph.microsoft.com/v1.0/me', ['user.read']);

  return {
    interactionType: InteractionType.Popup,
    protectedResourceMap,
  };
}

export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return {
    interactionType: InteractionType.Popup,
    authRequest: {
      scopes: ['user.read'],
    },
  };
}

// MSAL token injections
export function MSALProviders() {
  return [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: MsalInterceptor,
      multi: true,
    },
    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory,
    },
    {
      // MSAL Guard Configuration
      provide: MSAL_GUARD_CONFIG,
      useFactory: MSALGuardConfigFactory,
    },
    {
      // MSAL Interceptor Configuration
      provide: MSAL_INTERCEPTOR_CONFIG,
      useFactory: MSALInterceptorConfigFactory,
    },
    MsalService,
    MsalGuard,
    MsalBroadcastService,
  ];
}
