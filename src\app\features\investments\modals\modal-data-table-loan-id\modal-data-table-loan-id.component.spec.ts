import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalDataTableLoanIdComponent } from './modal-data-table-loan-id.component';

describe('ModalDataTableLoanIdComponent', () => {
  let component: ModalDataTableLoanIdComponent;
  let fixture: ComponentFixture<ModalDataTableLoanIdComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalDataTableLoanIdComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalDataTableLoanIdComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
