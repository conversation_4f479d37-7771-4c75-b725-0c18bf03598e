import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogRef, NbToastrService, NbCardModule, NbButtonModule, NbIconModule } from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { DocumentService } from '@core/services/document.service';
declare const google: any;

const place = null as typeof google.maps.places.PlaceResult;

@Component({
  selector: 'app-delete-document',
  templateUrl: './delete-document.component.html',
  styleUrls: ['./delete-document.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbButtonModule, NbIconModule],
})
export class DeleteDocumentComponent implements OnInit {
  @Input() documentKey: string;
  @Input() userId: any;
  @Input() investorId?: any;
  @Input() investmentId?: any;

  constructor(
    protected dialogRef: NbDialogRef<any>,
    public spinner: NgxSpinnerService,
    private documentService: DocumentService,
    private toast: NbToastrService,
  ) {
    this.documentKey = '';
  }

  async ngOnInit(): Promise<void> {}

  close(): void {
    this.dialogRef.close(false);
  }

  delete(): void {
    const formData = new FormData();
    formData.append('DocumentKey', this.documentKey);

    if (this.userId) formData.append('UserId', this.userId);
    if (this.investorId) formData.append('InvestorId', this.investorId);
    if (this.investmentId) formData.append('InvestmentId', this.investmentId);

    this.documentService.deleteDocument(formData).subscribe(
      (res: any) => {
        this.toast.success('File deleted successfully.', 'Success!');
        this.dialogRef.close(true);
      },
      (err: any) => {
        this.toast.danger('Something went wrong please try again.', 'Error!');
        this.dialogRef.close(true);
      },
    );
  }
}
