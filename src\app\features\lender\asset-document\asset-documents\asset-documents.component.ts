import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { TypeKey } from '@core/models/config';
import { FacilitiesFilterResponse, FacilityFilter } from '@core/models/response/facilities-filter.response';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';
import { AddAssetDocumentComponent } from '../add-asset-document/add-asset-document.component';

@Component({
  selector: 'app-asset-documents',
  templateUrl: './asset-documents.component.html',
  styleUrls: ['./asset-documents.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbInputModule,
    NbFormFieldModule,
    TableModule,
    SkeletonModule,
    NbButtonModule,
  ],
})
export class AssetDocumentsComponent
  implements OnInit, OnDestroy, AfterViewChecked {
  @Output() changeTab = new EventEmitter<boolean>();
  @Input() removeAction: any;
  @Input() allDocs = false;
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  documents: any[] = [];
  statusData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  assetKeyDataId: number | undefined;
  dateFilterData: any[] = [];
  docTypes: any;
  facilitiesData!: FacilityFilter[];
  lenders: any;
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private dialogService: NbDialogService,
    private documentService: DocumentService,
    private cdr: ChangeDetectorRef,
    private assetService: AssetService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.dateFilterData = this.sharedService.getDateFilterRows();

    // if (this.isInvestor()) {
    //   this.investorId = this.investorsService.accountValue.investorId;
    // } else {
    if (this.sharedService.getFormParamValue) {
      this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId;
    }
    // }

    this.filterParams = {
      pageNumber: 1,
      pageSize: this.assetKeyDataId ? 100 : 10,
    } as Filters;
    this.getDocumentType();
    this.getFacilities();
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    if (!this.allDocs) {
      if (this.assetKeyDataId) {
        this.filterParams.assetId = this.assetKeyDataId;
      }
    }
    this.assetService.getDocuments(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.documents = (data as any).payload.documents;
        this.totalRecords = (data as any).payload.rows;
        this.dtTrigger.next(this.documents);
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  sortByCategory(docs: any[]) {
    if (this.filterParams.sortOrder == 'desc') {
      return docs.sort((a, b) => (a.category > b.category ? 1 : b.category > a.category ? -1 : 0));
    } else {
      return docs.sort((a, b) => (a.category < b.category ? 1 : b.category < a.category ? -1 : 0));
    }
  }

  private getDocumentType(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_DocumentType).subscribe((response: any) => {
      if (response.success) {
        this.docTypes = response.payload;
      }
    });
  }

  private getFacilities(): void {
    this.assetService.getFacilities().subscribe((response: FacilitiesFilterResponse) => {
      if (response.success) {
        this.facilitiesData = response.payload;
      }
    });
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isOriginatorManager(): boolean {
    return this.sharedService.isOriginatorManager();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  exportUser(): void {
    this.filterParams.export = true;
  }

  ngOnDestroy(): void { }

  addNewDocument(): void {
    this.dialogService
      .open(AddAssetDocumentComponent, {
        context: {},
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getList();
        }
      });
  }

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  deleteDocument(documentKey: string, userId: any, investorId: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Delete Document',
          message: 'Are you sure you want to delete this document?',
          yesButton: 'Delete',
          noButton: 'Cancel',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.documentService.deleteAssetDocument({ documentKey, userId }).subscribe(
            (response: any) => {
              if (response.success) {
                this.toast.success('File deleted successfully.', 'Success!');
              } else {
                this.toast.danger('Something went wrong please try again.', 'Error!');
              }
              this.getList();
            },
            (err: any) => {
              this.toast.danger('Something went wrong please try again.', 'Error!');
            },
          );
        }
      });
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  next(): void {
    this.changeTab.emit(true);
  }
}
