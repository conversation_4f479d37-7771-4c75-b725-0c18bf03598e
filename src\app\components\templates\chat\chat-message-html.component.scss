.files-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 5px 0px;

  .single-file {
    display: flex;
    margin: 0 7px;
    margin-left: 0px;

    .delete {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .attachment {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: center;
    }

    .name {
      font-size: 12px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
    }

    .size {
      font-size: 12px;
      font-weight: 500;
      color: #a4a4a4;
      margin: 0;
      margin-bottom: 0.25rem;
    }

    .info {
      display: flex;
      // padding: 8px;
      border: 2px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
      margin: 4px 3px;
      cursor: pointer;
    }
  }
}

.triangle {
  border-left: 0px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 1px solid #ededed;
  border-bottom-width: 13px;
  border-bottom-color: #ededed;
  border-bottom-style: solid;
  position: absolute;
  right: 40px;
  bottom: 13.5px;
}

.triangle-over {
  width: 0;
  height: 0;
  border-left: 37px solid transparent;
  border-right: 18px solid transparent;
  border-bottom: 81px solid #ffffff;
  border-bottom-width: 17px;
  border-bottom-color: #ffffff;
  border-bottom-style: solid;
  position: absolute;
  right: 42px;
  bottom: 14.6px;
}

.grey-triangle {
  border-left: 0px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 1px solid #ededed;
  border-bottom-width: 13px;
  border-bottom-color: #ededed;
  border-bottom-style: solid;
  position: absolute;
  right: 40px;
  bottom: 13.5px;
}

.grey-triangle-over {
  width: 0;
  height: 0;
  border-left: 37px solid transparent;
  border-right: 18px solid transparent;
  border-bottom: 81px solid #f7f7f7;
  border-bottom-width: 17px;
  border-bottom-color: #f7f7f7;
  border-bottom-style: solid;
  position: absolute;
  right: 42px;
  bottom: 14.6px;
}

.red-triangle {
  border-left: 0px solid transparent;
  border-right: 13px solid transparent;
  border-bottom: 1px solid #fc3d3d;
  border-bottom-width: 15px;
  border-bottom-color: #fc3d3d;
  border-bottom-style: solid;
  position: absolute;
  right: 40px;
  bottom: 14px;
}

.red-triangle-over {
  width: 0;
  height: 0;
  border-left: 37px solid transparent;
  border-right: 18px solid transparent;
  border-bottom: 81px solid #fff5f5;
  border-bottom-width: 17px;
  border-bottom-color: #fff5f5;
  border-bottom-style: solid;
  position: absolute;
  right: 41px;
  bottom: 14.6px;
}

.triangle-noreply {
  border-right: 0px solid transparent;
  border-left: 12px solid transparent;
  border-bottom: 1px solid #ededed;
  border-bottom-width: 17px;
  border-bottom-color: #ededed;
  border-bottom-style: solid;
  position: absolute;
  left: 40px;
  bottom: 14px;
}

.triangle-over-noreply {
  border-left: 12px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 6px solid #ffffff;
  border-bottom-width: 17px;
  border-bottom-color: #ffffff;
  border-bottom-style: solid;
  position: absolute;
  left: 42px;
  bottom: 14.6px;
}

.grey-triangle-noreply {
  border-right: 0px solid transparent;
  border-left: 12px solid transparent;
  border-bottom: 1px solid #ededed;
  border-bottom-width: 17px;
  border-bottom-color: #ededed;
  border-bottom-style: solid;
  position: absolute;
  left: 40px;
  bottom: 14px;
}

.grey-triangle-over-noreply {
  border-left: 12px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 6px solid #f7f7f7;
  border-bottom-width: 17px;
  border-bottom-color: #f7f7f7;
  border-bottom-style: solid;
  position: absolute;
  left: 42px;
  bottom: 14.6px;
}

.red-triangle-noreply {
  border-right: 0px solid transparent;
  border-left: 12px solid transparent;
  border-bottom: 1px solid #fc3d3d;
  border-bottom-width: 15px;
  border-bottom-color: #fc3d3d;
  border-bottom-style: solid;
  position: absolute;
  left: 40px;
  bottom: 13px;
}

.red-triangle-over-noreply {
  border-left: 12px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 6px solid #fff5f5;
  border-bottom-width: 17px;
  border-bottom-color: #fff5f5;
  border-bottom-style: solid;
  position: absolute;
  left: 42px;
  bottom: 14.6px;
}
