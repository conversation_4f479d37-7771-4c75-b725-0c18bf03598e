import { Component, inject, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { EntraAuthService } from '@core/services/auth/msal-entra.service';
import { NbIconLibraries } from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { environment } from 'src/environments/environment';
import MenuIcons from './core/helpers/menu.icons';
// declare ga as a function to set and sent the events
declare let gtag: (action: string, field: string, value?: string) => void;

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [RouterOutlet],
})
export class AppComponent implements OnInit {
  // title = 'SydneyWyde - Client Portal';

  private spinner = inject(NgxSpinnerService);
  private iconLibraries = inject(NbIconLibraries);
  private router = inject(Router);

  entraAuthService = inject(EntraAuthService);

  organisation: any;

  constructor() {
    this.spinner.show();

    if (environment.gaTrackingId) {
      // register google tag manager
      // this.registerGoogleTagManager();
    }
  }

  private registerGoogleTagManager(): void {
    const gTagManagerScript = document.createElement('script');
    gTagManagerScript.async = true;
    gTagManagerScript.src = `https://www.googletagmanager.com/gtag/js?id=${environment.gaTrackingId}`;
    document.head.appendChild(gTagManagerScript);

    // register google analytics
    const gaScript = document.createElement('script');
    gaScript.innerHTML = `
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', '${environment.gaTrackingId}');
      `;
    document.head.appendChild(gaScript);

    // subscribe to router events and send page views to Google Analytics
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        gtag('set', 'page', event.urlAfterRedirects);
        gtag('send', 'pageview');
      }
    });
  }

  async ngOnInit() {
    this.registerIcons();
    await this.entraAuthService.init();
    this.entraAuthService.trackEventsEntra();
  }

  private registerIcons(): void {
    this.iconLibraries.registerSvgPack('custom', {
      dashboardIcon: MenuIcons.dashboardIcon(),
      searchIcon: MenuIcons.searchIcon(),
      bellIcon: MenuIcons.bellIcon(),
      settingIcon: MenuIcons.settingIcon(),
      complianceIcon: MenuIcons.complianceIcon(),
      workspaceIcon: MenuIcons.workspaceIcon(),
      tableViewIcon: MenuIcons.tableViewIcon(),
      kanbanIcon: MenuIcons.kanbanIcon(),
      createWorkspaceIcon: MenuIcons.createWorkspaceIcon(),
      cloneWorkspaceIcon: MenuIcons.cloneWorkspaceIcon(),
      editIcon: MenuIcons.editIcon(),
      archiveIcon: MenuIcons.archiveIcon(),
      lockIcon: MenuIcons.lockIcon(),
      unlockIcon: MenuIcons.unlockIcon(),
      keyIcon: MenuIcons.keyIcon(),
      resetPasswordIcon: MenuIcons.resetPasswordIcon(),
      clientIcon: MenuIcons.clientIcon(),
      assignedToIcon: MenuIcons.assignedToIcon(),
      exportIcon: MenuIcons.exportIcon(),
      unlockBlackIcon: MenuIcons.unlockBlackIcon(),
      countryIcon: MenuIcons.countryIcon(),
      lockBlackIcon: MenuIcons.lockBlackIcon(),
      notesIcon: MenuIcons.notesIcon(),
      restorewhiteIcon: MenuIcons.restorewhiteIcon(),
      archivewhiteIcon: MenuIcons.archivewhiteIcon(),
      sendWhiteIcon: MenuIcons.sendWhiteIcon(),
      notesBlackIcon: MenuIcons.notesBlackIcon(),
      logoutWhiteIcon: MenuIcons.logoutWhiteIcon(),
      advisorDetailsIcon: MenuIcons.advisorDetailsIcon(),
      eyeIcon: MenuIcons.eyeIcon(),
      overviewIcon: MenuIcons.overviewIcon(),
      financialsIcon: MenuIcons.financialsIcon(),
      copyIcon: MenuIcons.copyIcon(),
      investmentIcon: MenuIcons.investmentIcon(),
      investmentsIcon: MenuIcons.investmentsIcon(),
      document30Icon: MenuIcons.document30Icon(),
      document40Icon: MenuIcons.document40Icon(),
      imageLocationIcon: MenuIcons.imageLocationIcon(),
      chatFilterIcon: MenuIcons.chatFilterIcon(),
      switchIcon: MenuIcons.switchIcon(),
      paymentIcon: MenuIcons.paymentIcon(),
      checklistIcon: MenuIcons.checklistIcon(),
      checklistDeleteIcon: MenuIcons.checklistDeleteIcon(),
      checklistDragIcon: MenuIcons.checklistDragIcon(),
      covenantTabIcon: MenuIcons.covenantTabIcon(),
      covenantEditPencilIcon: MenuIcons.covenantEditPencilIcon(),
      attachFileIcon: MenuIcons.attachFileIcon(),
      checkBoxIcon: MenuIcons.checkBoxIcon(),
      assetEditIcon: MenuIcons.assetEditIcon(),
      addTaskIcon: MenuIcons.addTaskIcon(),
      activitiesIcon: MenuIcons.activitiesIcon(),
    });
  }
}
