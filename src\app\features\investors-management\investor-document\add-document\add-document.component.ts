import { HttpEventType } from '@angular/common/http';
import { AfterViewChecked, ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  NbAlertModule,
  NbDialogRef,
  NbProgressBarModule,
  NbSpinnerModule,
  NbToastrService,
  NbCardModule,
  NbIconModule,
  NbSelectModule,
  NbButtonModule,
} from '@nebular/theme';
import { map } from 'rxjs/operators';
import { DocumentType } from '@core/models/config';
import { DocumentService } from '@core/services/document.service';
import { InvestmentService } from '@core/services/investment.service';
import { SharedService } from '@core/services/shared.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-add-document',
  templateUrl: './add-document.component.html',
  styleUrls: ['./add-document.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbAlertModule,
    NbProgressBarModule,
    NbSpinnerModule,
    NbButtonModule,
  ],
})
export class AddDocumentComponent implements OnInit, AfterViewChecked {
  investorId?: number;

  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;

  form: UntypedFormGroup;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';
  title = '';
  investmentLookup: any;

  uploadedDocuments: any;
  progress: any;

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    public toastr: NbToastrService,
    private cdr: ChangeDetectorRef,
    private sharedService: SharedService,
    private documentService: DocumentService,
    private toast: NbToastrService,
    private investmentService: InvestmentService,
  ) {
    this.form = this.formBuilder.group({
      investmentId: [null, Validators.required],
    });
  }

  ngOnInit(): void {
    this.investorId = this.sharedService.getFormParamValue.investorId;

    this.returnUrl = this.route.snapshot.queryParams.returnUrl || '/';

    if (this.investorId) {
      this.investmentService.getInvestmentLookup({}).subscribe((response: any) => {
        if (response.success) {
          this.investmentLookup = response.payload;
        }
      });
    }
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  close(): void {
    this.dialogRef.close(false);
  }

  clearDocuments(): void {
    this.uploadedDocuments = null;
    this.fileDropRef.nativeElement.value = '';
    this.loading = false;
    this.cdr.detectChanges();
  }

  get f() {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.form.invalid || !this.uploadedDocuments) {
      return;
    }

    this.loading = true;

    this.prepareFilesList(this.uploadedDocuments);
    this.cdr.detectChanges();
  }

  backtoList(): void {
    this.router.navigate(['/users']);
  }

  onFileDropped(event: any): void {
    // this.prepareFilesList(event);
    if (event && event.length > 1) {
      this.toast.danger('Multiple files upload not supported, Please drag single file only', 'Upload Error!');
      return;
    }
    this.uploadedDocuments = event;
  }

  fileBrowseHandler(event: any): void {
    // this.prepareFilesList(event.target.files);
    this.uploadedDocuments = event.target.files;
  }

  prepareFilesList(files: any[]): void {
    // const extensions = ['png', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'doc', 'docx', 'odt'];
    const allowedExtensions = /(\.jpg|\.jpeg|\.png|\.pdf|\.xls|\.xlsx|\.doc|\.docx|\.odt)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toast.danger('File type not supported', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = null;
          this.loading = false;
          this.submitted = false;
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toast.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = null;
          this.submitted = false;
          this.loading = false;
          return;
        }
      }
    }

    const formData = new FormData();

    formData.append('DocumentType', DocumentType.Document as any);
    formData.append('SaveLog', true as any);

    if (this.investorId) {
      formData.append('investorId', this.investorId as any);
    }

    if (this.form.value.investmentId) {
      formData.append('investmentId', this.form.value.investmentId as any);
    }

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.documentService
      .uploadDocument(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.toast.success('File Uploaded Successfully!', 'Success!');
            this.dialogRef.close(true);
            this.uploadedDocuments = [];
          }
        },
        (err: any) => this.toast.success(err.error.message, 'Failed!'),
      );
  }
}
