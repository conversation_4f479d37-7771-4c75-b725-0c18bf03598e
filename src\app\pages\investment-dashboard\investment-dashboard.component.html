<div class="flex flex-wrap -mx-2">
  <div class="md:w-9/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Investment Overview</h5>
    </div>
  </div>
  <div class="md:w-3/12 px-2 text-right my-[15px]">
    <!-- <button class="float-right" nbButton status="primary" (click)="createWorkspace()">
            Create New Workspace
        </button> -->

    <nb-select
      placeholder=""
      fullWidth
      size="large"
      shape="semi-round"
      status="basic"
      name="investmentId"
      [(ngModel)]="investmentId"
      (selectedChange)="loadDashboard()"
    >
      <nb-option *ngFor="let entityType of investmentLookup" [value]="entityType.id" [disabled]="entityType.disabled">
        {{ entityType.title }}
      </nb-option>
    </nb-select>
  </div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div *ngIf="currentInvestmentOverviewData">{{ currentInvestmentOverviewData.title }}</div>
            </div>
          </div>
        </div>

        <br />

        <div class="image-container">
          <!-- <img *ngIf="investment.imageData" [src]="investment.imageData"> -->

          <ng-container *ngIf="currentInvestmentOverviewData">
            <img *ngIf="currentInvestmentOverviewData.imageUrl" [src]="currentInvestmentOverviewData.imageUrl" />

            <img
              *ngIf="
                !currentInvestmentOverviewData.imageUrl &&
                currentInvestmentOverviewData &&
                currentInvestmentOverviewData.documentFileDetail
              "
              src="{{
                'data:' +
                  currentInvestmentOverviewData.documentFileDetail.contentType +
                  ';base64,' +
                  currentInvestmentOverviewData.documentFileDetail.fileData
              }}"
            />

            <nb-icon
              class="img"
              icon="image-outline"
              *ngIf="
                !currentInvestmentOverviewData.imageUrl &&
                (!currentInvestmentOverviewData || !currentInvestmentOverviewData.documentFileDetail)
              "
            >
            </nb-icon>
          </ng-container>

          <div
            class="invested"
            *ngIf="currentInvestmentOverviewData && currentInvestmentOverviewData.investedAmount > 0"
          >
            INVESTED -
            {{ currentInvestmentOverviewData.investedAmount | currency: "USD" : "symbol" : "1.0" }}
          </div>

          <div
            class="invested"
            *ngIf="
              !(currentInvestmentOverviewData && currentInvestmentOverviewData.investedAmount > 0) &&
              currentInvestmentOverviewData &&
              currentInvestmentOverviewData.applicationAmount > 0
            "
          >
            APPLICATION - {{ currentInvestmentOverviewData.applicationAmount | currency: "USD" : "symbol" : "1.0" }}
          </div>

          <!-- <div class="location">
                        <nb-icon icon="imageLocationIcon" pack="custom"></nb-icon> &nbsp;
                        {{currentInvestmentOverviewData.title}}
                    </div> -->
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="isMonthlyDistLoading" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Monthly Distributions</div>
            </div>
          </div>
        </div>

        <ng-container *ngIf="!monthlyDistributionData || monthlyDistributionData.length === 0">
          <div class="my-[15px]">
            <div class="no-data">
              <p class="no-data-text">No Monthly Distributions yet.</p>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="monthlyDistributionData && monthlyDistributionData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <!-- <p class="chart-data-title">{{getTotal(monthlyDistributionData)}}</p> -->
            </div>
          </div>

          <div class="my-[15px] flex flex-wrap -mx-2" style="height: 400px">
            <ngx-charts-bar-vertical
              [scheme]="colorScheme"
              [results]="monthlyDistributionData"
              [gradient]="false"
              [legend]="false"
              [showDataLabel]="false"
              [barPadding]="20"
              [yAxisTicks]="yAxisTicks"
              [noBarWhenZero]="true"
              [yAxisTickFormatting]="yAxisTickFormatting"
              [roundDomains]="true"
              [dataLabelFormatting]="yAxisTickFormatting"
              [showGridLines]="true"
              [xAxis]="true"
              [yAxis]="true"
              [legendTitle]="''"
            >
            </ngx-charts-bar-vertical>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Quarterly Updates</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <nb-card-body [nbSpinner]="!quarterlyUpdatesData" class="nb-scroll-body">
        <div>
          <ng-container *ngIf="quarterlyUpdatesData && quarterlyUpdatesData.length === 0">
            <div class="no-data">
              <p class="no-data-text">No Quarterly Updates yet.</p>
            </div>
          </ng-container>

          <div *ngFor="let logs of quarterlyUpdatesData">
            <div class="logs">
              <div class="display-flex" style="align-items: center">
                <nb-icon class="file-icon" icon="file-text"></nb-icon>
                <div class="text-blue-600 cursor-pointer file-name" (click)="downloadFile(logs)">
                  {{ logs.description }}
                </div>
              </div>
              <div>{{ logs.dateCreated | date: "dd/MM/YYYY" }}</div>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-8/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Transaction History</div>
            </div>
          </div>
        </div>
      </nb-card-header>
      <nb-card-body [nbSpinner]="!transactionHistoryData" class="nb-scroll-body">
        <div class="my-[15px]">
          <p-table
            #dt
            [filterDelay]="700"
            [value]="transactionHistoryData"
            [lazy]="true"
            [loading]="loading"
            [paginator]="false"
            [rows]="200"
            [totalRecords]="totalRecords"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
            [rowsPerPageOptions]="[10, 25, 50]"
            [scrollable]="true"
            scrollWidth="flex"
            scrollHeight="flex"
            [globalFilterFields]="['description', 'investment', 'dateCreated', 'fileSize']"
            sortField="lastLogin"
            [sortOrder]="-1"
          >
            <ng-template pTemplate="header">
              <tr>
                <th>
                  <div>
                    <div>Transaction Type</div>
                  </div>
                </th>

                <th>
                  <div>
                    <div>Date</div>
                  </div>
                </th>

                <th>
                  <div>
                    <div>Amount</div>
                  </div>
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-document>
              <tr>
                <td>{{ document.transactionType }}</td>
                <td>{{ document.paymentDate | date: "dd/MM/YYYY" }}</td>
                <td>
                  <span class="text-blue-600"> {{ document.amount | currency: "USD" : "symbol" : "1.0" }} </span>
                </td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage" let-columns>
              <tr>
                <td style="text-align: center; display: block" [attr.colspan]="3">No transactions yet.</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Fund Documents</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <nb-card-body [nbSpinner]="!fundDocumentsData" class="nb-scroll-body">
        <ng-container *ngIf="fundDocumentsData && fundDocumentsData.length === 0">
          <div class="no-data">
            <p class="no-data-text">No Fund Documents yet.</p>
          </div>
        </ng-container>

        <div>
          <div *ngFor="let logs of fundDocumentsData">
            <div class="logs">
              <div class="display-flex" style="align-items: center">
                <nb-icon class="file-icon" icon="file-text"></nb-icon>
                <div class="text-blue-600 cursor-pointer file-name" (click)="downloadFile(logs)">
                  {{ logs.description }}
                </div>
              </div>
              <div>{{ logs.dateCreated | date: "dd/MM/YYYY" }}</div>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
</div>
