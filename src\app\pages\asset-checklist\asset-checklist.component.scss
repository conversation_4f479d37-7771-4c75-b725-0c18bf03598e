@use "themes" as *;

:host ::ng-deep {
  .p-datatable-scrollable {
    height: calc(100vh - 300px) !important;
  }
}

:host {
  .title h5,
  .chart-title {
    text-decoration-line: underline;
    text-underline-offset: 10px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }

  .btn-success {
    background-color: #4db664 !important;
    border-color: #4db664 !important;
    border-radius: 0.25rem;
    border-style: none;
    color: rgba(255, 255, 255, 1) !important;
    min-width: 63px;
    min-height: 27px;
    font-family: "Roboto";
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
  }

  .btn-light {
    background-color: #e4e9f2 !important;
    border-color: #e4e9f2 !important;
    border-radius: 0.25rem;
    border-style: none;
    color: #222b45 !important;
    min-width: 63px;
    min-height: 27px;
    font-family: "Roboto";
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
  }
}

.chart-title {
  font-size: 18px;
  line-height: 30px;
}

.text-right {
  text-align: right;
}

.float-right {
  margin-left: 12px;
}
