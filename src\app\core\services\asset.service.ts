import { Injectable } from '@angular/core';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { AssetKeyDataResponse } from '@core/models/response/asset-key-data.response';
import { AssetKeyDataRequest } from '@core/models/request/asset-key-data.request';
import { BehaviorSubject, Observable } from 'rxjs';
import { TypeKey } from '@core/models/config';
import { AssetDropdownResponse } from '@core/models/response/asset-dropdown.response';
import { Filters } from './shared.service';
import { AssetsResponse } from '@core/models/response/assets.response';
import { AssetResponse } from '@core/models/response/asset.response';
import { SaveLenderDataRequest } from '@core/models/request/save-lender-data.request';
import { AssetLendersResponse } from '@core/models/response/asset-lenders.response';
import { FacilitiesFilterResponse } from '@core/models/response/facilities-filter.response';
import { AssetDetailsRequest } from '@core/models/request/asset-details.request';
import { AssetDetailsResponse } from '@core/models/response/asset-details.response';
import { ArchiveAssetRequest } from '@core/models/request/archive-asset-request';
import { GetLenderRequest } from '@core/models/request/get-lenders.request';
import { AssetGraphResponse } from '@core/models/response/asset-graph.response';
import { AssetRecentMessagesResponse } from '@core/models/response/asset-recent-messges.response';
import { LoanPortfolioBytypeResponse } from '@core/models/response/get-loan-portfolio-bytype.response';
import { LoanPortfolioBytype } from '@core/helpers';
import { GlobalChatRequest } from '@core/models/request/global-chat-request';
import { GlobalChatResponse } from '@core/models/response/global-chat.response';

@Injectable({
  providedIn: 'root',
})
export class AssetService {
  private checklistStructureSubject;
  constructor(private http: HttpClient) {
    this.checklistStructureSubject = new BehaviorSubject<ChecklistDefinition>(
      JSON.parse(localStorage.getItem('checklist') as string),
    ) as BehaviorSubject<ChecklistDefinition>;
  }

  public get getChecklistStructureValue(): Observable<ChecklistDefinition> {
    return this.checklistStructureSubject.asObservable();
  }

  setChecklistStructureValue(setValue: ChecklistDefinition): any {
    localStorage.setItem('checklist', JSON.stringify(setValue));
    this.checklistStructureSubject.next(setValue);
  }

  saveKeyData(assetKeyDataRequest: AssetKeyDataRequest): Observable<AssetKeyDataResponse> {
    return this.http.post<AssetKeyDataResponse>(`${environment.apiURL}/api/Asset/save-key-data`, assetKeyDataRequest);
  }

  getAssetDetails(assetKeyDataRequest: AssetDetailsRequest): Observable<AssetDetailsResponse> {
    return this.http.post<AssetDetailsResponse>(
      `${environment.apiURL}/api/Asset/get-asset-detail`,
      assetKeyDataRequest,
    );
  }

  getAssets(filters: Filters): Observable<AssetsResponse> {
    return this.http.post<AssetsResponse>(`${environment.apiURL}/api/Asset/get-assets`, filters);
  }

  getTypeBy(typeKey: TypeKey): Observable<AssetDropdownResponse> {
    return this.http.post<AssetDropdownResponse>(`${environment.apiURL}/api/Asset/get-type-by`, { typekey: typeKey });
  }

  getAsset(assetId: number): Observable<AssetResponse> {
    return this.http.get<AssetResponse>(`${environment.apiURL}/api/Asset/get-key-data/${assetId}`);
  }

  getAssetList(): Observable<any> {
    return this.http.get<any>(`${environment.apiURL}/api/Asset/get-asset-list`);
  }

  saveLenderData(saveLenderDataRequest: SaveLenderDataRequest): Observable<AssetKeyDataResponse> {
    return this.http.post<AssetKeyDataResponse>(
      `${environment.apiURL}/api/Asset/save-lender-data`,
      saveLenderDataRequest,
    );
  }

  // TODO : Make method typed
  getLender(lenderId: number, assetId: number): Observable<any> {
    return this.http.post<any>(`${environment.apiURL}/api/Asset/get-lender-data`, {
      lenderId: lenderId,
      assetId: assetId,
    });
  }

  getLenders(getLenderRequest: GetLenderRequest): Observable<AssetLendersResponse> {
    return this.http.post<AssetLendersResponse>(`${environment.apiURL}/api/Asset/get-lenders`, getLenderRequest);
  }

  getFacilities(contactOrgId?: number): Observable<FacilitiesFilterResponse> {
    if (contactOrgId) {
      return this.http.get<FacilitiesFilterResponse>(`${environment.apiURL}/api/Asset/get-facilities/${contactOrgId}`);
    }
    return this.http.get<FacilitiesFilterResponse>(`${environment.apiURL}/api/Asset/get-facilities`);
  }

  getDocuments(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Asset/get-documents`, filters);
  }

  archiveLender(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Asset/delete-lender`, filters);
  }

  chathistory(filters: any): any {
    return this.http.post(`${environment.apiURL}/api/Asset/chathistory`, filters);
  }

  archiveAsset(filters: ArchiveAssetRequest): any {
    return this.http.post(`${environment.apiURL}/api/Asset/archive-asset`, filters);
  }

  sendMessage(formData: any): any {
    const uploadURL = `${environment.apiURL}/api/Asset/sendmessage`;
    return this.http.post<any>(uploadURL, formData, {
      reportProgress: true,
      observe: 'events',
    });
  }

  getActiveLoanByType(): Observable<AssetGraphResponse> {
    return this.http.get<AssetGraphResponse>(`${environment.apiURL}/api/Asset/get-activeloan-bytype`);
  }

  getGeoLocationSummary(): Observable<AssetGraphResponse> {
    return this.http.get<AssetGraphResponse>(`${environment.apiURL}/api/Asset/get-geo-location-summary`);
  }

  getDrawnBalanceSummary(): Observable<AssetGraphResponse> {
    return this.http.get<AssetGraphResponse>(`${environment.apiURL}/api/Asset/get-drawnbalance-summary`);
  }

  getRecentMessages(assetId?: number): Observable<AssetRecentMessagesResponse> {
    return this.http.post<AssetRecentMessagesResponse>(`${environment.apiURL}/api/Asset/recent-messages`, { assetId });
  }

  getLoanPortfolioBytype(type: LoanPortfolioBytype): Observable<LoanPortfolioBytypeResponse> {
    return this.http.get<LoanPortfolioBytypeResponse>(
      `${environment.apiURL}/api/Asset/get-loan-portfolio-bytype/${type}`,
    );
  }

  getGlobalChatList(globalChatRequest: GlobalChatRequest): Observable<GlobalChatResponse> {
    return this.http.post<GlobalChatResponse>(`${environment.apiURL}/api/Asset/global-chat`, globalChatRequest);
  }

  getUpdateNotification(userId: number): any {
    return this.http.post(`${environment.apiURL}/api/Asset/update-notification`, { userId });
  }

  getNotificationCount(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Asset/notification-count`, filters);
  }

  getChecklists(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Asset/checklist`, filters);
  }

  getChecklistContent(checklistId: number): any {
    return this.http.post(`${environment.apiURL}/api/Asset/checklist-content`, {
      ChecklistId: checklistId,
    });
  }

  saveChecklist(checklistForm: any): any {
    return this.http.post(`${environment.apiURL}/api/Asset/save-checklist`, checklistForm);
  }

  saveChecklistContent(checklistContentsForm: any): any {
    return this.http.post(`${environment.apiURL}/api/Asset/save-checklist-contents`, checklistContentsForm);
  }

  archiveChecklist(checklistForm: any): any {
    return this.http.post(`${environment.apiURL}/api/Asset/save-checklist`, checklistForm);
  }

  // deleteChecklist(checklistId: number): any {
  //   return this.http.delete(`${environment.apiURL}/api/Asset/delete-checklist/${checklistId}`);
  // }

  getLenderOriginatorUsers(): Observable<any> {
    return this.http.get(`${environment.apiURL}/api/Asset/get-lender-originator-users`);
  }

  getAssetChecklistContent(checklistId: number, assetId: number, taskType: number): any {
    return this.http.post(`${environment.apiURL}/api/Asset/asset-checklist-content`, {
      ChecklistId: checklistId,
      AssetId: assetId,
      TaskType: taskType,
    });
  }

  saveAssetChecklistContent(checklistForm: any): any {
    return this.http.post(`${environment.apiURL}/api/Asset/save-asset-checklist-contents`, checklistForm);
  }

  getCovenantTasks(taskType: number, assetId?: number): any {
    return this.http.post(`${environment.apiURL}/api/Asset/covenant-task`, {
      AssetId: assetId,
      TaskType: taskType,
    });
  }

  getActivitiesTasks(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Asset/activities-task`, filters);
  }

  saveTask(taskForm: any): any {
    return this.http.post(`${environment.apiURL}/api/Asset/save-task`, taskForm);
  }

  saveNotesTask(noteForm: any, documents: any): any {
    const formData = new FormData();
    formData.append('id', noteForm.id as any);
    formData.append('taskType', noteForm.taskType as any);
    formData.append('taskTitle', noteForm.taskTitle as any);
    formData.append('dueDate', new Date(noteForm.dueDate).toISOString() as any);
    formData.append('assignedTo', noteForm.assignedTo as any);
    formData.append(
      'taskContent',
      noteForm.taskContent && noteForm.taskContent.length > 0 ? noteForm.taskContent : ('' as any),
    );
    formData.append('assetId', noteForm.assetId as any);
    formData.append('isArchived', noteForm.isArchived || (0 as any));
    if (noteForm.reviewerId) {
      formData.append('reviewerId', noteForm.reviewerId as any);
    }
    formData.append('reviewerDate', '' as any);
    if (noteForm.loanManagerId) {
      formData.append('loanManagerId', noteForm.loanManagerId as any);
    }
    formData.append('loanManagerDate', '' as any);
    if (noteForm.completedBy) {
      formData.append('completedBy', noteForm.completedBy as any);
    }
    formData.append(
      'completedByDate',
      noteForm.completedBy && noteForm.completedByDate ? new Date(noteForm.completedByDate).toISOString() : ('' as any),
    );
    if (documents.deletedDocuments && documents.deletedDocuments.length > 0) {
      for (const item of documents.deletedDocuments) {
        formData.append('deleteDocuments', item as any);
      }
    }
    if (documents.documents && documents.documents.length > 0) {
      for (const item of documents.documents) {
        item.progress = 0;
        formData.append('documents', item, item.name);
      }
    }

    return this.http.post(`${environment.apiURL}/api/Asset/save-notes-task`, formData, {
      observe: 'events',
      reportProgress: true,
    });
  }

  saveAssetNote(noteForm: any): any {
    const formData = new FormData();
    formData.append('noteContent', noteForm.noteContent as any);
    formData.append('taskId', noteForm.taskId as any);
    formData.append('taskType', noteForm.taskType as any);
    formData.append('userId', noteForm.userId as any);
    if (noteForm.id != undefined) {
      formData.append('id', noteForm.id as any);
    }
    if (noteForm.deletedDocuments && noteForm.deletedDocuments.length > 0) {
      for (const item of noteForm.deletedDocuments) {
        formData.append('deleteDocuments', item as any);
      }
    }
    if (noteForm.documents && noteForm.documents.length > 0) {
      for (const item of noteForm.documents) {
        item.progress = 0;
        formData.append('documents', item, item.name);
      }
    }
    return this.http.post(`${environment.apiURL}/api/Asset/save-asset-notes`, formData, {
      observe: 'events',
      reportProgress: true,
    });
  }
}

export interface ChecklistDefinition {
  checklistName?: string;
  checklistId?: number;
  checklistStatus?: number;
  checklistStructure: ChecklistStructureDefinition[];
}
export interface ChecklistStructureDefinition extends ChecklistDefinition {
  id?: number;
  type?: string;
  content?: string;
  title?: string;
  order?: number;
  edit?: boolean;
  active?: number;
}
