<div
  class="text"
  [style.background]="taskInformation ? taskInformation.background : ''"
  [style.border]="taskInformation ? (taskInformation.overdue ? '1px solid #FC3D3D' : '') : ''"
>
  <div class="type-icon" *ngIf="metaData.isInternalNote" style="position: absolute; right: 30px; top: 13px">
    <div>
      <nb-icon
        style="height: 36px; width: 36px"
        [icon]="taskInformation ? 'addTaskIcon' : 'assetEditIcon'"
        pack="custom"
      >
      </nb-icon>
    </div>
  </div>
  <div style="flex: 1">
    <div *ngIf="!noteType" style="font-size: 14px">
      <b style="font-size: 16px"> {{ sender }} </b>{{ date | date: dateFormat }}
      <span *ngIf="metaData.facilityName">
        | <span class="text-blue-600"> {{ metaData.facilityName }} </span>
      </span>
    </div>
    <div *ngIf="noteType" style="font-size: 14px">
      <button
        nbButton
        ghost
        shape="round"
        status="default"
        size="large"
        class="button-icon"
        [disabled]="taskInformation && taskInformation.completedBy"
        (click)="completeTask()"
      >
        <nb-icon style="margin-top: -5px; height: 26px; width: 26px" [icon]="taskInformation.icon"> </nb-icon>
      </button>
      <b style="font-size: 16px">{{ messageTitle }}</b>
      <span
        *ngIf="
          isAdmin || userId === taskInformation.assignedTo || userId === taskInformation.createdBy;
          else other_content
        "
        style="right: 120px; top: 27px; position: absolute"
      >
        {{ date | date: dateFormat }}
      </span>
      <ng-template #other_content
        ><span style="right: 75px; top: 27px; position: absolute">
          {{ date | date: dateFormat }}
        </span></ng-template
      >

      <button
        style="position: absolute; right: 70px; top: 12px"
        *ngIf="isAdmin || userId === taskInformation.assignedTo || userId === taskInformation.createdBy"
        nbButton
        ghost
        shape="round"
        status="default"
        class="button-icon"
        (click)="editTask()"
        nbTooltip="Edit Task"
        nbTooltipStatus="control"
        nbTooltipPlacement="bottom"
      >
        <nb-icon icon="covenantEditPencilIcon" pack="custom"></nb-icon>
      </button>
      <span *ngIf="metaData.facilityName">
        | <span class="text-blue-600"> {{ metaData.facilityName }} </span>
      </span>
    </div>
    <div class="chat-message" *ngIf="!message && metaData && metaData.attachments.length > 0 && !taskInformation">
      <b>{{ metaData.userName }}</b
      >Attached
      <div class="files-list">
        <div class="single-file" *ngFor="let file of metaData.attachments">
          <div class="info w-full px-2" (click)="downloadFile(file)">
            <div class="attachment">
              <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
            </div>
            <div class="name">
              {{ file && file.fileName ? file.fileName : file && file.documentName ? file.documentName : "" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="chat-message"
      style="margin-left: 50px"
      *ngIf="!message && metaData && metaData.attachments.length > 0 && taskInformation"
    >
      <div class="files-list">
        <div class="single-file" *ngFor="let file of metaData.attachments">
          <div class="info w-full px-2" (click)="downloadFile(file)">
            <div class="attachment">
              <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
            </div>
            <div class="name">
              {{ file && file.fileName ? file.fileName : file && file.documentName ? file.documentName : "" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="chat-message" *ngIf="message && metaData && metaData.attachments.length === 0 && !noteType">
      <div [innerHTML]="sanitizer.bypassSecurityTrustHtml(message)"></div>
    </div>

    <div
      class="chat-message"
      style="margin-left: 50px"
      *ngIf="message && metaData && metaData.attachments.length === 0 && noteType"
    >
      <div [innerHTML]="sanitizer.bypassSecurityTrustHtml(message)"></div>
    </div>
    <div
      class="chat-message"
      [style.marginLeft]="noteType ? '50px' : ''"
      *ngIf="message && metaData && metaData.attachments.length > 0"
    >
      <div [innerHTML]="sanitizer.bypassSecurityTrustHtml(message)"></div>
      <div class="files-list">
        <div class="single-file" *ngFor="let file of metaData.attachments">
          <div class="info w-full px-2" (click)="downloadFile(file)">
            <div class="attachment">
              <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
            </div>
            <div class="name">
              {{ file && file.fileName ? file.fileName : file && file.documentName ? file.documentName : "" }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  *ngIf="reply"
  [ngClass]="
    taskInformation
      ? taskInformation.overdue
        ? 'red-triangle'
        : taskInformation.completedBy
          ? 'grey-triangle'
          : 'triangle'
      : 'triangle'
  "
></div>
<div
  *ngIf="reply"
  [ngClass]="
    taskInformation
      ? taskInformation.overdue
        ? 'red-triangle-over'
        : taskInformation.completedBy
          ? 'grey-triangle-over'
          : 'triangle-over'
      : 'triangle-over'
  "
></div>

<div
  *ngIf="!reply"
  [ngClass]="
    taskInformation
      ? taskInformation.overdue
        ? 'red-triangle-noreply'
        : taskInformation.completedBy
          ? 'grey-triangle-noreply'
          : 'triangle-noreply'
      : 'triangle-noreply'
  "
></div>
<div
  *ngIf="!reply"
  [ngClass]="
    taskInformation
      ? taskInformation.overdue
        ? 'red-triangle-over-noreply'
        : taskInformation.completedBy
          ? 'grey-triangle-over-noreply'
          : 'triangle-over-noreply'
      : 'triangle-over-noreply'
  "
></div>
