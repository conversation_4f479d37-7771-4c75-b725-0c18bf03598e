<form *ngIf="!applied" class="w-full px-2" [formGroup]="financialsForm" (ngSubmit)="onSubmit()">
  <div class="w-full px-2 my-[15px]">
    <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Investment Amount </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <nb-form-field>
        <!-- <span nbPrefix class="flagIcon"> $ </span> -->
        <input
          nbInput
          fullWidth
          prefix="$"
          mask="separator"
          (input)="onInputValue($event)"
          thousandSeparator=","
          placeholder=""
          shape="semi-round"
          type="text"
          fieldSize="large"
          formControlName="investmentAmount"
          [maxlength]="12"
          status="{{ submitted && f.investmentAmount.errors ? 'danger' : 'basic' }}"
        />
      </nb-form-field>

      <div *ngIf="submitted && f.investmentAmount.errors" class="invalid-feedback">
        <div *ngIf="f.investmentAmount.errors.required">Investment Amount is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Units </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="separator"
        placeholder=""
        thousandSeparator=","
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="investmentUnit"
        [maxlength]="10"
        readonly
        status="{{ submitted && f.investmentUnit.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.investmentUnit.errors" class="invalid-feedback">
        <div *ngIf="f.investmentUnit.errors.required">Units is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Bank Account Name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="bankAccountName"
        status="{{ submitted && f.bankAccountName.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.bankAccountName.errors" class="invalid-feedback">
        <div *ngIf="f.bankAccountName.errors.required">Bank Account Name is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Bank </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="bank"
        status="{{ submitted && f.bank.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.bank.errors" class="invalid-feedback">
        <div *ngIf="f.bank.errors.required">Bank is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> BSB </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="0{6}"
        placeholder=""
        maxlength="6"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="bsb"
        status="{{ submitted && f.bsb.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.bsb.errors" class="invalid-feedback">
        <div *ngIf="f.bsb.errors.required">BSB is required.</div>
        <div *ngIf="f.bsb.errors.mask">Invalid BSB format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Account Number </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        mask="0*"
        maxlength="20"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="accountNo"
        status="{{ submitted && f.accountNo.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.accountNo.errors" class="invalid-feedback">
        <div *ngIf="f.accountNo.errors.required">Account Number is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label
        ><strong> Tax File Number </strong>
        <strong *ngIf="validator('taxFileNo')" class="text-lime required"> &nbsp; * </strong></label
      >
      <input
        nbInput
        fullWidth
        shape="semi-round"
        mask="0*"
        maxlength="30"
        type="text"
        fieldSize="large"
        formControlName="taxFileNo"
        status="{{ submitted && f.taxFileNo.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.taxFileNo.errors" class="invalid-feedback">
        <div *ngIf="f.taxFileNo.errors.required">Tax File Number is required.</div>
        <!-- <div *ngIf="f.taxFileNo.errors.mask">Invalid BSB format.</div> -->
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label
        ><strong> Tax File Number Exemption Code </strong>
        <strong *ngIf="validator('taxFileNoExcemptionCode')" class="text-lime required"> &nbsp; * </strong></label
      >
      <input
        nbInput
        fullWidth
        placeholder=""
        mask="A*"
        maxlength="30"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="taxFileNoExcemptionCode"
        status="{{ submitted && f.taxFileNoExcemptionCode.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.taxFileNoExcemptionCode.errors" class="invalid-feedback">
        <div *ngIf="f.taxFileNoExcemptionCode.errors.required">Tax File Number Exemption Code is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <span class="text-mute"> Before you invest in this loan you must read the attached investment documents. </span>

      <div class="files-list">
        <div class="single-file" *ngFor="let file of documents; let i = index">
          <div class="info">
            <nb-icon class="text-mute" icon="file-text"> </nb-icon>
            <div
              class="name"
              nbTooltipStatus="primary"
              nbTooltip="Click to Download {{ file?.fileName }}"
              class="clickable"
              (click)="downloadFile(file)"
            >
              {{ file?.fileName }}
            </div>
            <!-- <div>
                            <nb-user nbTooltip="{{file.userName}}" nbTooltipStatus="control" nbTooltipPlacement="bottom"
                                size="small" [name]="file.userName" onlyPicture="true">
                            </nb-user>
                        </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <label><strong> Acknowledgements. </strong></label>
    <br />
  </div>
  <div class="flex flex-wrap -mx-2">
    <p class="p1">When you apply to invest, you (the applicant) are telling us:</p>
    <ul class="ul1">
      <li class="li1">
        I/we have downloaded, read and understood the current Information Memorandum provided in this application, for
        the Offer in which I/we are investing;
      </li>
      <li class="li1">
        I/we have received and accepted the offer to invest in Australia (or a jurisdiction in which it is lawful for
        me/us to receive and accept the offer) and declare that all information in my/our application is true and
        correct;
      </li>
      <li class="li1">I/we agree to be bound by the terms and conditions of the Information Memorandum;</li>
      <li class="li1">
        I/we acknowledge that none of the Issuer, the Fund Manager, and each of their related bodies corporate,
        directors and other officers, shareholders, servants, employees, agents and permitted delegates (Indemnified
        Parties) have guaranteed or made any representation as to the performance or success of the Fund, or the
        repayment of capital from the Fund. Investments in the Fund are subject to various risks, including delays in
        repayment and loss of income or principal invested. Investments in the Fund are not deposits with or other
        liabilities of the Issuer, the Fund Manager, or any of its related bodies corporate or associates;
      </li>
      <li class="li1">
        I/we acknowledge the Issuer reserves the right to reject any application or scale back an application in its
        absolute discretion;
      </li>
      <li class="li1">
        I/we acknowledge that I/we have had the opportunity to seek independent professional advice regarding the legal,
        taxation and financial implications of investing in the Fund;
      </li>
      <li class="li1">
        If this Application Form is signed under Power of Attorney, each Attorney declares he/she has not received
        notice of revocation of that power (a certified copy of the Power of Attorney should be submitted with this
        Application Form);
      </li>
      <li class="li1">
        I/We acknowledge that we have not been given a Product Disclosure Statement and that none of the Indemnified
        Parties have provided me/us with financial product advice regarding an investment that has taken into account
        my/our objectives, financial situation or needs;
      </li>
      <li class="li1">
        I/We acknowledge that we have not been given any other document that would be required to be given to the client
        under Chapter 7 of the Corporations Act if the product or service were provided to me/us as a retail client;
      </li>
      <li class="li1">
        I/we have all requisite power and authority to execute and perform the obligations under the Information
        Memorandum and this Application Form;
      </li>
      <li class="li1">
        I/we acknowledge that application monies will be held in a trust account until invested in the Fund or returned
        to me/ us. Interest will not be paid to applicants in respect of their application monies regardless of whether
        their monies are returned;
      </li>
      <li class="li1">
        I/we acknowledge if an application is accepted in error, the Fund may treat the Application as not having been
        made or accepted and make appropriate consequential entries in the records of the Fund, including the Register.
        All money paid to the Fund in respect of an application which is not accepted will be as soon as practicable
        returned in full to the applicant. The Indemnified Parties are not liable to an applicant for any loss suffered
        as a result of the application being accepted in error.
      </li>
      <li class="li1">
        I/we have read the information on privacy and personal information contained in the Information Memorandum and
        consent to my/our personal information being used and disclosed as set out in the Information Memorandum;
      </li>
      <li class="li1">
        I/we acknowledge that the Issuer may deliver and make reports, statements and other communications available in
        electronic form, such as e-mail or by posting on a website;
      </li>
      <li class="li1">
        I/we indemnify the Indemnified Parties and to hold each of them harmless from and against any loss, damage,
        liability, cost or expense, including reasonable legal fees (collectively, a Loss) due to or arising out of a
        breach of representation, warranty, covenant or agreement by me/us contained in any document provided by me/us
        to the Issuer, its agents or other parties in connection with my/our investment in the Fund. The indemnification
        obligations provided herein survive the execution and delivery of this Application Form, any investigation at
        any time made by the Issuer and the issue and/or sale of the investment;
      </li>
      <li class="li2">
        To the extent permitted by law, I/we release each of the Indemnified Parties from all claims, actions, suits or
        demands whatsoever and howsoever arising that I/we may have against any Indemnified Party in connection with the
        Information Memorandum or my/our investment.
      </li>
    </ul>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <nb-checkbox formControlName="investorConsent" (checkedChange)="toggle($event)" required>
        <span class="text-mute">
          I have read and understood the attached documents. I have read and accept the acknowledgements for this
          Investment Application.</span
        >
      </nb-checkbox>
    </div>
  </div>

  <div class="w-full px-2 my-[15px]">
    <button
      class="float-right"
      [disabled]="(!financialsForm.valid && !isDocumentReaded) || submitted"
      [nbSpinner]="loading"
      nbButton
      status="primary"
      style="min-width: 135px"
    >
      APPLY
    </button>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>

<div *ngIf="applied" class="flex flex-wrap -mx-2 my-[15px]">
  <div class="w-full px-2">
    <div class="title">
      <h5>Application Received</h5>
    </div>

    <!-- <p>
            To complete the transaction please deposit your investment of {{financialsForm.value.investmentAmount |
            currency:'USD':'symbol':'2.0'}} into
            the Sydney Wyde Account.
        </p> -->

    <p style="margin-left: 12px">
      Thank you for your application to invest. The Sydney Wyde Support Team will review your application and advise
      next steps within 2 business hours.
    </p>

    <!-- <br>

        <p class="text-mute"> Account Name : Sydney Wyde Pty Ltd. </p>
        <p class="text-mute"> Bank : Commonwealth Bank </p>
        <p class="text-mute"> BSB : 032-516 </p>
        <p class="text-mute"> Account No. : 897654 </p>
        <p class="text-mute"> Reference - {{entityName}} </p> -->
  </div>
</div>
