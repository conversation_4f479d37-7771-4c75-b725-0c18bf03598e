<div class="search-container">
  <!-- Search Input with Icons -->
  <div class="search-input-wrapper">
    <input
      nbInput
      fullWidth
      shape="semi-round"
      fieldSize="large"
      [placeholder]="placeholder"
      [disabled]="disabled"
      [formControl]="searchControl"
      class="search-input"
      autocomplete="off"
    />

    <!-- Search Icon (shown when input is empty) -->
    <button
      *ngIf="showSearchIcon"
      nbButton
      ghost
      size="small"
      class="search-icon"
      tabindex="-1"
      (click)="onSearchIconClick()"
      title="Search"
    >
      <nb-icon icon="search-outline" pack="eva"></nb-icon>
    </button>

    <!-- Clear Icon (shown when input has value or item is selected) -->
    <button
      *ngIf="showClearIcon"
      nbButton
      ghost
      size="small"
      class="clear-icon"
      (click)="onClearSearch()"
      title="Clear search"
    >
      <nb-icon icon="close-outline" pack="eva"></nb-icon>
    </button>

    <!-- Loading Spinner -->
    <div *ngIf="isLoading" class="loading-spinner">
      <nb-icon icon="loader-outline" pack="eva" [options]="{ animation: { type: 'pulse' } }"></nb-icon>
    </div>
  </div>

  <!-- Dropdown Results -->
  <div *ngIf="showDropdown" class="search-dropdown">
    <nb-list>
      <nb-list-item
        *ngFor="let item of searchResults; trackBy: trackByFn"
        (click)="onItemSelect(item)"
        class="search-result-item"
      >
        <div class="result-content">
          <div class="result-title">{{ item.title }}</div>
          <div class="result-details">
            <span *ngIf="item.borrower" class="result-borrower">{{ item.borrower }}</span>
            <span *ngIf="item.investmentType" class="result-type">{{ item.investmentType }}</span>
            <span *ngIf="item.assetType" class="result-asset">{{ item.assetType }}</span>
            <span *ngIf="item.totalOpportunity" class="result-amount">
              {{ item.totalOpportunity | currency: "USD" : "symbol" : "1.0-0" }}
            </span>
          </div>
        </div>
      </nb-list-item>
    </nb-list>

    <!-- No Results Message -->
    <div *ngIf="hasSearched && searchResults.length === 0 && !isLoading" class="no-results">
      <nb-icon icon="search-outline" pack="eva"></nb-icon>
      <span>No investments found</span>
    </div>
  </div>
</div>
