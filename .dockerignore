# Docker ignore file for Angular project

# Version control
.git
.gitignore

# Node.js dependencies
node_modules
npm-debug.log
yarn-error.log
.npm
*.log

# Build artifacts and temporary files
/dist
/tmp
/out-tsc
/.angular
/.cache
/.tmp

# Test files
/e2e
/coverage
.nyc_output
.nyctrc
.coverage
.coverage.*
src/**/*.spec.ts
src/**/*.test.ts

# IDE and editor files
/.idea
/.vscode
*.swp
*.swo
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# OS files
.DS_Store
Thumbs.db

# Documentation and environment files
*.md
!README.md
.env*

# Husky git hooks
/.husky
