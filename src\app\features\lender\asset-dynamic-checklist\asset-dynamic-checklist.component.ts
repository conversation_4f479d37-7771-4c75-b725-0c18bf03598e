import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ApprovalChecklistRow,
  ChecklistTableHeader,
  ChecklistTableItem,
  Originator,
} from '@core/models/asset/asset-checklist';
import { AssetTaskType } from '@core/models/config';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbIconModule,
  NbInputModule,
  NbListModule,
  NbPopoverModule,
  NbSpinnerModule,
  NbToastrService,
  NbTooltipModule,
  NbUserModule,
} from '@nebular/theme';
import { TableModule } from 'primeng/table';
import { AssetNotesComponent } from '../asset-notes/asset-notes.component';

@Component({
  selector: 'app-asset-dynamic-checklist',
  templateUrl: './asset-dynamic-checklist.component.html',
  styleUrls: ['./asset-dynamic-checklist.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbUserModule,
    NbInputModule,
    NbTooltipModule,
    NbPopoverModule,
    NbListModule,
    NbSpinnerModule,
    NbButtonModule,
    TableModule,
  ],
})
export class AssetDynamicChecklistComponent implements OnInit {
  constructor(
    public sanitizer: DomSanitizer,
    private sharedService: SharedService,
    private assetService: AssetService,
    private dialogService: NbDialogService,
    private documentService: DocumentService,
    private toast: NbToastrService,
  ) { }

  @Input() checkListId!: number;
  @Input() tabId!: number;
  checkListContent: any = [];
  originalChecklistContent: any = [];
  @Input() assetKeyDataId!: number;
  userId: any;
  originatorUsers: any;
  allUploadedDocuments: any = [];
  allNotes: any = [];
  tableLoading = false;
  loading = false;
  updating = false;

  @Output() unsavedItemsEvent = new EventEmitter<number>();
  unsavedItems = false;
  resetTaskObject: any = {};

  async ngOnInit(): Promise<any> {
    if (this.sharedService.isAdmin() || this.sharedService.isOriginatorManager()) {
      this.userId = this.sharedService.getUserIdValue.userId;
      this.tabId = +this.tabId + 1;
      await this.getCheckListContent();
    }
  }

  calculateNotes() {
    const noteObjects: any = {};
    this.checkListContent.forEach((el: any) => {
      if (el.checklist_structure) {
        el.checklist_structure.forEach((et: any) => {
          noteObjects[et.id] = [];
          if (et.notes) {
            et.notes.forEach((note: any) => {
              const loanManagerObj = this.originatorUsers.find((i: any) => i.userId == note.userId);
              note.userName = loanManagerObj ? loanManagerObj.contactName : 'No User';
            });
            noteObjects[et.id] = et.notes;
          }
        });
      }
    });
    this.allNotes = noteObjects;
  }

  calculateDocuments() {
    const documentObjects: any = {};
    this.checkListContent.forEach((el: any) => {
      if (el.checklist_structure) {
        el.checklist_structure.forEach((et: any) => {
          documentObjects[et.id] = [];
          if (et.notes) {
            et.notes.forEach((ep: any) => {
              if (ep.noteDocuments) {
                documentObjects[et.id] = documentObjects[et.id].concat(ep.noteDocuments);
              }
            });
          }
        });
      }
    });
    this.allUploadedDocuments = documentObjects;
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isOriginatorManager(): boolean {
    return this.sharedService.isOriginatorManager();
  }

  async getCheckListContent() {
    try {
      this.tableLoading = true;

      const originatorUsersResponse = await this.assetService.getLenderOriginatorUsers().toPromise();
      if (!originatorUsersResponse.success) {
        throw new Error('Failed to fetch originator users');
      }
      this.originatorUsers = originatorUsersResponse.payload.userResult.filter(
        (user: any) => user.roleId === 4 || user.roleId == 1,
      );

      const assetChecklistResponse = await this.assetService
        .getAssetChecklistContent(this.checkListId, this.assetKeyDataId, AssetTaskType.Checklist)
        .toPromise();
      let checklist: any[] = [];

      if (assetChecklistResponse.success && assetChecklistResponse.payload.length) {
        checklist = this.structureNewChecklist(assetChecklistResponse.payload, this.originatorUsers);
      } else {
        const checklistTemplateResponse = await this.assetService.getChecklistContent(this.checkListId).toPromise();
        if (checklistTemplateResponse.success && checklistTemplateResponse.payload.length) {
          checklist = this.structureChecklist(checklistTemplateResponse.payload);
        } else {
          this.toast.danger('No checklist template found', 'Oops!');
        }
      }

      this.checkListContent = checklist;
      this.originalChecklistContent = [...checklist];
      this.calculateDocuments();
      this.calculateNotes();
    } catch (error) {
      this.toast.danger('Something went wrong', 'Oops!');
    } finally {
      this.tableLoading = false;
    }
  }

  structureNewChecklist(
    approvalChecklist: ApprovalChecklistRow[],
    originatorList: Originator[],
  ): ChecklistTableHeader[] {
    approvalChecklist.sort((a: any, b: any) => a.contentOrder - b.contentOrder);

    const tableStructure: ChecklistTableHeader[] = [];
    let currentHeader: ChecklistTableHeader | null = null;
    for (const row of approvalChecklist) {
      if (row.contentType === 'header') {
        currentHeader = {
          taskId: 0,
          header: row.contentTitle,
          checklist_structure: [],
          assetId: this.assetKeyDataId,
          ...row,
        };
        tableStructure.push(currentHeader);
      } else if (currentHeader) {
        const { loanManagerId, reviewerId, notApplicableId } = row;

        [loanManagerId, reviewerId, notApplicableId].forEach((id, index) => {
          if (id) {
            const originatorObj = originatorList.find((originator) => originator.userId === id);
            (row as Record<string, any>)[['loanManagerName', 'reviewerName', 'notApplicableName'][index]] =
              originatorObj?.contactName ?? 'No User';
          }
        });

        const { contentHtml, ...rest } = row;
        const checklistItem: ChecklistTableItem = {
          taskId: 0,
          contentHtml,
          checklistId: this.checkListId,
          assetId: this.assetKeyDataId,
          ...rest,
        };

        currentHeader.checklist_structure.push(checklistItem);
      }
    }

    return tableStructure;
  }

  structureChecklist(unformattedChecklist: any) {
    const tableStructure: any[] = [];
    let headerIndex = -1;

    unformattedChecklist.forEach((row: any) => {
      const { id, ...rest } = row;
      const newId = id;
      if (row.contentType === 'header') {
        tableStructure.push({
          newId,
          taskId: 0,
          header: row.contentTitle,
          checklist_structure: [],
          assetId: this.assetKeyDataId,
          ...rest,
        });
        headerIndex++;
      } else {
        tableStructure[headerIndex].checklist_structure.push({
          newId,
          taskId: 0,
          checklistId: this.checkListId,
          assetId: this.assetKeyDataId,
          ...rest,
        });
      }
    });

    return tableStructure;
  }

  editNote(data: any, inputNote: any) {
    let userNotes = null;
    let userIndex: string | number | null = null;
    let editMode = true;
    const notes = data['notes'];
    if (inputNote) {
      const note = notes.find((et: any) => et.id == inputNote.id);
      userNotes = note;
      if (note.userId == this.userId) {
        userIndex = notes.findIndex((et: any) => et.userId == this.userId);
        editMode = true;
      } else {
        editMode = false;
      }
    } else if (notes && notes.find((et: any) => et.userId == this.userId)) {
      const note = notes.find((et: any) => et.userId == this.userId);
      userIndex = notes.findIndex((et: any) => et.userId == this.userId);
      userNotes = note;
    }
    this.dialogService
      .open(AssetNotesComponent, {
        context: {
          editorContent: userNotes ? userNotes['noteContent'] : null,
          uploadedDocuments: userNotes ? userNotes['noteDocuments'] || [] : [],
          pendingDocuments: userNotes ? userNotes['documents'] || [] : [],
          deleteDocuments: userNotes ? userNotes['deletedDocuments'] || [] : [],
          taskId: data.id || data.newId,
          originalContent: this.originalChecklistContent,
          userId: this.userId,
          noteId: userNotes ? userNotes['id'] : null,
          taskType: AssetTaskType.Checklist,
          editMode: editMode,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          if (res.updated) {
            this.getCheckListContent();
          }
        }
      });
  }

  toggleTaskCompletion(type: string, data: any) {
    if (this.isOriginatorManager()) {
      const isReviewer = type === 'reviewer' && [data['loanManagerId'], data['notApplicableId']].includes(this.userId);
      const isLoanManager =
        type === 'loanManager' && [data['reviewerId'], data['notApplicableId']].includes(this.userId);
      const isNotApplicable =
        type === 'notApplicable' && [data['reviewerId'], data['loanManagerId']].includes(this.userId);

      if (isReviewer || isLoanManager || isNotApplicable) {
        return;
      }

      data[type + 'Id'] = this.userId;
      const matched = this.originatorUsers.find((i: any) => i.userId === data[type + 'Id']);
      data[type + 'Name'] = matched?.contactName ?? '';
      data[type + 'Date'] = new Date(Date.now());
      data[type + 'Edited'] = true;
      this.unsavedItemsEvent.emit(this.tabId);
      this.unsavedItems = true;
    } else if (this.isAdmin() && this.resetTaskObject[type + data.id]) {
      const resetData = this.resetTaskObject[type + data.id];
      data[type + 'Date'] = resetData[type + 'Date'];
      data[type + 'Id'] = resetData[type + 'Id'];
      data[type + 'Name'] = resetData[type + 'Name'];
      this.resetTaskObject[type + data.id] = null;
    }
  }

  resetTaskCompletion(type: string, data: any) {
    if (!this.isAdmin() && !data[type + 'Edited']) {
      return;
    }

    const resetTaskObject = {
      [type + 'Date']: data[type + 'Date'],
      [type + 'Id']: data[type + 'Id'],
      [type + 'Name']: data[type + 'Name'],
      [type + 'Edited']: data[type + 'Edited'],
    };
    data[type + 'Date'] = null;
    data[type + 'Id'] = null;
    data[type + 'Name'] = null;
    data[type + 'Edited'] = false;
    this.resetTaskObject[type + data['id']] = resetTaskObject;
    this.unsavedItemsEvent.emit(this.tabId);
    this.unsavedItems = true;
  }

  updateChecklist() {
    if (this.updating) {
      return;
    } else {
      this.updating = true;
      this.loading = true;
      const checklistContents: any[] = [];
      this.checkListContent.forEach((row: any) => {
        checklistContents.push(row);
        checklistContents.push(...row.checklist_structure);
      });
      this.assetService.saveAssetChecklistContent(checklistContents).subscribe(
        (res: any) => {
          if (res.success) {
            this.unsavedItemsEvent.emit(0);
            this.unsavedItems = false;
            this.toast.success('Checklist Updated', 'Success!');
            this.getCheckListContent();
            this.updating = false;
            this.loading = false;
          } else if (res.error) {
            this.loading = false;
            this.updating = false;
            this.toast.danger(res.error.message, 'Update Error!');
          }
        },
        (err: any) => this.toast.success(err.error.message, 'Checklist Failed!'),
      );
    }
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }
}
