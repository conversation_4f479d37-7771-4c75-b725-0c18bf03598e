<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Investor Management</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        fullWidth
        placeholder="Status"
        name="statuses"
        id="value"
        (selectedChange)="dt.filter($event, 'statusId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let source of statusData" [value]="source.id">
          {{ source.name }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <nb-select
        fullWidth
        placeholder="Entity Type"
        id="entityType"
        (selectedChange)="dt.filter($event, 'entityTypeId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let entityType of entityTypeData" [value]="entityType.id" [disabled]="entityType.disabled">
          {{ entityType.name }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>
  </div>
  <div class="lg:w-3/12 px-2 my-[15px]">
    <button *ngIf="isAdmin()" class="float-right" nbButton status="primary" (click)="createInvestor()">
      <nb-icon icon="plus"></nb-icon>
      ADD NEW INVESTOR
    </button>
  </div>
</div>

<p-skeleton *ngIf="!users"></p-skeleton>

<div *ngIf="users">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="users"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="true"
        [rows]="10"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="['investorId', 'firstName', 'lastName', 'entity', 'orgName', 'roleName']"
        sortField="dateCreated"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 80px; max-width: 80px" [pSortableColumn]="'investorId'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'investorId'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'entityName'">
              <div>
                <div>Entity Name</div>
                <p-sortIcon [field]="'entityName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'firstName'">
              <div>
                <div>First Name</div>
                <p-sortIcon [field]="'firstName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'lastName'">
              <div>
                <div>Last Name</div>
                <p-sortIcon [field]="'lastName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 130px" [pSortableColumn]="'mobile'">
              <div>
                <div>Mobile No.</div>
                <p-sortIcon [field]="'mobile'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'dateCreated'">
              <div>
                <div>Date Created</div>
                <p-sortIcon [field]="'dateCreated'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'entityType'">
              <div>
                <div>Entity Type</div>
                <p-sortIcon [field]="'entityType'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'lastLogin'">
              <div>
                <div>Last Login</div>
                <p-sortIcon [field]="'lastLogin'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'statusName'">
              <div>
                <div>Status</div>
                <p-sortIcon [field]="'statusName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 200px">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-user>
          <tr>
            <td style="min-width: 80px; max-width: 80px">{{ user.investorId }}</td>
            <td style="min-width: 125px">{{ user.entityName }}</td>
            <td style="min-width: 125px">{{ user.firstName }}</td>
            <td style="min-width: 125px">{{ user.lastName }}</td>
            <td style="min-width: 130px">{{ user.mobile }}</td>
            <td style="min-width: 125px">{{ user.dateCreated | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 125px">{{ user.entityType }}</td>
            <td style="min-width: 125px">{{ timeAgo(user.lastLogin) }}</td>
            <td style="min-width: 150px">
              <span class="user-status" [ngClass]="user.statusName"> {{ user.statusName }} </span>
            </td>
            <td style="min-width: 200px">
              <ng-container *ngIf="isAdmin()">
                <button
                  nbButton
                  ghost
                  shape="round"
                  status="default"
                  class="button-icon"
                  (click)="editInvestor(user)"
                  nbTooltip="Edit"
                  nbTooltipStatus="control"
                  nbTooltipPlacement="bottom"
                >
                  <nb-icon icon="editIcon" pack="custom"></nb-icon>
                </button>

                <ng-container *ngIf="enableIcons(user)">
                  <button
                    *ngIf="user.showLock"
                    status="default"
                    nbButton
                    ghost
                    class="button-icon"
                    shape="round"
                    (click)="confirmDisable(user)"
                    nbTooltip="Deactivate"
                    nbTooltipStatus="control"
                    nbTooltipPlacement="bottom"
                  >
                    <nb-icon icon="lockIcon" pack="custom"></nb-icon>
                  </button>

                  <button
                    *ngIf="!user.showLock"
                    status="default"
                    nbButton
                    ghost
                    class="button-icon"
                    shape="round"
                    (click)="activeInvestor(user)"
                    nbTooltip="Reactivate"
                    nbTooltipStatus="control"
                    nbTooltipPlacement="bottom"
                  >
                    <nb-icon icon="unlockIcon" pack="custom"></nb-icon>
                  </button>
                </ng-container>

                <button
                  class="button-icon"
                  status="default"
                  nbButton
                  shape="round"
                  ghost
                  (click)="deleteInvestorConfirm(user)"
                  nbTooltip="Archive"
                  nbTooltipStatus="control"
                  nbTooltipPlacement="bottom"
                >
                  <nb-icon icon="archiveIcon" pack="custom"></nb-icon>
                </button>

                <button
                  *ngIf="user.enableCopyLink"
                  status="default"
                  class="button-icon"
                  nbButton
                  shape="round"
                  ghost
                  (click)="copyLink(user)"
                  nbTooltip="Copy Link"
                  nbTooltipStatus="control"
                  nbTooltipPlacement="bottom"
                >
                  <nb-icon icon="copyIcon" pack="custom"></nb-icon>
                </button>
              </ng-container>

              <ng-container *ngIf="isManager()">
                <button
                  nbButton
                  ghost
                  shape="round"
                  status="default"
                  class="button-icon"
                  (click)="editInvestor(user)"
                  nbTooltip="View"
                  nbTooltipStatus="control"
                  nbTooltipPlacement="bottom"
                >
                  <nb-icon icon="eyeIcon" pack="custom"></nb-icon>
                </button>
              </ng-container>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" *ngIf="!eventFilters" [attr.colspan]="10">No users found</td>
            <td style="text-align: center; display: block" *ngIf="eventFilters" [attr.colspan]="10">
              Sorry, your search did not return any matching results. Please try again
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
