<div *ngIf="showFilter" class="filter-bar">
  <!-- <button nbButton ghost status="primary">
        <nb-icon icon="chatFilterIcon" pack="custom"></nb-icon>
    </button> -->

  <button
    nbButton
    status="primary"
    ghost
    shape="round"
    class="chat-button"
    [nbContextMenu]="filterItems"
    nbContextMenuTag="chat-filter"
  >
    <nb-icon icon="chatFilterIcon" pack="custom"></nb-icon>
  </button>
</div>
<app-chat
  [noMessagesPlaceholder]="isInternalNote ? 'No notes yet.' : 'No messages yet.'"
  size="large"
  (messageChange)="getOldMessage($event)"
  [scrollBottom]="scrollBottom"
>
  <app-chat-message
    *ngFor="let msg of messages"
    [metaData]="msg"
    [type]="msg.type"
    [message]="msg.message"
    [reply]="msg.isSender"
    [sender]="msg.userName"
    [date]="msg.dateCreated"
    [files]="msg.files"
    [avatar]="msg.userName"
    dateFormat="HH:mm:ssa  dd/MM/yy"
  >
  </app-chat-message>
  <app-chat-form
    [disabled]="isManager()"
    [globalMessageType]="globalMessageType"
    (send)="sendMessage($event)"
    [showFilter]="showFilter"
    [isAdmin]="isAdmin"
    [isInternalNote]="isInternalNote"
    [dropFilePlaceholder]="'Add Attachment'"
    [facilities]="facilities"
    [dropFiles]="true"
  >
  </app-chat-form>
</app-chat>
<nb-progress-bar style="width: 100%" *ngIf="progress" [value]="progress" status="primary">
  Uploading {{ progress }}%
</nb-progress-bar>
<div *ngIf="false" class="add-doc-header">
  <div>
    <strong class="h5"> Document : </strong>
  </div>

  <div>
    <!-- <div class="file-container" appDnd (fileDropped)="onFileDropped($event)">
            <input type="file" #fileDropRef id="fileDropRef" (change)="fileBrowseHandler($event)" name="moredoc"
                accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt" />
            <div class="m-0" for="fileDropRef" (click)="fileDropRef.click()">
                Add Other Doc <nb-icon icon="plus-outline"></nb-icon>
            </div>
        </div> -->
  </div>
</div>

<div *ngIf="false" class="files-list">
  <div class="single-file" *ngFor="let file of attachments; let i = index">
    <div class="info">
      <div
        class="name"
        nbTooltipStatus="primary"
        nbTooltip="Click to Download {{ file?.fileName }}"
        class="clickable"
        (click)="downloadFile(file?.documentKey)"
      >
        {{ file?.fileName }}
      </div>
      <div>
        <nb-user
          nbTooltip="{{ file.userName }}"
          nbTooltipStatus="control"
          nbTooltipPlacement="bottom"
          size="small"
          [name]="file.userName"
          onlyPicture="true"
        >
        </nb-user>
      </div>
      <div *ngIf="file.isDelete" class="delete" (click)="deleteDocument(file?.documentKey)">
        <nb-icon class="file-delete" icon="close-circle"></nb-icon>
      </div>
    </div>
  </div>
</div>
