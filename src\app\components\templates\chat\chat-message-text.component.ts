/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DocumentService } from '@core/services/document.service';
import { NbIconModule } from '@nebular/theme';

/**
 * Chat message component.
 */
@Component({
  selector: 'app-chat-message-text',
  templateUrl: 'chat-message-text.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, NbIconModule],
})
export class NbChatMessageTextComponent {
  /**
   * Message sender
   * @type {string}
   */
  @Input() sender!: string;

  /**
   * Message type
   * @type {boolean}
   */
  @Input() metaData!: any;

  /**
   * Message sender
   * @type {string}
   */
  @Input() message!: string;

  /**
   * Message send date
   * @type {Date}
   */
  @Input() date!: Date;

  /**
   * Message send date format, default 'shortTime'
   * @type {string}
   */
  @Input() dateFormat = 'shortTime';

  constructor(private documentService: DocumentService) {}

  async downloadFile(file: any): Promise<void> {
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }
}
