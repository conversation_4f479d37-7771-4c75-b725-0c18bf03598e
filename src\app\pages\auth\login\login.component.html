<nb-layout>
  <nb-layout-column class="p-0">
    <div class="admin-back flex flex-col items-center min-h-screen">
      <!-- <div class="mb-4">
      <img class="logo" src="assets/images/admin-logo.svg" />
    </div> -->
      <div class="w-full bg-velvet-700 h-[60px]"></div>
      <div class="w-full max-w-[400px] mt-16">
        <div class="header flex flex-col items-center mb-8">
          <div class="mb-[15px]">
            <img class="w-[350px]" src="assets/images/investor-wyde-logo-powered-by.svg" alt="Investor Wyde Logo" />
          </div>
          <h2 class="!font-lato font-bold mb-1 leading-[1.2] !text-velvet-700">Welcome Back</h2>
          <p class="!font-lato mb-0 !text-velvet-700">Please sign in to access your account</p>
        </div>
        <div class="p-4 bg-white rounded-lg login-form-container min-h-[300px] flex flex-col justify-center gap-4">
          <div class="flex flex-col gap-6">
            <div *ngIf="showGoogleButton" class="google-signin-container">
              <asl-google-signin-button
                type="standard"
                size="large"
                text="signin_with"
                shape="square"
                theme="outline"
                logo_alignment="center"
                locale="en"
                [width]="352"
              ></asl-google-signin-button>
            </div>
            <button
              (click)="loginWithMicrosoft()"
              class="flex items-center justify-center gap-3 px-4 py-2 border border-grey-600 rounded-lg bg-white !font-lato text-sm font-normal text-grey-700 transition-all duration-200 ease-in-out hover:border-velvet-700 hover:bg-velvet-700/[0.02] focus:outline-none focus:ring-2 focus:ring-velvet-700/20 focus:border-velvet-700 group"
            >
              <img src="assets/icons/microsoft-logo-icon.svg" alt="Microsoft Icon" class="w-5 h-5" />
              <span class="group-hover:text-velvet-700 transition-colors duration-200">Sign in with Microsoft</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </nb-layout-column>
</nb-layout>
