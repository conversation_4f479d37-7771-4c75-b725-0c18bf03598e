FROM node:22.14.0-alpine AS build

WORKDIR /app

COPY package.json package-lock.json ./

ARG ENVIRONMENT=dev

RUN npm install

COPY . .

RUN npm run build -- --configuration=$ENVIRONMENT

FROM nginx:stable-alpine AS production

COPY --from=build /app/dist/Sydney-Wyde/browser /usr/share/nginx/html

# Copy our fixed nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
