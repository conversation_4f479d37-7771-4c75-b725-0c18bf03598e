<p-skeleton *ngIf="!documents"></p-skeleton>

<div class="flex flex-wrap -mx-2">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        placeholder="Date"
        name="date"
        id="value"
        [selected]=""
        (selectedChange)="dt.filter($event, 'date', 'equals')"
      >
        <nb-option *ngFor="let dateFilter of dateFilterData" [value]="dateFilter.value">
          {{ dateFilter.name }}
        </nb-option>
      </nb-select>
    </div>

    <div *ngIf="!assetKeyDataId">
      <nb-select
        fullWidth
        placeholder="Facility"
        name="facilityName"
        id="value"
        (selectedChange)="dt.filter($event, 'facility', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let facility of facilitiesData" [value]="facility.facilityName">
          {{ facility.facilityName }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <nb-select
        fullWidth
        placeholder="Document Type"
        name="documentTypeId"
        id="value"
        (selectedChange)="dt.filter($event, 'documentTypeId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let documentType of docTypes" [value]="documentType.id">
          {{ documentType.name }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>
  </div>
</div>

<div *ngIf="documents">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="documents"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="assetKeyDataId ? false : true"
        [rows]="assetKeyDataId ? 200 : 10"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollHeight="flex"
        scrollHeight="flex"
        [globalFilterFields]="['id', 'description', 'investment', 'dateCreated', 'fileSize']"
        sortField="id"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 100px; max-width: 100px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 300px" [pSortableColumn]="'description'">
              <div>
                <div>Description</div>
                <p-sortIcon [field]="'description'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 250px" [pSortableColumn]="'facility'">
              <div>
                <div>Facility</div>
                <p-sortIcon [field]="'facility'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 125px" [pSortableColumn]="'type'">
              <div>
                <div>Type</div>
                <p-sortIcon [field]="'type'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 150px" [pSortableColumn]="'lenderTitle'">
              <div>
                <div>Update Title</div>
                <p-sortIcon [field]="'lenderTitle'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 150px" [pSortableColumn]="'category'">
              <div>
                <div>Category</div>
                <p-sortIcon [field]="'category'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 125px" [pSortableColumn]="'date'">
              <div>
                <div>Date</div>
                <p-sortIcon [field]="'date'"></p-sortIcon>
              </div>
            </th>

            <th style="width: 125px" [pSortableColumn]="'fileSize'">
              <div>
                <div>Size</div>
                <p-sortIcon [field]="'fileSize'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 125px" [hidden]="removeAction" *ngIf="isAdmin()">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-document>
          <tr>
            <td style="min-width: 100px; max-width: 100px">{{ document.id }}</td>

            <td style="min-width: 300px">
              <div class="text-blue-600 cursor-pointer" (click)="downloadFile(document)">
                {{ document.description }}
              </div>
            </td>
            <td style="width: 250px">{{ document.facility }}</td>
            <td style="width: 125px">{{ document.type }}</td>
            <td style="width: 150px">{{ document.lenderTitle }}</td>
            <td style="width: 150px">{{ document.category }}</td>
            <td style="width: 125px">{{ document.date | date: "dd/MM/YYYY" }}</td>
            <td style="width: 125px" class="text-blue-600">{{ document.fileSize }}KB</td>
            <td style="width: 125px" [hidden]="removeAction" *ngIf="isAdmin()">
              <button
                *ngIf="isAdmin()"
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                (click)="deleteDocument(document.documentKey, document.uploadedBy, assetKeyDataId)"
                nbTooltip="Delete Document"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="trash-2-outline"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" [attr.colspan]="10">No documents yet.</td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
<div class="w-full px-2" [hidden]="removeAction">
  <div class="flex flex-wrap -mx-2" *ngIf="(isAdmin() || isOriginatorManager()) && this.assetKeyDataId">
    <div class="w-6/12 px-2 my-[15px]">
      <button
        class="bg-velvet-700 hover:bg-velvet-600 text-white"
        *ngIf="true"
        nbButton
        status="default"
        type="button"
        (click)="addNewDocument()"
      >
        ADD NEW DOCUMENT
        <nb-icon icon="plus-outline"> </nb-icon>
      </button>
    </div>
    <!-- <div class="w-6/12 px-2 my-[15px]">
            <button class="float-right" (click)="next()" nbButton status="primary" style="min-width: 135px;">
                NEXT
            </button>
        </div> -->
  </div>
</div>
