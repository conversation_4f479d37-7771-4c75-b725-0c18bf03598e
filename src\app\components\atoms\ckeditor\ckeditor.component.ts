import { Component, forwardRef, input, OnInit, output } from '@angular/core';
import type { ClassicEditor, EditorConfig } from 'https://cdn.ckeditor.com/typings/ckeditor5.d.ts';

import { CommonModule } from '@angular/common';
import { FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { CKEditorCloudResult, CKEditorModule, loadCKEditorCloud } from '@ckeditor/ckeditor5-angular';
import { environment } from '@environments/environment';

/**
 * Base64UploadAdapter class for CKEditor
 * Converts uploaded images to Base64 encoded strings
 */
class Base64UploadAdapter {
  loader: any;
  reader: any;

  constructor(loader: any) {
    this.loader = loader;
  }

  upload() {
    return this.loader.file.then(
      (file: File) =>
        new Promise((resolve, reject) => {
          const reader = (this.reader = new FileReader());

          reader.addEventListener('load', () => {
            resolve({ default: reader.result });
          });

          reader.addEventListener('error', (err: any) => {
            reject(err);
          });

          reader.addEventListener('abort', () => {
            reject();
          });

          if (file) {
            reader.readAsDataURL(file);
          }
        }),
    );
  }

  abort() {
    if (this.reader) {
      this.reader.abort();
    }
  }
}

// Factory function that creates a Base64UploadAdapter instance
function Base64UploadAdapterPlugin(editor: any) {
  editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
    return new Base64UploadAdapter(loader);
  };
}

@Component({
  selector: 'app-ckeditor',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, CKEditorModule],
  templateUrl: './ckeditor.component.html',
  styleUrl: './ckeditor.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CkeditorComponent),
      multi: true,
    },
  ],
})
export class CkeditorComponent implements OnInit {
  public Editor: typeof ClassicEditor | null = null;
  public editorConfig: EditorConfig | null = null;
  public fullConfig: EditorConfig | null = null;
  variant = input<'default' | 'full'>('default');

  // Computed property for current config
  get currentConfig(): EditorConfig | null {
    return this.variant() === 'full' ? this.fullConfig : this.editorConfig;
  }

  /** Two‑way bound editor data */
  set innerValue(value: string) {
    if (value !== this._innerValue) {
      this._innerValue = value;
      this.onChange(value); // Propagate change to Angular forms
    }
  }
  get innerValue(): string {
    return this._innerValue;
  }
  private _innerValue = '';

  ready = false;
  isDisabled = false;

  focused = output();

  ngOnInit(): void {
    loadCKEditorCloud({
      version: '45.2.0',
      premium: true,
    }).then(this._setupEditor.bind(this));
  }

  private _setupEditor(cloud: CKEditorCloudResult<{ version: '45.2.0'; premium: true }>) {
    const {
      ClassicEditor,
      Essentials,
      Paragraph,
      Bold,
      Italic,
      Underline,
      Strikethrough,
      Undo,
      Alignment,
      BlockQuote,
      List,
      ListProperties,
      Heading,
      FontSize,
      FontFamily,
      FontColor,
      FontBackgroundColor,
      Autoformat,
      CloudServices,
      CKFinder,
      CKFinderUploadAdapter,
      PictureEditing,
      Image,
      ImageCaption,
      ImageStyle,
      ImageToolbar,
      ImageUpload,
      Indent,
      IndentBlock,
      Link,
      MediaEmbed,
      PasteFromOffice,
      Table,
      TableToolbar,
      TextTransformation,
      SourceEditing,
      HorizontalLine,
      SpecialCharacters,
      SpecialCharactersEssentials,
      PageBreak,
      RemoveFormat,
      SelectAll,
      FindAndReplace,
      Code,
      CodeBlock,
      Highlight,
      Subscript,
      Superscript,
      FileRepository,
      PasteFromMarkdownExperimental,
      Fullscreen,
      Clipboard,
    } = cloud.CKEditor;

    // const { SourceEditingEnhanced, FormatPainter } = cloud.CKEditorPremiumFeatures;
    this.Editor = ClassicEditor;
    this.editorConfig = {
      licenseKey: environment.ckEditorKey,
      plugins: [
        Essentials,
        Paragraph,
        Heading,
        Bold,
        Italic,
        Underline,
        Strikethrough,
        List,
        ListProperties,
        Alignment,
        BlockQuote,
        Undo,
        FontSize,
        FontFamily,
        FontColor,
        FontBackgroundColor,
        Autoformat,
        CloudServices,
        CKFinder,
        CKFinderUploadAdapter,
        PictureEditing,
        Image,
        ImageCaption,
        ImageStyle,
        ImageToolbar,
        ImageUpload,
        Indent,
        IndentBlock,
        Link,
        MediaEmbed,
        PasteFromOffice,
        Table,
        TableToolbar,
        TextTransformation,
        SourceEditing,
        HorizontalLine,
        SpecialCharacters,
        SpecialCharactersEssentials,
        PageBreak,
        RemoveFormat,
        SelectAll,
        FindAndReplace,
        Code,
        CodeBlock,
        Highlight,
        Subscript,
        Superscript,
        FileRepository,
        Clipboard,
        // SourceEditingEnhanced, // premium
        // FormatPainter, // premium
        PasteFromMarkdownExperimental,
        Fullscreen,
        Base64UploadAdapterPlugin, // Add the Base64UploadAdapter plugin
      ],

      placeholder: 'Start writing...',
      toolbar: {
        items: [
          'heading',
          'alignment',
          'bold',
          'italic',
          'underline',
          'bulletedList',
          'numberedList',
          'blockQuote',
          'insertTable',
          'link',
          'mediaEmbed',
          'sourceEditing',
          'undo',
          'redo',
        ],
        shouldNotGroupWhenFull: true,
      },
      fontSize: {
        options: [8, 9, 10, 11, 12, 14, 16, 18, 24, 30, 36, 48, 60, 72, 96],
        supportAllValues: true,
      },
      fontFamily: {
        options: [
          'default',
          'Arial, Helvetica, sans-serif',
          'Courier New, Courier, monospace',
          'Georgia, serif',
          'Lucida Sans Unicode, Lucida Grande, sans-serif',
          'Tahoma, Geneva, sans-serif',
          'Times New Roman, Times, serif',
          'Trebuchet MS, Helvetica, sans-serif',
          'Verdana, Geneva, sans-serif',
        ],
        supportAllValues: true,
      },
      fontColor: {
        colors: [
          { color: 'hsl(0, 0%, 0%)', label: 'Black' },
          { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
          { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
          { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
          { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
          { color: 'hsl(0, 75%, 60%)', label: 'Red' },
          { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
          { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
          { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
          { color: 'hsl(120, 75%, 60%)', label: 'Green' },
          { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
          { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
          { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
          { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
          { color: 'hsl(270, 75%, 60%)', label: 'Purple' },
        ],
        columns: 5,
        documentColors: 10,
        colorPicker: {
          format: 'hsl',
        },
      },
      fontBackgroundColor: {
        colors: [
          { color: 'hsl(0, 0%, 0%)', label: 'Black' },
          { color: 'hsl(0, 0%, 30%)', label: 'Dim grey' },
          { color: 'hsl(0, 0%, 60%)', label: 'Grey' },
          { color: 'hsl(0, 0%, 90%)', label: 'Light grey' },
          { color: 'hsl(0, 0%, 100%)', label: 'White', hasBorder: true },
          { color: 'hsl(0, 75%, 60%)', label: 'Red' },
          { color: 'hsl(30, 75%, 60%)', label: 'Orange' },
          { color: 'hsl(60, 75%, 60%)', label: 'Yellow' },
          { color: 'hsl(90, 75%, 60%)', label: 'Light green' },
          { color: 'hsl(120, 75%, 60%)', label: 'Green' },
          { color: 'hsl(150, 75%, 60%)', label: 'Aquamarine' },
          { color: 'hsl(180, 75%, 60%)', label: 'Turquoise' },
          { color: 'hsl(210, 75%, 60%)', label: 'Light blue' },
          { color: 'hsl(240, 75%, 60%)', label: 'Blue' },
          { color: 'hsl(270, 75%, 60%)', label: 'Purple' },
        ],
        columns: 5,
        documentColors: 10,
        colorPicker: {
          format: 'hsl',
        },
      },
      image: {
        toolbar: [
          'imageTextAlternative',
          'toggleImageCaption',
          'imageStyle:inline',
          'imageStyle:block',
          'imageStyle:side',
          'linkImage',
        ],
        upload: {
          types: ['jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff'],
        },
      },
      // Configure image upload to use Base64 adapter
      ckfinder: {
        // Feature is not used, but needed to enable the upload button
        uploadUrl: '/upload', // This URL won't be used as we're using the Base64 adapter
      },
      table: {
        contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties'],
      },
      link: {
        decorators: {
          openInNewTab: {
            mode: 'manual',
            label: 'Open in a new tab',
            attributes: {
              target: '_blank',
              rel: 'noopener noreferrer',
            },
          },
        },
      },
      codeBlock: {
        languages: [
          { language: 'plaintext', label: 'Plain text' },
          { language: 'c', label: 'C' },
          { language: 'cs', label: 'C#' },
          { language: 'cpp', label: 'C++' },
          { language: 'css', label: 'CSS' },
          { language: 'diff', label: 'Diff' },
          { language: 'html', label: 'HTML' },
          { language: 'java', label: 'Java' },
          { language: 'javascript', label: 'JavaScript' },
          { language: 'php', label: 'PHP' },
          { language: 'python', label: 'Python' },
          { language: 'ruby', label: 'Ruby' },
          { language: 'typescript', label: 'TypeScript' },
          { language: 'xml', label: 'XML' },
        ],
      },
    };

    this.fullConfig = {
      ...this.editorConfig,
      toolbar: {
        items: [
          'bold',
          'italic',
          'underline',
          'strikethrough',
          'removeFormat',
          '|',
          'bulletedList',
          'numberedList',
          '|',
          'indent',
          'outdent',
          'alignment',
          '|',
          'fontFamily',
          'fontSize',
          'heading',
          '|',
          'fontColor',
          'fontBackgroundColor',
          '|',
          'subscript',
          'superscript',
          '|',
          'uploadImage',
          'mediaEmbed',
          '-',
          'formatPainter', //premium
          'copy',
          'cut',
          'paste',
          'selectAll',
          '|',
          'horizontalLine',
          'insertTable',
          'link',
          'specialCharacters',
          '|',
          'undo',
          'redo',
          '|',
          'findAndReplace',
          'sourceEditing',
          'sourceEditingEnhanced',
          'fullscreen',
          // 'preview',
        ],
        shouldNotGroupWhenFull: true,
      },
    };

    // Set initial config
    this.editorConfig = this.currentConfig;
  }

  // ---------- ControlValueAccessor hooks ----------
  writeValue(value: unknown): void {
    this.innerValue = (value ?? '') as string;
    this.ready = true; // once first value arrives, render editor
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  // ---------- internal cahttps://open.spotify.com/track/4p2djURll4u7nNvhLMDAVBllbacks ----------
  // default no‑ops; replaced by Angular forms at runtime
  private onChange: (value: string) => void = () => {};
  private onTouched = () => {};
}
