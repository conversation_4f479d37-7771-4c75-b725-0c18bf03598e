/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { NbChatMessageTextComponent } from './chat-message-text.component';

/**
 * Chat message component.
 */
@Component({
  selector: 'app-chat-message-quote',
  template: `
    <p class="sender" *ngIf="sender || date">
      {{ sender }} <time>{{ date | date: dateFormat }}</time>
    </p>
    <p class="quote">
      {{ quote }}
    </p>
    <app-chat-message-text [message]="message">
      {{ message }}
    </app-chat-message-text>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, NbChatMessageTextComponent],
})
export class NbChatMessageQuoteComponent {
  /**
   * Message sender
   * @type {string}
   */
  @Input() message!: string;

  /**
   * Message sender
   * @type {string}
   */
  @Input() sender!: string;

  /**
   * Message send date
   * @type {Date}
   */
  @Input() date!: Date;

  /**
   * Message send date format, default 'shortTime'
   * @type {string}
   */
  @Input() dateFormat = 'shortTime';

  /**
   * Quoted message
   * @type {Date}
   */
  @Input() quote!: string;
}
