<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <div *ngIf="!investorId; then saveText; else updateText"></div>
      <ng-template #saveText>
        <h5>New Investor</h5>
      </ng-template>
      <ng-template #updateText>
        <h5>{{ entityName }}</h5>
      </ng-template>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="w-full px-2">
    <nb-card style="box-shadow: 0px 0px 4px 1px #d3d3d36b; border-radius: 12px">
      <nb-card-body style="padding: 0">
        <ng-container>
          <nb-tabset class="hide-tab-mobile" (changeTab)="tabClick($event)" #tabset>
            <nb-tab
              tabId="1"
              [responsive]="'true'"
              tabTitle="Overview"
              [tabIcon]="{ icon: 'overviewIcon', pack: 'custom' }"
              active
            >
              <div style="width: 100%" *ngIf="tabId === 1">
                <app-investor-overview (userChange)="userChange($event)" (changeTab)="changeTab($event)">
                </app-investor-overview>
              </div>
            </nb-tab>

            <nb-tab
              tabId="2"
              [disabled]="!investorId"
              tabTitle="Financials"
              [tabIcon]="{ icon: 'financialsIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 2">
                <app-investor-financials (changeTab)="changeTab($event)"></app-investor-financials>
              </div>
            </nb-tab>

            <nb-tab
              tabId="3"
              [disabled]="!investorId"
              tabTitle="Investments"
              [tabIcon]="{ icon: 'investmentsIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 3">
                <app-investor-investments (changeTab)="changeTab($event)"></app-investor-investments>
              </div>
            </nb-tab>

            <nb-tab
              tabId="4"
              [disabled]="!investorId"
              tabTitle="Documents"
              [tabIcon]="{ icon: 'document30Icon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 4">
                <app-investor-documents (changeTab)="changeTab($event)"></app-investor-documents>
              </div>
            </nb-tab>

            <nb-tab
              tabId="5"
              [disabled]="!investorId"
              tabTitle="Notes"
              [tabIcon]="{ icon: 'notesIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 5">
                <app-investor-chat *ngIf="investorId" [investorId]="investorId" [isInternalNote]="true">
                </app-investor-chat>
              </div>
            </nb-tab>
          </nb-tabset>
        </ng-container>

        <nb-accordion class="show-on-mobile">
          <nb-accordion-item>
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="overviewIcon" pack="custom"></nb-icon>
              Overview
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investor-overview (userChange)="userChange($event)" (changeTab)="changeTab($event)">
              </app-investor-overview>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item [disabled]="!investorId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="financialsIcon" pack="custom"></nb-icon>
              Financials
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-investor-financials (changeTab)="changeTab($event)"></app-investor-financials>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <!-- <nb-accordion-item [disabled]="!investorId">
                        <nb-accordion-item-header>
                            <nb-icon class="accordion-icon" icon="investmentsIcon" pack="custom"></nb-icon> Investments
                        </nb-accordion-item-header>
                        <nb-accordion-item-body>
                            <app-investor-investments (changeTab)="changeTab($event)"></app-investor-investments>
                        </nb-accordion-item-body>
                    </nb-accordion-item> -->

          <!-- <nb-accordion-item [disabled]="!investorId">
                        <nb-accordion-item-header>
                            <nb-icon class="accordion-icon" icon="document30Icon" pack="custom"></nb-icon> Documents
                        </nb-accordion-item-header>
                        <nb-accordion-item-body>
                            <app-investor-documents (changeTab)="changeTab($event)"></app-investor-documents>
                        </nb-accordion-item-body>
                    </nb-accordion-item> -->

          <!-- <nb-accordion-item [disabled]="!investorId">
                        <nb-accordion-item-header>
                            <nb-icon class="accordion-icon" icon="notesIcon" pack="custom"></nb-icon> Notes
                        </nb-accordion-item-header>
                        <nb-accordion-item-body>
                            <app-investor-chat *ngIf="investorId" [investorId]="investorId" [isInternalNote]="true">
                            </app-investor-chat>
                        </nb-accordion-item-body>
                    </nb-accordion-item> -->
        </nb-accordion>
      </nb-card-body>
    </nb-card>
  </div>
</div>
