import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { TypeKey } from '@core/models/config';

@Injectable({
  providedIn: 'root',
})
export class OrgService {
  constructor(private http: HttpClient) {}

  getOrgByRole(typeKey: TypeKey): any {
    return this.http.post(`${environment.apiURL}/api/Org/get-org-by-role`, {
      typekey: typeKey,
    });
  }
}
