// Header Section
.title {
    h5 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: #333;
    }

    .subtitle {
        font-size: 0.875rem;
        color: #666;
        margin: 0;
    }
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;

    .theme-btn {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        color: #495057;

        &:hover {
            background: #e9ecef;
        }
    }

    .export-btn {
        background: #007bff;
        border: 1px solid #007bff;

        &:hover {
            background: #0056b3;
            border-color: #0056b3;
        }
    }
}

// Report Type Section
.report-type-section {
    margin: 2rem 0;

    h6 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #333;
    }

    .report-type-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;

        .report-type-btn {
            padding: 0.75rem 1.5rem;
            border: 1px solid #e9ecef;
            background: #fff;
            color: #495057;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;

            &:hover {
                background: #f8f9fa;
                border-color: #dee2e6;
            }

            &.active {
                background: #007bff;
                border-color: #007bff;
                color: #fff;

                &:hover {
                    background: #0056b3;
                    border-color: #0056b3;
                }
            }

            nb-icon {
                font-size: 1rem;
            }
        }
    }
}

// Filters Section
.filters-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;

    h6 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #333;
    }

    .filter-group {
        label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #495057;
        }

        input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            font-size: 0.875rem;

            &:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }

            &::placeholder {
                color: #6c757d;
            }
        }
    }

    .apply-filters-btn {
        background: #007bff;
        border: 1px solid #007bff;
        padding: 0.5rem 1.5rem;

        &:hover {
            background: #0056b3;
            border-color: #0056b3;
        }
    }
}

// Report Data Section
.report-data-section {
    margin: 2rem 0;

    .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        h6 {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .record-count {
            font-size: 0.875rem;
            color: #6c757d;
        }
    }
}

// Table Styling
::ng-deep {
    .p-datatable {
        .p-datatable-header {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 1rem;
        }

        .p-datatable-thead > tr > th {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            font-weight: 600;
            color: #495057;
            font-size: 0.875rem;
        }

        .account-activity-header th {
            background: #e3f2fd;
            color: #1976d2;
            font-weight: 600;
            text-align: center;
            border-right: 1px solid #bbdefb;

            &:last-child {
                border-right: none;
            }
        }

        .p-datatable-tbody > tr > td {
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            font-size: 0.875rem;
            text-align: center;
        }

        .p-datatable-tbody > tr:nth-child(even) {
            background: #f8f9fa;
        }

        .p-datatable-tbody > tr:hover {
            background: #e3f2fd;
        }
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        background: #ffc107;
        color: #212529;

        &.completed {
            background: #28a745;
            color: #fff;
        }
    }

    // Nebular card styling
    nb-card {
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    nb-card-body {
        padding: 1.5rem;
    }
}
