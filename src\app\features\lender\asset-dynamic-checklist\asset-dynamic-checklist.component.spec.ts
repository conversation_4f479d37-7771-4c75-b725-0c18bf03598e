import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AssetDynamicChecklistComponent } from './asset-dynamic-checklist.component';

describe('AssetDynamicChecklistComponent', () => {
  let component: AssetDynamicChecklistComponent;
  let fixture: ComponentFixture<AssetDynamicChecklistComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AssetDynamicChecklistComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AssetDynamicChecklistComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
