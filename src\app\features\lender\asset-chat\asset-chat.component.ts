import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { NbChatFormComponent } from '@components/templates/chat/chat-form.component';
import { NbChatMessageComponent } from '@components/templates/chat/chat-message.component';
import { NbChatComponent } from '@components/templates/chat/chat.component';
import { DeleteDocumentComponent } from '@components/templates/delete-document/delete-document.component';
import { GlobalMessageType } from '@core/helpers';
import { FacilitiesFilterResponse, FacilityFilter } from '@core/models/response/facilities-filter.response';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { NbMenuItem } from '@core/services/menu.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbContextMenuModule,
  NbDialogService,
  NbIconModule,
  NbMenuService,
  NbProgressBarModule,
  NbToastrService,
  NbTooltipModule,
  NbUserModule,
} from '@nebular/theme';
import { Subject } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-asset-chat',
  templateUrl: './asset-chat.component.html',
  styleUrls: ['./asset-chat.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbIconModule,
    NbProgressBarModule,
    NbContextMenuModule,
    NbTooltipModule,
    NbUserModule,
    NbChatComponent,
    NbChatMessageComponent,
    NbChatFormComponent,
    NbButtonModule,
  ],
})
export class AssetChatComponent implements OnInit, OnChanges {
  @Input() assetId?: any;

  @Input() globalMessageType: GlobalMessageType = GlobalMessageType.Investor;

  @Input() userId?: any;

  @Input() contactOrgId?: number;

  @Input() filter!: any;

  @Input() showFilter = true;

  @Input() isInternalNote = false;

  @Input() isAdmin!: boolean;

  facilities: FacilityFilter[] = [];

  scrollBottom = true;
  messages: any[] = [];
  progress: any;
  attachments: any;
  droppedFiles: any[] = [];

  chatFilter = true;
  filterItems: NbMenuItem[] = [
    {
      title: 'Internal',
      data: true,
      selected: this.chatFilter,
    },
    {
      title: 'Client',
      data: false,
      selected: !this.chatFilter,
    },
  ];
  private ngUnsubscribe = new Subject();
  isFirstScroll = true;

  constructor(
    private documentService: DocumentService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private dialogService: NbDialogService,
    private assetService: AssetService,
    private nbMenuService: NbMenuService,
  ) {}

  ngOnInit(): void {
    this.chatFilter = this.isInternalNote;

    // setTimeout(() => {
    //   this.getMessage(this.assetId);
    // }, 1000);

    this.nbMenuService
      .onItemClick()
      .pipe(takeUntil(this.ngUnsubscribe))
      .pipe(
        filter(({ tag }) => tag === 'chat-filter'),
        map(({ item: { data } }) => data),
      )
      .subscribe((data) => {
        this.chatFilter = data;
        this.getMessage(this.assetId);
        this.filterItems = [
          {
            title: 'Internal',
            data: true,
            selected: this.chatFilter,
          },
          {
            title: 'Client',
            data: false,
            selected: !this.chatFilter,
          },
        ];
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.chatFilter = this.isInternalNote;
    if ('assetId' in changes) {
      this.getMessage(changes.assetId.currentValue);
    } else if ('filter' in changes) {
      this.getMessage(this.assetId);
    } else if ('userId' in changes) {
      this.getMessage(this.assetId);
    } else {
      this.getMessage(this.assetId);
    }

    if ('contactOrgId' in changes) {
      this.getFacilities(this.contactOrgId);
    }
  }

  private getMessage(assetId: any, chatId = 0): void {
    let params: any = {
      assetId,
      chatId,
      userId: this.userId,
      isInternalNote: this.chatFilter,
      filterBy: this.filter,
    };
    if (chatId === 0) {
      params = {
        assetId,
        userId: this.userId,
        isInternalNote: this.chatFilter,
        filterBy: this.filter,
      };
    }

    setTimeout(() => {
      this.chathistory(params, chatId);
    }, 500);

    this.getAttachments(assetId);
  }

  private chathistory(params: any, chatId: number): void {
    this.assetService.chathistory(params).subscribe((response: any) => {
      if (response.success) {
        if (chatId === 0) {
          this.scrollBottom = true;
          this.messages = response.payload.chats;
          this.messages = this.messages.reverse();
        } else {
          if (this.isFirstScroll === false) {
            this.scrollBottom = false;
          }
          this.messages = [...response.payload.chats.reverse(), ...this.messages];
          if (this.isFirstScroll) {
            this.isFirstScroll = false;
          }
        }
      }
    });
  }

  private getAttachments(assetId: any): void {
    this.documentService.getAttachments({ assetId }).subscribe((response: any) => {
      if (response.success) {
        this.attachments = response.payload.attachments;
      }
    });
  }

  sendMessage(event: any): void {
    event.message = event.message.trim();

    const selectedFacility: FacilityFilter = event.selectedFacility as FacilityFilter;

    const formData = new FormData();

    if (selectedFacility && this.globalMessageType === GlobalMessageType.Asset) {
      formData.append('FacilityId', selectedFacility.id as any);
      formData.append('Facility', selectedFacility.facilityName as any);
    }

    if (this.userId) {
      formData.append('RecipientId', this.userId);
    }

    if (this.assetId) {
      formData.append('assetId', this.assetId as any);
    }

    formData.append('Message', event.message);
    formData.append('MessageType', '2');

    formData.append('IsInternalNote', event.isInternalNote);

    for (const item of event.files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
      // this.files.push(item);
    }

    if (this.sharedService.isInvestor()) {
      formData.append('IsAdmin', false as any);
    } else {
      formData.append('IsAdmin', true as any);
    }

    formData.append('Host', window.location.host);

    this.assetService
      .sendMessage(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                if (formData.get('Files')) {
                  this.progress = Math.round((100 * event.loaded) / event.total);
                } else {
                  this.progress = 0;
                }
              }

              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.isFirstScroll = true;
            this.getMessage(this.assetId);
            this.progress = 0;
          }
        },
        (err: any) => console.log(err),
      );
  }

  getOldMessage(event: any): void {
    this.getMessage(this.assetId, this.messages[0].chatId);
  }

  async downloadFile(documentKey: any): Promise<void> {
    await this.documentService.getDocument({
      documentKey,
    });
  }

  // onFileDropped(event: any): void {
  //   this.prepareFilesList(event);
  // }

  // fileBrowseHandler(event: any): void {
  //   console.log(event);
  //   this.prepareFilesList(event.target.files);
  // }

  private getFacilities(contactOrgId?: number): void {
    this.assetService.getFacilities(contactOrgId).subscribe((response: FacilitiesFilterResponse) => {
      if (response.success) {
        this.facilities = response.payload;
      }
    });
  }

  prepareFilesList(files: any[]): void {
    // const extensions = ['png', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'doc', 'docx', 'odt'];
    const allowedExtensions = /(\.jpg|\.jpeg|\.png|\.pdf|\.xls|\.xlsx|\.doc|\.docx|\.odt)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toast.danger('File type not supported', 'Upload Error!');
          // this.fileDropRef.nativeElement.value = '';
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toast.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          // this.fileDropRef.nativeElement.value = '';
          return;
        }
      }
    }

    const formData = new FormData();

    formData.append('FieldId', '0');

    if (this.assetId) {
      formData.append('FormRecordId', this.assetId as any);
    }

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.documentService
      .uploadDocument(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }

              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.toast.success('File Uploaded Successfully!', 'Success!');
            res.payload.forEach((element: any) => {});
          }
          this.getAttachments(this.assetId);
          this.getMessage(this.assetId);
        },
        (err: any) => console.log(err.message),
      );
  }

  deleteDocument(documentKey: string): void {
    this.dialogService
      .open(DeleteDocumentComponent, {
        context: {
          documentKey,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getAttachments(this.assetId);
          this.getMessage(this.assetId);
        }
      });
  }

  isManager(): boolean {
    return this.sharedService.isManager();
  }
}
