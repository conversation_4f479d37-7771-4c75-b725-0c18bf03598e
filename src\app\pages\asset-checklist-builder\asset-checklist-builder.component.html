<div class="flex flex-wrap -mx-2 my-[15px]">
  <div class="md:w-full px-2" style="margin: auto">
    <div class="title">
      <h5>Checklist Builder</h5>
    </div>
  </div>
</div>
<nb-card>
  <div class="w-full px-2 md:w-full px-2" style="padding: 24px 40px !important">
    <div class="flex flex-wrap -mx-2">
      <div class="md:w-6/12 px-2 sm:w-full px-2 w-full px-2">
        <label><strong> Title </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
        <input
          [(ngModel)]="checklistName"
          nbInput
          fullWidth
          placeholder=""
          shape="semi-round"
          type="text"
          fieldSize="large"
        />
      </div>
      <div class="lg:w-6/12 px-2 md:w-6/12 px-2 sm:w-full px-2 w-full px-2">
        <label>
          <strong for=" inStore"> Status </strong>
          <strong class="text-lime required"> &nbsp; * </strong></label
        >

        <nb-select
          fullWidth
          placeholder="Select Assessment"
          [(selected)]="selectedStatus"
          size="large"
          shape="semi-round"
          status="basic"
        >
          <nb-option *ngFor="let o of statusList" [value]="o.value">
            {{ o.name }}
          </nb-option>
        </nb-select>
      </div>
    </div>
  </div>
  <div
    *ngIf="checklistStructure && checklistStructure.length > 0 && !isLoading"
    style="padding: 0px 15px 25px !important"
  >
    <!-- <smooth-dnd-container #doctorSelect (drop)="onDrop($event)" [dragClass]="'card-ghost'"
            [dragHandleSelector]="'.drag-handle'" [nonDragAreaSelector]="'.non-draggable'"
            [dropClass]="'card-ghost-drop'" style="background: #f7f7f7;">
            <smooth-dnd-draggable *ngFor="let rowData of checklistStructure; let index = index"> -->
    <div cdkDropList (cdkDropListDropped)="onDrop($event)">
      <div
        *ngFor="let rowData of checklistStructure; let index = index"
        cdkDrag
        [cdkDragDisabled]="isDragDisabled(index)"
      >
        <span [ngSwitch]="rowData.type">
          <div class="flex flex-wrap -mx-2">
            <div class="w-full px-2">
              <div [ngSwitch]="index && index > 1">
                <div *ngSwitchCase="false">
                  <!-- <nb-icon class="non-draggable" icon="checklistDragIcon" pack="custom"></nb-icon>
                                    <nb-icon class="float-right" icon="checklistDeleteIcon" pack="custom"></nb-icon> -->
                </div>
                <div *ngSwitchCase="true">
                  <nb-icon
                    class="drag-handle"
                    icon="checklistDragIcon"
                    pack="custom"
                    dragHandleSelector
                    style="cursor: pointer"
                  ></nb-icon>
                  <nb-icon
                    class="float-right"
                    icon="checklistDeleteIcon"
                    pack="custom"
                    (click)="removeItem(index)"
                    style="cursor: pointer"
                  ></nb-icon>
                </div>
              </div>
            </div>
          </div>
          <div *ngSwitchCase="'header'">
            <div class="flex flex-wrap -mx-2" style="margin: 0px 25px 20px">
              <input
                [(ngModel)]="rowData.title"
                nbInput
                fullWidth
                placeholder="Enter Section Header"
                shape="semi-round"
                type="text"
                fieldSize="large"
                style="width: 20%"
              />
            </div>
          </div>
          <div *ngSwitchCase="'text'" style="margin: 0px 25px">
            <div class="w-full px-2">
              <div class="flex flex-wrap -mx-2">
                <div class="editor-container editor-container_classic-editor" #editorContainerElement>
                  <div class="editor-container__editor">
                    <div #editorElement>
                      <app-ckeditor [(ngModel)]="rowData.content" *ngIf="isLayoutReady"></app-ckeditor>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </span>
      </div>
    </div>
    <!-- </smooth-dnd-draggable>
                </smooth-dnd-container> -->
  </div>
  <div class="flex flex-wrap -mx-2 buttonRow">
    <div>
      <button class="new-item-button" *ngIf="true" nbButton status="default" type="button" (click)="addNewItem('text')">
        <nb-icon icon="plus-outline"> </nb-icon>
        ADD NEW TASK
      </button>
      <button
        style="margin-left: 20px"
        class="new-item-button"
        *ngIf="true"
        nbButton
        status="default"
        type="button"
        (click)="addNewItem('header')"
      >
        <nb-icon icon="plus-outline"> </nb-icon>
        ADD NEW SECTION
      </button>
    </div>
    <div>
      <button
        nbButton
        status="primary"
        style="margin-right: 20px; margin-left: 10px; min-width: 170px"
        (click)="previewChecklist()"
        [disabled]="checklistName.length === 0 || checklistStructure[0]['title'].length === 0"
      >
        PREVIEW
      </button>
      <button
        [nbSpinner]="isLoading"
        nbButton
        status="primary"
        style="margin-left: 10px; min-width: 170px"
        (click)="submitChecklist()"
        [disabled]="checklistName.length === 0 || checklistStructure[0]['title'].length === 0"
      >
        SAVE
      </button>
    </div>
  </div>
</nb-card>
