@use "themes" as *;

p {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
}

.help-text {
  font-size: 16px;
  line-height: 24px;
  color: var(--color-closing-bell);
}

.list {
  overflow: auto;
  max-height: 350px;
  margin: 9px;
}

.user-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-width: 350px;
  padding: 20px;
}

nb-popover {
  border: 1px solid #e7e9ec;
  box-sizing: border-box;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.04);
  border-radius: 20px;
}

nb-list-item :hover,
.selected {
  background: #f8f8f8;
  border-radius: 10px;
  cursor: pointer;
}

.forward-icon {
  position: absolute;
  right: 0;
  margin-right: 15px;
}
