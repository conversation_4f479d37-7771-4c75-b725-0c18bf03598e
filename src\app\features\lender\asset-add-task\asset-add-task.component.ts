import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { AfterViewChecked, ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { AssetTaskType } from '@core/models/config';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbAutocompleteModule,
  NbButtonModule,
  NbCardModule,
  NbDatepickerModule,
  NbDialogRef,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbProgressBarModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-asset-add-task',
  templateUrl: './asset-add-task.component.html',
  styleUrls: ['./asset-add-task.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbAlertModule,
    NbInputModule,
    NbFormFieldModule,
    NbDatepickerModule,
    NbAutocompleteModule,
    NbProgressBarModule,
    NbSpinnerModule,
    CkeditorComponent,
    NbButtonModule,
  ],
})
export class AssetAddTaskComponent implements OnInit, AfterViewChecked {
  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;

  @Input() data: any;
  @Input() edit = false;
  @Input() type?: AssetTaskType;
  @Input() facilityList: any;
  form: UntypedFormGroup;
  error = '';
  archiveLoading = false;
  saveLoading = false;
  submitted = false;

  assetKeyDataId: number | undefined;
  originatorUsers: any;
  selectedFacility: any;
  facilityOptions: any[] = [];
  uploadedDocuments: any[] = [];
  pendingDocuments: any[] = [];
  deleteDocuments: any[] = [];
  progress = 0;

  public editorConfig: any = {};

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private dialogService: NbDialogService,
    private formBuilder: UntypedFormBuilder,
    private cdr: ChangeDetectorRef,
    private sharedService: SharedService,
    private toast: NbToastrService,
    private assetService: AssetService,
    private documentService: DocumentService,
  ) {
    this.form = this.formBuilder.group({
      id: [null, Validators.required],
      taskType: [null, Validators.required],
      taskTitle: ['', Validators.required],
      dueDate: ['', Validators.required],
      assignedTo: [null, Validators.required],
      taskContent: [''],
      assetId: [null, Validators.required],
      isArchived: [null, Validators.required],
      reviewerId: [null],
      reviewerDate: [''],
      loanManagerId: [null],
      loanManagerDate: [''],
      completedBy: [null],
      completedByDate: [''],
    });
  }

  ngOnInit(): void {
    this.setConfig();
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId;
    if (this.data) {
      if (this.type == AssetTaskType.NoteTask) {
        const facilityObj = this.facilityList.find((f: any) => f.id == this.assetKeyDataId);
        this.selectedFacility = facilityObj.facilityName;
        this.uploadedDocuments = this.data.noteDocuments;
      }

      this.form.setValue({
        id: this.data.id,
        taskType: this.data.taskType,
        taskTitle: this.data.taskTitle,
        taskContent: this.data.taskContent,
        dueDate: new Date(this.data.dueDate),
        assignedTo: this.data.assignedTo,
        assetId: this.assetKeyDataId,
        isArchived: 0,
        reviewerId: this.data.reviewerId,
        reviewerDate: this.data.reviewerDate,
        loanManagerId: this.data.loanManagerId,
        loanManagerDate: this.data.loanManagerDate,
        completedBy: this.data.completedBy,
        completedByDate: this.data.completedByDate,
      });
    } else {
      this.f['id'].setValue(0);
      this.f['assetId'].setValue(this.assetKeyDataId || null);
      this.f['taskType'].setValue(this.type);
      this.f['isArchived'].setValue(0);
    }
    // get list of users
    this.getOriginatorList();
  }

  private getOriginatorList(): void {
    this.assetService.getLenderOriginatorUsers().subscribe((data: any) => {
      if (data.success) {
        this.originatorUsers = (data as any).payload.userResult.filter(
          (user: any) => user.roleId == 4 || user.roleId == 1,
        );
      }
    });
  }

  get f() {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.form.invalid) {
      return;
    }
    this.saveTask();
  }

  saveTask(): any {
    this.saveLoading = true;
    if (this.type == AssetTaskType.NoteTask) {
      this.saveNotesTask();
    } else {
      this.assetService.saveTask([this.form.value]).subscribe((data: any) => {
        this.saveLoading = false;
        if (data.success) {
          this.dialogRef.close(true);
        }
      });
    }
  }
  saveNotesTask() {
    const documents = {
      deletedDocuments: this.deleteDocuments,
      documents: this.pendingDocuments,
    };
    this.assetService
      .saveNotesTask(this.form.value, documents)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                if (documents.documents.length > 0) {
                  this.progress = Math.round((100 * event.loaded) / event.total);
                } else {
                  this.progress = 0;
                }
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          this.saveLoading = false;
          if (res.success) {
            this.dialogRef.close(true);

            setTimeout(() => {
              this.dialogRef.close({
                updated: true,
              });
            }, 200);
          }
        },
        (err: any) => {
          this.toast.danger(err.error.message, 'Notes Failed!');
        },
      );
  }

  archiveTaskConfirm(): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Archive Task',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Archive Task',
          yesButtonIcon: 'archive-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
        hasBackdrop: true,
        closeOnEsc: false,
        closeOnBackdropClick: false,
        hasScroll: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.archiveTask();
        }
      });
  }

  archiveTask(): void {
    this.archiveLoading = true;
    this.f['isArchived'].setValue(1);
    this.assetService.saveTask([this.form.value]).subscribe((response: any) => {
      if (response.success) {
        this.archiveLoading = false;
        this.dialogService
          .open(ConfirmPopupComponent, {
            context: {
              yesButton: 'CLOSE',
              yesSuccessPopUp: '',
              message: 'Task successfully archived',
              icon: 'checkmark-circle-2-outline',
            },
            autoFocus: false,
            hasBackdrop: true,
            closeOnEsc: false,
            closeOnBackdropClick: false,
            hasScroll: false,
          })
          .onClose.subscribe((res: any) => {
            if (res) {
              this.dialogRef.close(true);
            }
          });
      } else {
        this.archiveLoading = false;
        this.toast.danger(response.error.message, 'Oops!');
      }
    });
  }

  setConfig(): void {
    // this.editorConfig = {
    //   height: '100px',
    //   toolbar: [
    //     ['Format',
    //       'Bold',
    //       'Italic',
    //       'Underline',
    //       'BulletedList',
    //       'NumberedList',
    //       'Blockquote',
    //       'JustifyLeft',
    //       'JustifyCenter',
    //       'JustifyRight',
    //       'JustifyBlock']
    //     , ['Undo',
    //       'Redo']
    //   ],
    //   extraPlugins: ['justify', 'editorplaceholder'],
    //   editorplaceholder: 'Write task description...',
    //   resize_enabled: false,
    //   toolbarLocation: 'bottom'
    // };
  }

  dateChange(event: any): void {
    this.form.patchValue({
      dueDate: event,
    });
  }

  private filter(value: any): string[] {
    const filterValue = value.toLowerCase();
    return this.facilityList.filter((optionValue: any) => optionValue.facilityName.toLowerCase().includes(filterValue));
  }

  onFacilityChange(value: string) {
    this.f['assetId'].setValue(null);
    if (value && value.length > 2) {
      this.facilityOptions = this.filter(value);
    }
  }

  onFacilitySelection(event: string) {
    if (event && event.length > 0) {
      const filteredFacility = this.facilityList.find((f: any) => f.facilityName == event);
      this.selectedFacility = filteredFacility.facilityName;
      this.f['assetId'].setValue(filteredFacility.id);
    }
  }

  onFileDropped(event: any): void {
    const files = event;
    for (const file of files) {
      if (file.size > 20971520) {
        this.toast.warning('File size cannot be larger than 20mb', 'File Size Error');
        return;
      }
    }
    this.pendingDocuments.push(...event);
  }

  fileBrowseHandler(event: any): void {
    const files = event.target.files;
    for (const file of files) {
      if (file.size > 20971520) {
        this.toast.warning('File size cannot be larger than 20mb', 'File Size Error');
        return;
      }
    }
    this.pendingDocuments.push(...event.target.files);
  }

  clearDocuments(file: any, uploaded: boolean): void {
    if (uploaded) {
      this.deleteDocuments.push(file.id);
      const newUploadedDocuments = [...this.uploadedDocuments].filter((el) => el.id !== file.id);
      this.uploadedDocuments = newUploadedDocuments;
    } else {
      const newUploadedDocuments = [...this.pendingDocuments].filter((el) => el.name !== file.name);
      this.pendingDocuments = newUploadedDocuments;
      if (this.pendingDocuments.length == 0) {
        this.fileDropRef.nativeElement.value = '';
      }
      this.cdr.detectChanges();
    }
  }

  async downloadFile(file: any): Promise<void> {
    // this.toast.default(`Downloading started`, 'Success!', {
    // icon: 'download'
    // });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  close(): void {
    this.dialogRef.close(false);
  }
}
