import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { AuthenticationService } from '@core/services/authentication.service';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(
    private authenticationService: AuthenticationService,
    private router: Router,
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((error) => {
        // if (err.status === 401) {
        //     // auto logout if 401 response returned from api
        //     // this.authenticationService.logout();
        // }

        // const error = err.error.message || err.statusText;
        // return throwError(error);

        if (error instanceof HttpErrorResponse && error.status === 401) {
          return this.handle401Error(request, next);
        } else {
          return throwError(error);
        }
      }),
    );
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (request.url.indexOf('RenewToken') !== -1) {
      this.authenticationService.logout();
    }

    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authenticationService.refreshAccessToken().pipe(
        switchMap((token: any) => {
          this.isRefreshing = false;
          this.authenticationService.saveToken(token);
          this.refreshTokenSubject.next(token);
          return next.handle(this.addToken(request));
        }),
      );
    } else {
      return this.refreshTokenSubject.pipe(
        filter((token) => token != null),
        take(1),
        switchMap((user) => {
          this.authenticationService.saveToken(user);
          return next.handle(this.addToken(request));
        }),
      );
    }
  }

  private addToken(request: HttpRequest<any>): HttpRequest<any> {
    if (!this.router.url.includes('/admin')) {
      request = request.clone({
        setHeaders: {
          magicToken: environment.magicToken,
        },
      });
    }

    if (this.router.url.includes('/admin')) {
      request = request.clone({
        setHeaders: {
          WorkjetAppToken: environment.workjetAppToken,
        },
      });
    } else {
      request = request.clone({
        setHeaders: {
          WorkjetAppToken: environment.workjetAppToken,
        },
      });
    }

    const user = this.authenticationService.userValue;
    const isLoggedIn = user && user.isLoggedIn;
    // const isApiUrl = request.url.startsWith(environment.apiUrl);

    if (isLoggedIn) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${user.token}`,
        },
      });
    }
    return request;
  }
}
