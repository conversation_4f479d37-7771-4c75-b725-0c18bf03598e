import { After<PERSON>iew<PERSON>nit, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { CurrencyPipe } from '@angular/common';
import { SharedService } from '@core/services/shared.service';
import { AssetLendersResponse, Lenders } from '@core/models/response/asset-lenders.response';
import { AssetService } from '@core/services/asset.service';
import { AssetDetails, AssetDetailsResponse } from '@core/models/response/asset-details.response';
import {
  NbToastrService,
  NbCardModule,
  NbIconModule,
  NbSelectModule,
  NbSpinnerModule,
  NbUserModule,
  NbListModule,
  NbButtonModule,
} from '@nebular/theme';
import { DocumentService } from '@core/services/document.service';
import { DomSanitizer } from '@angular/platform-browser';
import { AssetTaskType, UpdatePublishStatus } from '@core/models/config';
import { RecentMessages } from '@core/models/response/asset-recent-messges.response';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgxMaskPipe } from 'ngx-mask';

@Component({
  selector: 'app-asset-updates',
  templateUrl: './asset-updates.component.html',
  styleUrls: ['./asset-updates.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbSpinnerModule,
    NbListModule,
    NbUserModule,
    NgxMaskPipe,
    NbButtonModule,
  ],
})
export class AssetUpdatesComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject();
  assetKeyDataId!: number;
  lenderId!: number;
  loading!: boolean;
  lenders!: Lenders[];
  assetDetails!: AssetDetails;
  managerReports: any[] = [];
  supportingDocuments: any[] = [];
  covenantReportingAll: any[] = [];
  covenantReportingCompleted: any[] = [];
  covenantReportingDue: any[] = [];
  updateTitle!: string;
  UpdatePublishStatus = UpdatePublishStatus;

  recentMessages!: RecentMessages[];

  constructor(
    private currencyPipe: CurrencyPipe,
    private assetService: AssetService,
    private sharedService: SharedService,
    private toast: NbToastrService,
    private documentService: DocumentService,
    public sanitizer: DomSanitizer,
  ) {}
  ngAfterViewInit(): void {
    // this.scrollToInvestor();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  public yAxisTickFormatting = (value: any) => `${this.currencyPipe.transform(value, 'USD', 'symbol', '0.0')}`;

  async ngOnInit(): Promise<void> {
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId || 0;
    this.getLenderList();
    this.getRecentLogs();
    this.getCovenantReports();
  }

  private getLenderList(): void {
    if (this.assetKeyDataId) {
      this.assetService
        .getLenders({
          assetId: this.assetKeyDataId,
          includeDraft: false,
        })
        .subscribe((data: AssetLendersResponse) => {
          if (data.success) {
            this.lenders = data.payload.filter(
              (lender) => lender.updatePublishStatusId === UpdatePublishStatus.Published,
            ) as Lenders[];
            if (!this.lenderId || this.lenderId === 0) {
              this.lenderId = this.lenders[0].id;
            }
            this.loadDashboard();
            this.loading = false;
          }
        });
    }
  }

  loadDashboard(): void {
    this.getAssetDetails();

    if (this.lenders) {
      this.updateTitle = this.lenders.find((x) => x.id === this.lenderId)?.updateTitle || '';
    }
  }

  private getAssetDetails(): void {
    if (this.assetKeyDataId) {
      this.assetService
        .getAssetDetails({
          assetId: this.assetKeyDataId,
          lenderId: this.lenderId,
        })
        .subscribe((data: AssetDetailsResponse) => {
          if (data.success) {
            this.managerReports = data.payload.assetDocuments.filter(
              (el: { category: string }) => el.category === 'Manager Reports',
            );
            this.supportingDocuments = data.payload.assetDocuments.filter(
              (el: { category: string }) => el.category === 'Supporting Documentation',
            );
            this.assetDetails = data.payload as AssetDetails;
            this.loading = false;
            this.filterCovenantReports();
          }
        });
    }
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  private getRecentLogs(): void {
    this.assetService.getRecentMessages(this.assetKeyDataId).subscribe((data: any) => {
      if (data.success) {
        this.recentMessages = data.payload;
      }
    });
  }

  private getCovenantReports(): void {
    this.assetService.getCovenantTasks(AssetTaskType.Covenant, this.assetKeyDataId).subscribe((data: any) => {
      if (data.success) {
        this.covenantReportingAll = data.payload;
        this.filterCovenantReports();
      }
    });
  }

  private filterCovenantReports(): void {
    if (this.assetDetails) {
      const currentDate = new Date(this.assetDetails.reportingMonth);
      let nextMonth = currentDate.getMonth() + 1;
      if (nextMonth == 12) {
        nextMonth = 0;
      }
      const completedThisMonth: any[] = [];
      const dueNextMonth: any[] = [];
      this.covenantReportingAll.forEach((task: any) => {
        if (task.completedBy !== 0) {
          if (new Date(task.completedByDate).getMonth() == currentDate.getMonth()) {
            completedThisMonth.push(task);
          } else if (new Date(task.dueDate).getMonth() == nextMonth) {
            dueNextMonth.push(task);
          }
        } else {
          if (new Date(task.dueDate).getMonth() == nextMonth) {
            dueNextMonth.push(task);
          }
        }
      });
      this.covenantReportingCompleted = completedThisMonth;
      this.covenantReportingDue = dueNextMonth;
    }
  }
}
