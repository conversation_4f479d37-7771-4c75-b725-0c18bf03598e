<p-skeleton *ngIf="!documents"></p-skeleton>

<div class="flex flex-wrap -mx-2" *ngIf="!isInvestor()">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        placeholder="Date"
        name="dateCreated"
        id="value"
        [selected]=""
        (selectedChange)="dt.filter($event, 'dateCreated', 'equals')"
      >
        <nb-option *ngFor="let dateFilter of dateFilterData" [value]="dateFilter.value">
          {{ dateFilter.name }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>
  </div>
</div>

<div *ngIf="documents">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="documents"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="investorId ? false : true"
        [rows]="investorId ? 200 : 10"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollHeight="flex"
        scrollHeight="flex"
        [globalFilterFields]="['id', 'description', 'investment', 'dateCreated', 'fileSize']"
        sortField="id"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 100px; max-width: 100px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 300px" [pSortableColumn]="'description'">
              <div>
                <div>Description</div>
                <p-sortIcon [field]="'description'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px; max-width: 175px" *ngIf="!isInvestor()" [pSortableColumn]="'investor'">
              <div>
                <div>Investor</div>
                <p-sortIcon [field]="'investor'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px; max-width: 175px" *ngIf="!isInvestor()" [pSortableColumn]="'investment'">
              <div>
                <div>Investment</div>
                <p-sortIcon [field]="'investment'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px; max-width: 150px" [pSortableColumn]="'dateCreated'">
              <div>
                <div>Date</div>
                <p-sortIcon [field]="'dateCreated'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 125px; max-width: 150px" [pSortableColumn]="'fileSize'">
              <div>
                <div>Size</div>
                <p-sortIcon [field]="'fileSize'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px; max-width: 150px" [hidden]="removeAction" *ngIf="isAdmin()">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-document>
          <tr>
            <td style="min-width: 100px; max-width: 100px">{{ document.id }}</td>

            <td style="min-width: 300px">
              <div class="text-blue-600 cursor-pointer" (click)="downloadFile(document)">
                {{ document.description }}
              </div>
            </td>
            <td style="min-width: 125px; max-width: 175px" *ngIf="!isInvestor()">{{ document.investor }}</td>
            <td style="min-width: 125px; max-width: 175px" *ngIf="!isInvestor()">{{ document.investment }}</td>
            <td style="min-width: 125px; max-width: 150px">{{ document.dateCreated | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 125px; max-width: 150px" class="text-blue-600">{{ document.fileSize }}KB</td>
            <td style="min-width: 125px; max-width: 150px" [hidden]="removeAction" *ngIf="isAdmin()">
              <button
                *ngIf="isAdmin()"
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                (click)="deleteDocument(document.documentKey, document.uploadedBy, investorId)"
                nbTooltip="Delete Document"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="trash-2-outline"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" [attr.colspan]="10">No documents yet.</td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
<div class="w-full px-2" [hidden]="removeAction">
  <div class="flex flex-wrap -mx-2" *ngIf="isAdmin() && this.investorId">
    <div class="w-6/12 px-2 my-[15px]">
      <button
        class="bg-velvet-700 hover:bg-velvet-600 text-white"
        *ngIf="true"
        nbButton
        status="default"
        type="button"
        (click)="addNewDocument()"
      >
        ADD NEW DOCUMENT
        <nb-icon icon="plus-outline"> </nb-icon>
      </button>
    </div>
    <!-- <div class="w-6/12 px-2 my-[15px]">
            <button class="float-right" (click)="next()" nbButton status="primary" style="min-width: 135px;">
                NEXT
            </button>
        </div> -->
  </div>
</div>
