<div class="wrapper {{ config.containerClass }}" id="c_{{ componentKey }}" *ngIf="otpForm?.controls && config">
  <input
    mask="0"
    [pattern]="config.allowNumbersOnly ? '\\d*' : ''"
    [type]="inputType"
    numberOnly
    [placeholder]="config.placeholder || ''"
    [disabledNumberOnly]="!config.allowNumbersOnly"
    style="width: &quot;16%&quot;; max-width: &quot;55px&quot;; height: &quot;74px&quot;"
    maxlength="1"
    class="otp-input {{ config.inputClass }}"
    autocomplete="off"
    *ngFor="let item of otpForm?.controls | keys; let i = index"
    [formControl]="otpForm.controls[item]"
    id="otp_{{ i }}_{{ componentKey }}"
    (keydown)="onKeyDown($event)"
    (keyup)="onKeyUp($event, i)"
  />
</div>
<br />
