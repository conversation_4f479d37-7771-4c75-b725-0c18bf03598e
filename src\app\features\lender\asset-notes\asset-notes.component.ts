import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { AssetTaskType } from '@core/models/config';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogRef,
  NbIconModule,
  NbProgressBarModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-asset-notes',
  templateUrl: './asset-notes.component.html',
  styleUrls: ['./asset-notes.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbIconModule,
    NbProgressBarModule,
    NbSpinnerModule,
    NbButtonModule,
    CkeditorComponent,
  ],
})
export class AssetNotesComponent implements OnInit {
  constructor(
    protected dialogRef: NbDialogRef<any>,
    private cdr: ChangeDetectorRef,
    private toast: NbToastrService,
    private documentService: DocumentService,
    private assetService: AssetService
  ) { }
  saveLoading = false;
  edit = false;
  checkListId!: number;

  @Input() uploadedDocuments: any[] = [];
  @Input() pendingDocuments: any[] = [];
  @Input() editorContent = '';
  @Input() deleteDocuments: any[] = [];
  @Input() editMode = false;
  @Input() originalContent: any;
  @Input() userId: any;
  @Input() taskId: any;
  @Input() noteId: any;
  @Input() taskType: any;
  progress = 0;
  editingNote = false;
  updating = false;
  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;
  public editorConfig: any = {};
  // Editor = ClassicEditor;

  ngOnInit(): void {
    this.setConfig();
    if (this.editMode && (this.editorContent || this.uploadedDocuments.length || this.pendingDocuments.length)) {
      this.editingNote = true;
    }
  }
  private setConfig(): void {
    // this.editorConfig = {
    //   toolbar: [
    //     ['Format',
    //       'Bold',
    //       'Italic',
    //       'Underline',
    //       'BulletedList',
    //       'NumberedList',
    //       'Blockquote',
    //       'JustifyLeft',
    //       'JustifyCenter',
    //       'JustifyRight',
    //       'JustifyBlock']
    //     , ['Undo',
    //       'Redo']
    //   ],
    //   extraPlugins: ['justify', 'editorplaceholder'],
    //   // editorplaceholder: 'Write something here.....',
    //   resize_enabled: false,
    //   toolbarLocation: 'bottom',
    //   readOnly: !this.editMode,
    // };
  }
  onSubmit(): void {
    if (this.updating) {
      return;
    } else {
      this.updating = true;
      this.saveNote();
    }
  }

  saveNote(): any {
    this.updateChecklistNote();
  }

  close(): void {
    this.dialogRef.close(false);
  }

  onFileDropped(event: any): void {
    const files = event;
    for (const file of files) {
      if (file.size > 20971520) {
        this.toast.warning('File size cannot be larger than 20mb', 'File Size Error');
        return;
      }
    }
    this.pendingDocuments.push(...event);
  }

  fileBrowseHandler(event: any): void {
    const files = event.target.files;
    for (const file of files) {
      if (file.size > 20971520) {
        this.toast.warning('File size cannot be larger than 20mb', 'File Size Error');
        return;
      }
    }
    this.pendingDocuments.push(...event.target.files);
  }

  clearDocuments(file: any, uploaded: boolean): void {
    if (uploaded) {
      this.deleteDocuments.push(file.id);
      const newUploadedDocuments = [...this.uploadedDocuments].filter((el) => el.id !== file.id);
      this.uploadedDocuments = newUploadedDocuments;
    } else {
      const newUploadedDocuments = [...this.pendingDocuments].filter((el) => el.name !== file.name);
      this.pendingDocuments = newUploadedDocuments;
      if (this.pendingDocuments.length == 0) {
        this.fileDropRef.nativeElement.value = '';
      }
      this.cdr.detectChanges();
    }
  }

  async downloadFile(file: any): Promise<void> {
    // this.toast.default(`Downloading started`, 'Success!', {
    // icon: 'download'
    // });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  updateChecklistNote() {
    const userNote = {
      taskType: this.taskType,
      id: this.noteId,
      taskId: this.taskId,
      noteContent: this.editorContent || '',
      userId: this.userId,
      documents: this.pendingDocuments || [],
      deletedDocuments: this.deleteDocuments || [],
      noteDocuments: this.uploadedDocuments.length > 0 ? this.uploadedDocuments : null,
    };
    if (this.noteId) {
      this.updateNote(userNote);
    } else {
      if (this.taskType == AssetTaskType.Covenant) {
        // Covenant Notes
        this.updateNote(userNote);
      } else {
        // Checklist Notes
        const updatingContent: any[] = [];
        this.originalContent.forEach((row: any) => {
          updatingContent.push(row);
          updatingContent.push(...row.checklist_structure);
        });
        const findIndex = updatingContent.findIndex((row) => row.id == this.taskId || row.newId == this.taskId);
        this.assetService.saveAssetChecklistContent(updatingContent).subscribe(
          (res: any) => {
            if (res.success) {
              const ids = res.payload.checklistContentId;
              const noteTaskId = ids[findIndex];
              userNote.taskId = noteTaskId;
              this.updateNote(userNote);
            } else if (res.error) {
              this.updating = false;
              this.toast.danger(res.error.message, 'Update Error!');
            }
          },
          (err: any) => {
            this.updating = false;
            this.toast.success(err.error.message, 'Notes Failed!');
          },
        );
      }
    }
  }

  updateNote(userNote: any) {
    this.assetService
      .saveAssetNote(userNote)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.toast.success('Note Uploaded Successfully!', 'Success!');

            setTimeout(() => {
              this.dialogRef.close({
                updated: true,
              });
            }, 200);
          }
        },
        (err: any) => {
          this.updating = false;
          this.toast.success(err.error.message, 'Notes Failed!');
        },
      );
  }
}
