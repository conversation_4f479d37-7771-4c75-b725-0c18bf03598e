<form class="w-full px-2" [formGroup]="overviewForm" (ngSubmit)="onSubmit()">
  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Facility Name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="facilityName"
        status="{{ submitted && f.facilityName.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.facilityName.errors" class="invalid-feedback">
        <div *ngIf="f.facilityName.errors.required">Facility Name is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong> Lender </strong>
        <strong class="text-lime required"> &nbsp; * </strong>
      </label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="lenderId"
        formControlName="lenderId"
        status="{{ submitted && f.lenderId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let lender of lenderData" [value]="lender.id" [disabled]="lender.disabled">
          {{ lender.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.lenderId.errors" class="invalid-feedback">
        <div *ngIf="f.lenderId.errors.required">Lender is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong> Lender Reference </strong>
        <strong class="text-lime required"> &nbsp; * </strong>
      </label>

      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="lenderReference"
        status="{{ submitted && f.lenderReference.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.lenderReference.errors" class="invalid-feedback">
        <div *ngIf="f.lenderReference.errors.required">Lender Reference is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Facility Type </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="facilityTypeId"
        formControlName="facilityTypeId"
        status="{{ submitted && f.facilityTypeId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let facility of facilityTypeData" [value]="facility.id" [disabled]="facility.disabled">
          {{ facility.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.facilityTypeId.errors" class="invalid-feedback">
        <div *ngIf="f.facilityTypeId.errors.required">Facility Type is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Borrower </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="borrower"
        status="{{ submitted && f.borrower.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.borrower.errors" class="invalid-feedback">
        <div *ngIf="f.borrower.errors.required">Borrower is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong>Originator </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="originator"
        status="{{ submitted && f.originator.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.originator.errors" class="invalid-feedback">
        <div *ngIf="f.originator.errors.required">Originator is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Facility Status </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="facilityStatusId"
        formControlName="facilityStatusId"
        status="{{ submitted && f.facilityStatusId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let status of statusData" [value]="status.id" [disabled]="status.disabled">
          {{ status.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.facilityStatusId.errors" class="invalid-feedback">
        <div *ngIf="f.facilityStatusId.errors.required">Facility Status is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong>Loan Manager </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="portfolioManager"
        status="{{ submitted && f.portfolioManager.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.portfolioManager.errors" class="invalid-feedback">
        <div *ngIf="f.portfolioManager.errors.required">Loan Manager is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label> <strong> Security Address </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder="Search address"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="securityAddress"
        status="{{ submitted && f.securityAddress.errors ? 'danger' : 'basic' }}"
        autocomplete="off"
        #search
        id="securityAddress"
        name="securityAddress"
      />
      <div *ngIf="submitted && f.securityAddress.errors" class="invalid-feedback">
        <div *ngIf="f.securityAddress.errors.required">Security Address is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong>Project Summary </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="projectSummary"
        status="{{ submitted && f.projectSummary.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.projectSummary.errors" class="invalid-feedback">
        <div *ngIf="f.projectSummary.errors.required">Project Summary is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2" *ngIf="assetPayload && assetPayload.imageUrl">
    <div class="lg:w-4/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <div class="image">
        <img class="image-asset" [src]="assetPayload.imageUrl" />
        <div class="overlay">
          <div class="text">
            <button
              type="button"
              shape="round"
              nbButton
              status="danger"
              size="small"
              (click)="deleteDocument(assetPayload.documentKey, assetPayload.uploaddedBy)"
            >
              <nb-icon icon="trash-2-outline"></nb-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2" *ngIf="!assetPayload || (assetPayload && !assetPayload.imageUrl)">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <app-add-asset-image (uploadDocument)="uploadDocument($event)" [uploadedDocuments]="uploadedDocuments">
      </app-add-asset-image>
      <nb-progress-bar style="width: 100%" *ngIf="progress" [value]="progress" status="primary">
        Uploading {{ progress }}%
      </nb-progress-bar>
    </div>
  </div>

  <div class="w-full px-2 my-[15px]">
    <button
      [disabled]="loading || overviewForm.disabled"
      class="float-right"
      [nbSpinner]="loading"
      nbButton
      status="primary"
      style="min-width: 135px"
    >
      <div *ngIf="!assetKeyDataId; then saveText; else updateText"></div>
      <ng-template #saveText> SAVE </ng-template>
      <ng-template #updateText> UPDATE </ng-template>
    </button>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>
