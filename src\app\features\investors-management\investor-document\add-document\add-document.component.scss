@use "themes" as *;

nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

p {
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 30px;
}

.title {
  font-weight: 600;
  font-size: 20px;
  line-height: 40px;
  color: #0a0a0a;
}

.outer-circle,
.inner-circle {
  left: 13px !important;
}

.file-container {
  width: 100%;
  padding: 0.5rem;
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--color-velvet-700);
  border-radius: 10px;
  height: 180px;

  p {
    font-size: 16px;
    line-height: 24px;
    color: #bfc6d0;
  }

  input {
    opacity: 0;
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  label {
    color: white;
    width: 183px;
    height: 44px;
    border-radius: 21.5px;
    background-color: #db202f;
    padding: 8px 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #38424c;
  }
}

.fileover {
  animation: shake 1s;
  animation-iteration-count: infinite;
  border-width: 3px;
}

.files-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin: 5px 0px;

  .single-file {
    display: flex;

    .delete {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .name {
      font-size: 14px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
    }

    .size {
      font-size: 12px;
      font-weight: 500;
      color: #a4a4a4;
      margin: 0;
      margin-bottom: 0.25rem;
    }

    .info {
      display: flex;
      padding: 8px;
      border: 2px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
      margin: 4px 3px;
    }
  }
}
