@use "themes" as *;

:host ::ng-deep {
  .cke-default .ck-editor {
    display: flex;
    flex-direction: column-reverse;
    gap: 16px;
  }

  .cke-default .ck-editor__editable {
    cursor: text;
    border: 2px solid #e7e9ec;
    padding: 12px;
    border-radius: 10px;
    order: 1; /* Ensures the editable area stays above the toolbar */
  }
  .cke-default .ck-sticky-panel__content {
    border: none !important;
  }
  .cke-default .ck-sticky-panel__content_sticky {
    position: absolute !important;
    top: unset !important;
    bottom: 0 !important;
    box-shadow: none;
  }

  .cke-default .ck-toolbar__items {
    gap: 4px;
  }
  .cke-default .ck-toolbar {
    padding: 0px;
  }
  .cke-default .ck-dropdown {
    margin: 0 !important;
  }
  .cke-default .ck-toolbar__items .ck-button {
    // background: #f3f3f3 !important;
    border-radius: 10px;
    padding: 10px;
    margin: 0 !important;
    border: 1px solid #d5d8dd !important;
    height: 40px;
  }

  .cke-full .ck-sticky-panel__content {
    border: 2px solid #dadada !important;
    border-radius: 3px 3px 0 0 !important;
  }

  .cke-full .ck-rounded-corners .ck.ck-editor__main > .ck-editor__editable {
    border: 2px solid #dadada !important;
    border-top: 0 !important;
    border-radius: 0 0 3px 3px !important;
  }

  .cke-full {
    .ck-editor {
      .ck-toolbar {
        background-color: #f9f9f9;
        .ck-dropdown {
          .ck-dropdown__panel {
            max-height: 300px;
            overflow-y: auto;
          }
        }
      }
      .ck-editor__main {
        .ck-content {
          min-height: 360px;
        }
      }
    }
  }

  .ck-content {
    border: 2px solid var(--color-grey-600) !important;
    min-height: 220px;
  }

  .ck {
    .ck-editor__main {
      ol {
        li {
          margin-left: 15px;
        }
      }
      ul {
        li {
          margin-left: 20px;
          .ck-list-bogus-paragraph {
            margin-left: -5px;
          }
        }
      }
    }
  }
}
