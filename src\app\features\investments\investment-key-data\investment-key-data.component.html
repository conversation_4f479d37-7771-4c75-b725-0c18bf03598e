<form class="w-full px-2" [formGroup]="financialsForm" (ngSubmit)="onSubmit()">
  <div class="w-full px-2 my-[15px]">
    <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Title </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="title"
        status="{{ submitted && f.title.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.title.errors" class="invalid-feedback">
        <div *ngIf="f.title.errors.required">Title is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Borrower/Issuer</strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="borrower"
        status="{{ submitted && f.borrower.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.borrower.errors" class="invalid-feedback">
        <div *ngIf="f.borrower.errors.required">Borrower/Issuer is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Investment Term</strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        shape="semi-round"
        mask="0*"
        maxlength="12"
        type="text"
        fieldSize="large"
        formControlName="term"
        status="{{ submitted && f.term.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.term.errors" class="invalid-feedback">
        <div *ngIf="f.term.errors.required">Investment Term is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong for="inStore">Investment Start Date</strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <nb-form-field>
        <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>

        <input
          type="text"
          shape="semi-round"
          nbInput
          fieldSize="large"
          fullWidth
          rInputMask="99/99/9999"
          id="startDate"
          name="startDate"
          formControlName="startDate"
          required
          [nbDatepicker]="dateTimePicker"
        />
      </nb-form-field>
      <nb-datepicker #dateTimePicker (dateChange)="dateChange($event)"></nb-datepicker>
      <div *ngIf="submitted && f.startDate.errors" class="invalid-feedback">
        <div *ngIf="f.startDate.errors.required">Investment Start Date is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Total Opportunity </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        shape="semi-round"
        prefix="$"
        mask="separator"
        thousandSeparator=","
        maxlength="12"
        type="text"
        fieldSize="large"
        formControlName="totalOpportunity"
        status="{{ submitted && f.totalOpportunity.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.totalOpportunity.errors" class="invalid-feedback">
        <div *ngIf="f.totalOpportunity.errors.required">Total Opportunity is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> LVR </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="lvr"
        status="{{ submitted && f.lvr.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.lvr.errors" class="invalid-feedback">
        <div *ngIf="f.lvr.errors.required">LVR is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Minimum Investment </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        shape="semi-round"
        prefix="$"
        mask="separator"
        maxlength="12"
        thousandSeparator=","
        type="text"
        fieldSize="large"
        formControlName="minInvestment"
        status="{{ submitted && f.minInvestment.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.minInvestment.errors" class="invalid-feedback">
        <div *ngIf="f.minInvestment.errors.required">Minimum Investment is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Investment Return</strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="investmentReturn"
        status="{{ submitted && f.investmentReturn.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.investmentReturn.errors" class="invalid-feedback">
        <div *ngIf="f.investmentReturn.errors.required">Investment Return is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label> <strong for="inStore"> Asset Type </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="assetType"
        status="{{ submitted && f.assetType.errors ? 'danger' : 'basic' }}"
      />

      <div *ngIf="submitted && f.assetType.errors" class="invalid-feedback">
        <div *ngIf="f.assetType.errors.required">Asset Type is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong for="inStore"> Investment Type </strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >

      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="investmentType"
        status="{{ submitted && f.investmentType.errors ? 'danger' : 'basic' }}"
      />

      <div *ngIf="submitted && f.investmentType.errors" class="invalid-feedback">
        <div *ngIf="f.investmentType.errors.required">Investment Type is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label> <strong for="inStore"> State </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        placeholder="Please Select"
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="stateId"
        formControlName="stateId"
      >
        <nb-option
          *ngFor="let entityType of investmentStateData"
          [value]="entityType.id"
          [disabled]="entityType.disabled"
        >
          {{ entityType.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.stateId.errors" class="invalid-feedback">
        <div *ngIf="f.stateId.errors.required">Investment Title is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Suburb </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="suburb"
        status="{{ submitted && f.suburb.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.suburb.errors" class="invalid-feedback">
        <div *ngIf="f.suburb.errors.required">Suburb is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label> <strong for="inStore"> Status </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        placeholder="Please Select"
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="statusId"
        formControlName="statusId"
      >
        <nb-option *ngFor="let entityType of statusData" [value]="entityType.id" [disabled]="entityType.disabled">
          {{ entityType.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.statusId.errors" class="invalid-feedback">
        <div *ngIf="f.statusId.errors.required">Status is required.</div>
      </div>
    </div>
  </div>

  <div class="w-full px-2 my-[15px]">
    <button
      class="float-right"
      [disabled]="financialsForm.disabled"
      [nbSpinner]="loading"
      nbButton
      status="primary"
      style="min-width: 135px"
    >
      UPDATE
    </button>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>
