import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { Component, ElementRef, EventEmitter, NgZone, OnInit, Output, ViewChild } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { RegisterUser } from '@core/models/auth';
import { FacilityStatus, TypeKey } from '@core/models/config';
import { AssetKeyDataRequest } from '@core/models/request/asset-key-data.request';
import { AssetResponse } from '@core/models/response/asset.response';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { PlaceService } from '@core/services/place.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbDialogService,
  NbIconModule,
  NbInputModule,
  NbProgressBarModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { map } from 'rxjs/operators';
import { AddAssetImageComponent } from './add-asset-image/add-asset-image.component';
@Component({
  selector: 'app-asset-key-data',
  templateUrl: './asset-key-data.component.html',
  styleUrls: ['./asset-key-data.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbSelectModule,
    NbInputModule,
    NbProgressBarModule,
    NbSpinnerModule,
    AddAssetImageComponent,
    NbButtonModule,
  ],
})
export class AssetKeyDataComponent implements OnInit {
  @Output() userChange = new EventEmitter<RegisterUser>();
  @Output() changeTab = new EventEmitter<boolean>();

  @ViewChild('search')
  public searchElementRef!: ElementRef;
  autocomplete!: google.maps.places.Autocomplete;
  address1Field!: HTMLInputElement;
  uploadedDocuments: any;
  overviewForm: UntypedFormGroup;
  loading = false;
  submitted = false;

  assetKeyDataId: number | undefined;
  userId: number | undefined;

  statusData: any;
  lenderData: any;
  facilityTypeData: any;
  assetPayload: any;
  progress: any;
  geoLocationState!: string;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private placeService: PlaceService,
    private ngZone: NgZone,
    private assetService: AssetService,
    private documentService: DocumentService,
    private dialogService: NbDialogService,
  ) {
    this.overviewForm = this.formBuilder.group({
      id: 0,
      facilityName: ['', Validators.required],
      lenderId: [null, Validators.required],
      lenderReference: ['', [Validators.required]],
      facilityTypeId: [null, [Validators.required]],
      borrower: ['', Validators.required],
      originator: ['', [Validators.required]],
      facilityStatusId: [null, [Validators.required]],
      portfolioManager: ['', [Validators.required]],
      securityAddress: ['', [Validators.required]],
      projectSummary: ['', [Validators.required]],
    });

    if (this.sharedService.isAdmin() || this.sharedService.isOriginatorManager()) {
      this.overviewForm.enable();
    } else {
      this.overviewForm.disable();
    }
  }

  ngOnInit(): void {
    this.assetKeyDataId = this.sharedService.getFormParamValue?.assetKeyDataId;
    this.userId = this.sharedService.getFormParamValue.userId;

    this.getKeyData(this.assetKeyDataId as number);

    this.getFacilityStatus();
    this.getLenderOrg();
    this.getFacilityType();

    this.placeService.api.then((maps) => {
      this.initAutocomplete(maps);
    });
  }

  private getLenderOrg(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_LenderOrg).subscribe((userData: any) => {
      if (userData.success) {
        this.lenderData = userData.payload;
      }
    });
  }

  private getFacilityType(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_FacilityType).subscribe((userData: any) => {
      if (userData.success) {
        this.facilityTypeData = userData.payload;
      }
    });
  }

  private getFacilityStatus(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_FacilityStatus).subscribe((response: any) => {
      if (response.success) {
        this.statusData = response.payload;
        if (!this.assetKeyDataId) {
          const [selectedStatus] = this.statusData.filter((status: any) => status.id === FacilityStatus.Draft);
          if (selectedStatus) {
            this.overviewForm.patchValue({
              facilityStatusId: FacilityStatus.Draft,
            });
          }
          this.overviewForm.controls.facilityStatusId.disable();
        }
      }
    });
  }

  private getKeyData(assetKeyDataId: number): void {
    if (assetKeyDataId) {
      this.assetService.getAsset(assetKeyDataId).subscribe((data: AssetResponse) => {
        if (data.success) {
          this.assetPayload = data.payload;
          this.userChange.emit(this.assetPayload);
          this.overviewForm.patchValue(this.assetPayload);
          this.geoLocationState = this.assetPayload.geoLocationState;
        }
      });
    }
  }

  get f() {
    return this.overviewForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;
    // stop here if form is invalid
    if (this.overviewForm.invalid) {
      return;
    }
    this.loading = true;

    // TODO: Impliment asset key data
    this.create();
    // if (this.userId && this.orgId) {
    //   this.updateUser();
    // }
  }

  private create(): void {
    const assetKeyDataPayload: AssetKeyDataRequest = {
      id: this.assetKeyDataId || undefined,
      facilityName: this.overviewForm.value.facilityName,
      lenderId: this.overviewForm.value.lenderId,
      lenderReference: this.overviewForm.value.lenderReference,
      facilityTypeId: this.overviewForm.value.facilityTypeId,
      borrower: this.overviewForm.value.borrower,
      originator: this.overviewForm.value.originator,
      facilityStatusId: this.overviewForm.getRawValue().facilityStatusId,
      portfolioManager: this.overviewForm.value.portfolioManager,
      securityAddress: this.overviewForm.value.securityAddress,
      projectSummary: this.overviewForm.value.projectSummary,
      geoLocationState: this.geoLocationState,
    };

    this.assetService.saveKeyData(assetKeyDataPayload).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            if (data.payload) {
              this.loading = false;
              if (data.payload.assetKeyDataId) {
                if (!this.uploadedDocuments) {
                  this.toastr.success('Saved Successfully.', 'Success!');
                  this.afterSave(data.payload.assetKeyDataId);
                } else {
                  this.assetKeyDataId = data.payload.assetKeyDataId;
                  this.uploadAssetDocument(this.uploadedDocuments);
                }
              }
            }
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  private afterSave(assetKeyDataId: number): void {
    this.sharedService.setFormParamValue({
      userId: this.sharedService.getUserIdValue.userId,
      assetKeyDataId,
      changeTab: this.assetPayload ? false : true,
    });
    this.router.navigate(['/asset/edit']);
    setTimeout(() => {
      if (this.assetPayload) {
        this.getKeyData(this.assetKeyDataId as number);
      } else {
        this.changeTab.emit(true);
      }
    }, 1000);
  }

  backtoList(): void {
    this.router.navigate(['/investors']);
  }

  initAutocomplete(maps: any): void {
    this.address1Field = document.querySelector('address') as HTMLInputElement;

    this.autocomplete = new maps.places.Autocomplete(this.searchElementRef.nativeElement, {
      componentRestrictions: { country: ['au'] },
      fields: ['address_components', 'geometry'],
      types: ['address'],
    });

    this.autocomplete.addListener('place_changed', (that) => {
      this.ngZone.run(() => {
        this.fillInAddress(that);
      });
    });
  }

  fillInAddress(that: any): void {
    const addressJson: any = {};
    const place: google.maps.places.PlaceResult = this.autocomplete.getPlace();

    for (const component of place.address_components as google.maps.GeocoderAddressComponent[]) {
      //  remove once typings fixed
      const componentType = component.types[0];
      addressJson[componentType] = component.short_name;
    }

    const address = `${addressJson.subpremise || ''} ${
      addressJson.street_number || ''
    } ${addressJson.route || ''}, ${addressJson.locality || ''} ${
      addressJson.administrative_area_level_1 || ''
    }, ${addressJson.postal_code || ''}, ${addressJson.country}`.trim();

    this.geoLocationState = addressJson.administrative_area_level_1;

    this.overviewForm.patchValue({
      securityAddress: address,
    });
  }

  uploadDocument(event: any): void {
    console.log(event);
    this.uploadedDocuments = event;
    if (this.assetKeyDataId && this.uploadedDocuments) {
      this.uploadAssetDocument(this.uploadedDocuments);
    }
  }

  uploadAssetDocument(files: any[]): void {
    const formData = new FormData();

    if (this.assetKeyDataId) {
      formData.append('AssetId', this.assetKeyDataId as any);
    }

    formData.append('IsPublic', true as any);

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.documentService
      .uploadAssetDocument(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.toastr.success('File Uploaded Successfully!', 'Success!');
            setTimeout(() => {
              if (this.assetKeyDataId) {
                this.afterSave(this.assetKeyDataId);
              }
              this.uploadedDocuments = null;
              this.progress = 0;
            }, 200);
          }
        },
        (err: any) => this.toastr.success(err.error.message, 'Failed!'),
      );
  }

  deleteDocument(documentKey: string, userId: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Delete Image!',
          message: 'Are you sure you want to delete?',
          yesButton: 'Delete',
          noButton: 'Cancel',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.documentService.deleteAssetDocument({ documentKey, userId }).subscribe(
            (response: any) => {
              if (response.success) {
                this.toastr.success('File deleted successfully.', 'Success!');
              } else {
                this.toastr.danger('Something went wrong please try again.', 'Error!');
              }
              this.getKeyData(this.assetKeyDataId as number);
            },
            (err: any) => {
              this.toastr.danger('Something went wrong please try again.', 'Error!');
            },
          );
        }
      });
  }
}
