@use "@nebular/theme/styles/globals" as *;
@use "themes" as *;
@use "responsive" as *;
@use "tailwindcss";

@include nb-install() {
  @include nb-theme-global();
}

/* You can add global styles to this file, and also import other style files */

.cdk-overlay-container {
  z-index: 11008 !important;
}

* {
  outline: none;
}

[hidden] {
  display: none !important;
}

.chart-legend .legend-labels {
  background: transparent !important;
  text-align: center !important;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

nb-spinner.status-basic {
  border-radius: 20px !important;
}

.chart-legend .legend-label-color {
  border-radius: 50% !important;
}

.invalid-feedback {
  color: #eb5757;
  text-align: left;
  font-size: 12px;
}

.sub-title {
  color: #7a8ba1;
}

.mb-4 {
  margin-bottom: 15px;
  margin-top: 15px;
}

label {
  margin-bottom: 5px;
  display: block;
  color: #7a8ba1;
  font-family: Lato;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: var(--color-velvet-700);
}

.popup-container {
  transition: transform 0.6s ease-in-out;
  transition: width 0.6s ease-in-out;
  background: #eef1f5;
  height: -webkit-fill-available;
  background-size: contain;
  background-position: right top;
  background-repeat: no-repeat;
  padding-right: 125px;
  border-radius: 19px;
}

.popup-container {
  h5,
  h6 {
    padding: 0 !important;
    margin: 11px 0px !important;
  }
}

// hr {
//   padding: 4px !important;
//   border: none !important;
// }

hr.solid {
  width: 100%;
  border-radius: 17px;
  border-width: 0px 0px 1px 0px;
  border-color: rgb(228 233 242);
}

.text-center {
  text-align: center;
}

.float-right {
  float: right;
}

.logo {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 300px;
  padding: 35px 0px;
}

.success-link-text {
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 30px;
  /* or 150% */

  text-align: center;

  /* Grey Body */

  color: var(--color-velvet-700);

  padding: 43px 0px;
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.invalid {
  display: block;
  font-size: 12px;
  color: #ff3d71;
}

.clickable {
  text-decoration: none;
}

.clickable:hover {
  cursor: pointer;
  text-decoration: none;
}

[nbprefix].flagIcon {
  border-right: 1px solid var(--color-grey-600);
  display: flex;
  // padding: 15px 15px;
}

nb-select nb-icon {
  right: 0.9em !important;
}

.admin-title {
  font-style: normal;
  font-weight: 300;
  font-size: 20px;
  line-height: 23px;
  color: nb-theme(color-primary-500);
}

.flag {
  padding-left: 1px;
  padding-top: 1px;
  height: 14px;
  width: 24px;
  margin-left: -11px;
  margin-right: 4px;
}

.code-text {
  font-size: 15px;
  color: var(--color-velvet-700);
  font-weight: 400;
  padding-left: 2px;
}

.dis-flex {
  display: flex;
  float: right;
  margin-top: 20px;
}

thead {
  background: #f4f5f8;
}

thead th {
  height: 40px !important;
}

.p-component-overlay {
  background-color: transparent !important;
}

.p-datatable-scrollable-header-box {
  padding-right: 5px !important;
}

.p-datatable .p-datatable-loading-icon {
  transition: all 0.3s;
  font-size: 2rem;
  color: #002c24;
}

.p-datatable .p-datatable-scrollable-header,
.p-datatable .p-datatable-scrollable-footer {
  background: white !important;
}

.p-multiselect .p-multiselect-label {
  display: flex !important;
  padding: 2px !important;
}

.p-multiselect {
  background: #ffffff !important;
  border: 1px solid #ced4da00 !important;
  border-radius: 20px !important;
  margin-right: 5px;
}

p-multiselect .info-container {
  margin-left: 2px !important;
}

.p-multiselect .p-multiselect-trigger {
  width: 14px !important;
  margin: 3px 5px 3px 1px;
}

.p-multiselect-panel {
  left: auto !important;
  right: 0 !important;
}

.p-paginator {
  .p-paginator-pages {
    .p-paginator-page {
      background-color: var(--color-velvet-700);
      color: var(--color-white);
      &:not(.p-disabled):not(.p-paginator-page-selected):hover {
        background-color: var(--color-velvet-600);
        color: var(--color-white);
      }
    }
    .p-paginator-page-selected {
      background-color: var(--color-lime);
    }
  }
  .p-dropdown,
  .p-paginator-first,
  .p-paginator-prev,
  .p-paginator-next,
  .p-paginator-last {
    background-color: var(--color-velvet-700);
    color: var(--color-white);
    &:not(.p-disabled):hover {
      background-color: var(--color-velvet-600);
      color: var(--color-white);
    }
  }
  .p-paginator-current {
    color: var(--color-velvet-700);
  }
}

body,
body .p-component,
.p-inputtext {
  font-family: "Lato" !important;
  font-size: 16px !important;
}

.p-datatable .p-datatable-thead > tr > th {
  font-weight: 700;
  background-color: white !important;
  color: var(--color-velvet-700) !important;
  font-size: 14px;
  min-height: 45px;
  align-items: flex-start !important;
  padding: 0rem 1rem !important;
}

.p-datatable .p-sortable-column:focus {
  box-shadow: none !important;
}

.p-datatable {
  .p-datatable-thead,
  .p-datatable-tbody {
    tr {
      display: flex;
      flex-wrap: nowrap;
      width: 100%;
      th {
        flex: 1;
        .p-sortable-column-icon {
          color: var(--color-lime);
        }
      }
      td {
        flex: 1;
      }
    }
  }
}
.p-datatable .p-datatable-thead > tr > th > div {
  display: flex !important;
  align-items: flex-start;
  justify-content: flex-start !important;
  gap: 2px;
}

.p-paginator {
  background: transparent !important;
  border: transparent !important;
}

.p-datatable .p-datatable-tbody > tr {
  color: var(--color-velvet-700);
  > td {
    font-size: 14px;
    word-break: break-word;
  }
}

.p-paginator .p-select {
  background: white !important;
  border-color: white !important;
  color: var(--color-velvet-700);
}

.p-datatable .p-paginator-bottom {
  border-width: 1px 0 1px 0 !important;
}

// .p-paginator {
//   padding: 20px 1rem !important;
// }

.request-detail {
  margin: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  width: 90%;
}

.deals-body {
  min-height: calc(100vh - 200px);
  max-height: calc(100vh - 200px);
  display: inline-table;
}

.art-scroll {
  max-height: calc(100vh - 392px);
  overflow-y: scroll;
  min-height: calc(100vh - 392px);
}

[nbsuffix] {
  margin-right: -40px;
}

.nb-form-field-control-with-suffix [nbInput].size-large {
  padding-right: 55px !important;
}

// .nb-form-field-suffix-large {
//   margin-left: -99px !important;
// }

.admin-login-padding {
  padding: 0px 30px;
}

.auth-container {
  width: 500px;
  border-radius: 20px !important;
  border: 1px solid #8cd5ff;
  box-shadow: 0px 0px 20px rgba(56, 182, 255, 0.2);
}

.art-date {
  font-size: 16px;
  line-height: 20px;
  color: #818589;
}

app-admin .p-datatable-scrollable {
  min-height: calc(100vh - 410px);
  overflow-y: scroll !important;
  max-height: calc(100vh - 410px);
  background: white;
}

app-base-layout .p-datatable-scrollable {
  overflow-y: scroll !important;
  height: calc(100vh - 350px) !important;
  background: white;
}

app-asset-details,
app-investment-documents,
app-investor-details {
  .p-datatable-scrollable {
    height: calc(100vh - 500px) !important;
    overflow-y: scroll !important;
    background: white;
  }
}

app-investment-dashboard {
  .p-datatable-scrollable {
    min-height: 345px !important;
    overflow-y: scroll !important;
    max-height: 345px !important;
    background: white;
  }
}

.mobile-logo {
  max-height: 50px;
}

.col-20-per {
  flex: 0 0 23%;
  max-width: 20%;
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

app-chat {
  // display: flex !important;
  // position: sticky !important;
  // float: right;
  // right: 0;
  // bottom: 0;
  // margin: 0px -20px -20px -20px;
  border-bottom-right-radius: 23px;
  box-shadow: none;
}

app-chat.status-basic .header {
  display: none;
}

app-chat-message.reply app-chat-message-text,
app-chat-message.not-reply app-chat-message-text {
  align-items: normal !important;
}

app-chat-message.reply app-chat-message-html,
app-chat-message.not-reply app-chat-message-html {
  align-items: normal !important;
}

app-chat.size-large {
  overflow-y: auto !important;
  height: calc(100vh - 260px);
}

app-chat-message.reply app-chat-message-text .text,
app-chat-message.not-reply app-chat-message-text .text {
  background: #f7f7f7;
  border-radius: 20px;
  color: var(--color-velvet-700);
  margin: 0px;
  position: relative;
  margin-left: 44px;
  margin-right: 44px;
  display: flex;
  min-width: 211px;
}

app-chat-message.not-reply app-chat-message-html .text {
  background: #ffffff;
  border-radius: 20px;
  border: 1px solid #e7e9ec;
  color: var(--color-velvet-700);
  margin: 0px;
  position: relative;
  margin-left: 44px;
  margin-right: 0px;
  display: flex;
  min-width: 211px;
}

app-chat-message.reply app-chat-message-html .text {
  background: #ffffff;
  border-radius: 20px;
  border: 1px solid #e7e9ec;
  color: var(--color-velvet-700);
  margin: 0px;
  position: relative;
  margin-left: 44px;
  margin-right: 44px;
  display: flex;
  min-width: 211px;
}

app-chat-message.not-reply app-chat-message-text .text {
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 0px !important;
}

app-chat-message.not-reply app-chat-message-html .text {
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 0px !important;
}

app-chat-message.reply app-chat-message-text .text {
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 0;
}

app-chat-message.reply app-chat-message-html .text {
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 0;
}

app-chat-message.not-reply app-chat-message-text .text:before {
  content: "";
  display: block;
  position: absolute;
  bottom: 0;
  border: 18px solid transparent;
  border-bottom-width: 0;
  left: -26px;
  border-right-color: #f7f7f7;
  border-right-width: 12px;
}

app-chat-message app-chat-message-text .text {
  padding: 1.2rem;
}

app-chat-message app-chat-message-html .text {
  padding: 1.2rem;
}

app-chat-message.reply app-chat-message-text .text:before {
  // transform: matrix(-1, 0, 0, 1, 0, 0);
  content: "";
  display: block;
  position: absolute;
  bottom: 0;
  border: 18px solid transparent;
  border-bottom-width: 0;
  right: -26px;
  border-left-color: #f7f7f7;
  border-left-width: 12px;
}

app-chat {
  app-chat-message.reply app-chat-message-text .text,
  app-chat-message.not-reply app-chat-message-text .text {
    background: white !important;
  }

  app-chat-message.reply app-chat-message-text .text:before {
    border-left-color: white !important;
  }

  app-chat-message.not-reply app-chat-message-text .text:before {
    border-right-color: white !important;
  }

  app-chat-message.reply app-chat-message-html .text:before {
    border-left-color: white !important;
  }

  app-chat-message.not-reply app-chat-message-html .text:before {
    border-right-color: white !important;
  }
}

app-chat-message {
  --user-medium-initials-text-font-size: 18px;

  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: row;
  float: left;
  position: relative;
}

app-chat-message.not-reply .avatar {
  left: 0;
  bottom: 0px;
  position: absolute;
}

app-chat-message.reply .avatar {
  right: 0;
  bottom: 0px;
  position: absolute;
}

app-chat-message app-chat-message-file .message-content-group {
  margin: 44px;
}

li.tab.active a {
  color: nb-theme(menu-item-active-text-color) !important;
}

path.custom {
  fill: nb-theme(color-primary-500) !important;
}

.details-view-header {
  margin: auto;
  width: 50%;
  padding: 22px 34px;
  border-bottom: var(--chat-divider-width) var(--chat-divider-style) var(--chat-divider-color);
}

.details-view-header-admin {
  margin: auto;
  width: 50%;
  padding: 20px;
  border-bottom: var(--chat-divider-width) var(--chat-divider-style) var(--chat-divider-color);
}

.details-view-header div.header,
.details-view-header-admin div.header {
  margin: auto;
}

.details-view-header p {
  margin-top: 32px;
}

.details-view-header .icon,
.details-view-header-admin .icon {
  margin: 3px 25px;
}

.not-active svg {
  fill: #979797 !important;
}

.user-bar > a .menu-title {
  background: gray !important;
}

.deal-status {
  padding: 5px 10px;
  border-radius: 5px;
  color: white;
  text-align: center;
}

.deal-status.Draft {
  color: var(--color-velvet-700);
  border: 1px solid var(--color-grey-600);
}

.context-menu span.menu-title {
  flex: unset !important;
}

.status-circle {
  background: black;
  height: 21px;
  width: 21px;
  border-radius: 50px;
  margin-right: 18px;
}

.status-icon {
  margin-right: 18px;
}

.editMode {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;

  [nbInput].size-large,
  nb-select.appearance-outline.size-large .select-button {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
    border-radius: 10px !important;
  }

  [nbprefix].flagIcon {
    border-right: 1px solid var(--color-grey-600);
    display: flex;
    padding: 6px 15px;
  }

  .nb-form-field-prefix-large,
  .nb-form-field-suffix-large {
    height: 30px !important;
  }

  // .display-flex {
  //   margin: 12px;
  // }

  // .file-container {
  //   margin: 15px auto !important;
  // }
}

.display-flex {
  display: flex;
}

.chart-data-title {
  font-size: 22px;
  line-height: 24px;
  color: var(--color-velvet-700);
}

.chat-message {
  color: var(--color-grey-600);
  font-size: 16px;
  padding-top: 10px;
  overflow-wrap: anywhere !important;
  word-break: break-word !important;
}

nb-layout-column.admin-back {
  background-image: url("../src/assets/images/admin-bg.png") !important;
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  min-height: 100%;
}

#cke_editor1,
#cke_editor2,
.cke_chrome {
  border: none !important;
}

.cke_editable {
  cursor: text;
  border: 2px solid #e7e9ec;
  padding: 12px;
  border-radius: 10px;
}

.cke_bottom {
  border-top: none !important;
  background: transparent !important;
}

.cke_contents {
  border: 2px solid var(--color-grey-600) !important;
  border-radius: 10px;
}

.cke_toolgroup a,
a.cke_combo_button {
  // background: #f3f3f3 !important;
  border-radius: 10px;
  padding: 10px !important;
}

a.cke_combo_button {
  // background: #f3f3f3 !important;
  border-radius: 10px;
  padding: 6px !important;
}

.cke_combo:after {
  border-right: none !important;
}

.cke_button_icon {
  zoom: 1.2 !important;
  margin-top: 0px !important;
  width: 17px !important;
  height: 15px !important;
}

a.cke_button.cke_button_on {
  border: 1px var(--color-lime) solid !important;
}

a.cke_button,
a.cke_combo_button {
  margin: 3px !important;
  border: 1px solid #d5d8dd !important;
  border-width: 1px !important;
}

ul li::marker {
  color: var(--color-velvet-700);
  font-size: 1.2em;
}

ul {
  list-style-type: square;
}

.p-dialog-mask {
  z-index: 999999 !important;
}

// .cke_combo_text {
//   width: 100px !important;
// }

// .cke_reset_all,
// .cke_reset_all *,
// .cke_reset_all a,
// .cke_reset_all textarea {
//   font-family: "roboto" !important;
//   font-size: 15px !important;
// }

// ul {
//   list-style-type: square !important;
// }

// .cke_editable ul li::marker {
//   color: red;
//   font-size: 1.5em;
// }

.p-sidebar-mask.p-component-overlay {
  z-index: 0 !important;
}

.advanced-pie-legend {
  .total-value {
    font-size: 36px !important;
    font-weight: 600 !important;
    line-height: 43px;
    color: var(--color-velvet-700) !important;
  }

  .total-label {
    color: var(--color-velvet-700) !important;
  }

  .legend-items-container .legend-items .legend-item .item-value {
    margin-bottom: 10px;
    margin-top: 0px !important;
    color: var(--color-velvet-700) !important;
    font-weight: 600 !important;
  }

  .legend-items-container .legend-items {
    display: contents;
  }

  .legend-items-container .legend-items .legend-item .item-percent,
  .legend-items-container .legend-items .legend-item .item-label {
    font-size: 16px !important;
  }

  // .legend-items-container .legend-items .legend-item .item-color {
  //   height: 60px !important;
  // }
}

nb-spinner {
  z-index: 1039 !important;
}

a.icon-container {
  display: contents;
}

.user-title {
  overflow-wrap: anywhere !important;
}

.title h5 {
  margin: 20px 12px !important;
  content: "this is check";
  font-family: var(--font-bitter);
}

.show-on-mobile {
  display: none;
}

.investment-card {
  width: 470px;
}

.investment-button {
  padding: 0px 30px;
}

nb-accordion-item {
  margin: 5px 5px !important;
}

nb-accordion-item-header.accordion-item-header-expanded {
  color: var(--color-lime);
}

.accordion-icon {
  width: 28px;
  display: inline;
}

svg.svg-inline--fa.fa-file-invoice-dollar.fa-w-12 {
  font-size: 7px;
  width: 40px;
  height: 30px;
  padding-top: 5px;
}

@media only screen and (max-width: 768px) {
  .search-filter {
    width: 100% !important;
  }
}

.search-filter {
  width: 396px;
}

app-add-user,
app-investor-overview {
  .p-select-dropdown {
    display: none !important;
  }

  .p-select {
    border: none !important;
    background: transparent;
    &.p-disabled {
      background: transparent;
    }
    .p-select-label-empty {
      color: var(--color-velvet-700);
      opacity: 1;
    }
  }
}

/* Let's get this party started */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px transparent;
  box-shadow: inset 0 0 6px transparent;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background-color: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  -webkit-border-radius: 3px;
  border-radius: 3px;
  background: #b3b5b8;
  -webkit-box-shadow: inset 0 0 6px #b3b5b8;
  box-shadow: inset 0 0 6px #b3b5b8;
}

nb-spinner.status-basic {
  background-color: rgb(143 155 179 / 0%) !important;
}

.document-popup {
  margin: 22px 50px;
  width: 600px;
}

app-asset-updates {
  app-chat.size-large {
    overflow-y: auto !important;
    max-height: 325px !important;
  }
}

.pi-sort-alt:before {
  content: "" !important;
}

.pi-sort-amount-up-alt:before {
  content: "\e91c" !important;
}

.pi-sort-amount-down:before {
  content: "\e919" !important;
}

app-asset-dashboard {
  .advanced-pie,
  .total-value,
  .total-label {
    display: none !important;
  }

  .advanced-pie-legend .legend-items-container {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;

    .legend-items .legend-item {
      margin: 20px 35px !important;

      .item-percent {
        display: none;
      }
    }
  }
}

.p-buttonset {
  .p-button:first-of-type {
    border-top-left-radius: 22px;
    border-bottom-left-radius: 22px;
  }

  .p-button:last-of-type {
    border-top-right-radius: 22px;
    border-bottom-right-radius: 22px;
  }
}

.p-selectbutton .p-button.p-highlight {
  background: nb-theme(color-primary-500) !important;
  border-color: nb-theme(color-primary-500) !important;
  color: #ffffff;
}

.select-facility {
  min-width: 140px;
  margin-right: 6px;
}

.recent-msg {
  display: flex;

  .user-title {
    color: (--color-grey-600);
  }
}
.p-overlay-mask {
  background: transparent !important;
}
nb-layout-header nav {
  width: 100%;
  background-color: var(--color-mist);
}

.p-select-list-container {
  .p-select-option {
    &.p-select-option-selected {
      background-color: var(--color-lime);
      color: var(--color-white);
      &.p-focus {
        background-color: var(--color-lime);
        color: var(--color-white);
      }
    }
    &:not(.p-select-option-selected) {
      &:not(.p-disabled) {
        &.p-focus {
          background-color: var(--color-lime);
        }
      }
    }
  }
}
