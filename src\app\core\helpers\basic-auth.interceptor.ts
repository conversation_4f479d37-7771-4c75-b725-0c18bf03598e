import { Injectable } from '@angular/core';
import { <PERSON>ttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from 'src/environments/environment';
import { AuthenticationService } from '@core/services/authentication.service';
import { Router } from '@angular/router';

@Injectable()
export class BasicAuthInterceptor implements HttpInterceptor {
  constructor(
    private authenticationService: AuthenticationService,
    private router: Router,
  ) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    // add header with basic auth credentials if user is logged in and request is to the api url

    request = this.addToken(request);

    return next.handle(request);
  }

  private addToken(request: HttpRequest<any>): HttpRequest<any> {
    if (
      !this.router.url.includes('/login') &&
      !this.router.url.includes('/reset-password') &&
      !this.router.url.includes('/create-password')
    ) {
      request = request.clone({
        setHeaders: {
          magicToken: environment.magicToken,
        },
      });
    }

    request = request.clone({
      setHeaders: {
        WorkjetAppToken: environment.workjetAppToken,
      },
    });

    const user = this.authenticationService.userValue;
    const isLoggedIn = user && user.isLoggedIn;
    // const isApiUrl = request.url.startsWith(environment.apiUrl);

    if (isLoggedIn) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${user.token}`,
        },
      });
    }
    return request;
  }
}
