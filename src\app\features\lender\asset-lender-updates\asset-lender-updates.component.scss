nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

p {
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 30px;
}

.title {
  font-weight: 600;
  font-size: 20px;
  line-height: 40px;
  color: #0a0a0a;
}

.outer-circle,
.inner-circle {
  left: 13px !important;
}

.calendarModal {
  width: 100%;
  display: inline-grid;
}

.calendarInput {
  border-color: red !important;
}

:host ::ng-deep {
  .invalidCalendar {
    .p-calendar .p-inputtext {
      border-color: red !important;
      background-color: #e7eceb !important;
    }
  }
}
