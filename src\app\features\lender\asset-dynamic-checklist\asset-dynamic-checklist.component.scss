@use "themes" as *;

.user-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  width: 100%;
  border: 1px solid #d5d8dd;
  border-radius: 10px;
}

nb-list-item {
  margin: 5px;
  cursor: pointer;
}

.text-right {
  text-align: right;
}

.float-right {
  margin-left: 12px;
}

.files-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 5px 0px;
  padding-left: 5px;
  padding-right: 10px;

  .single-file {
    display: flex;

    .delete {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .attachment {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .name {
      font-size: 12px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
    }

    .size {
      font-size: 12px;
      font-weight: 500;
      color: #a4a4a4;
      margin: 0;
      margin-bottom: 0.25rem;
    }

    .info {
      display: flex;
      padding: 8px;
      // padding-left: 3px;
      // padding-right: 3px;
      border: 1px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
      margin: 4px 3px;
      cursor: pointer;
    }
  }
}

.buttonRow {
  margin: 40px;
  justify-content: space-between;
}

.button {
  min-width: 176px;
  min-height: 50px;
  margin-right: 16px;
}

nb-card-footer {
  border-top: 0px !important;
}

.p-datatable .p-datatable-header {
  background: white !important;
  color: rgba(143, 155, 179, 1) !important;
  padding: 1rem 1rem !important;
  border: 0px white !important;
  border-width: 0 !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  line-height: 24px !important;
}

nb-icon {
  font-size: 20px !important;
}

:host ::ng-deep {
  .row {
    display: flow-root;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  ::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
    /* make scrollbar transparent */
  }

  .p-datatable .p-datatable-tbody > tr > td {
    height: 78px !important;
  }

  .p-datatable .p-datatable-header {
    background: white !important;
    color: rgba(143, 155, 179, 1) !important;
    padding: 1rem 0rem !important;
    border: 0px white !important;
    border-width: 0 !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    line-height: 24px !important;
  }
}
