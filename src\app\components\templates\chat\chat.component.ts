/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  AfterViewInit,
  Component,
  ContentChild,
  ContentChildren,
  ElementRef,
  EventEmitter,
  HostBinding,
  Input,
  OnChanges,
  Output,
  QueryList,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { convertToBoolProperty, NbBooleanInput } from '@core/helpers/helpers';
import { NbComponentOrCustomStatus, NbComponentSize, NbStatusService } from '@nebular/theme';

import { NbChatFormComponent } from './chat-form.component';
import { NbChatMessageComponent } from './chat-message.component';

/**
 * Conversational UI collection - a set of components for chat-like UI construction.
 *
 * Main features:
 * - different message types support (text, image, file, file group, map, etc)
 * - drag & drop for images and files with preview
 * - different UI styles
 * - custom action buttons (coming soon)
 *
 * Here's a complete example build in a bot-like app. Type `help` to be able to receive different message types.
 * Enjoy the conversation and the beautiful UI.
 * @stacked-example(Showcase, chat/chat-showcase.component)
 *
 * Basic chat configuration and usage:
 * ```ts
 * <app-chat title="Nebular Conversational UI">
 *       <app-chat-message *ngFor="let msg of messages"
 *                        [type]="msg.type"
 *                        [message]="msg.text"
 *                        [reply]="msg.reply"
 *                        [sender]="msg.user.name"
 *                        [date]="msg.date"
 *                        [files]="msg.files"
 *                        [quote]="msg.quote"
 *                        [latitude]="msg.latitude"
 *                        [longitude]="msg.longitude"
 *                        [avatar]="msg.user.avatar">
 *   </app-chat-message>
 *
 *   <app-chat-form (send)="sendMessage($event)" [dropFiles]="true">
 *   </app-chat-form>
 * </app-chat>
 * ```
 * ### Installation
 *
 * Import `NbChatModule` to your feature module.
 * ```ts
 * @NgModule({
 *   imports: [
 *     // ...
 *     NbChatModule,
 *   ],
 * })
 * export class PageModule { }
 * ```
 *
 * If you need to provide an API key for a `map` message type (which is required by Google Maps)
 * you may use `NbChatModule.forRoot({ ... })` call if this is a global app configuration
 * or `NbChatModule.forChild({ ... })` for a feature module configuration:
 *
 * ```ts
 * @NgModule({
 *   imports: [
 *     // ...
 *     NbChatModule.forRoot({ messageGoogleMapKey: 'MAP_KEY' }),
 *   ],
 * })
 * export class AppModule { }
 * ```
 *
 * ### Usage
 *
 * There are three main components:
 * ```ts
 * <app-chat>
 * </app-chat> // chat container
 *
 * <app-chat-form>
 * </app-chat-form> // chat form with drag&drop files feature
 *
 * <app-chat-message>
 * </app-chat-message> // chat message, available multiple types
 * ```
 *
 * Two users conversation showcase:
 * @stacked-example(Conversation, chat/chat-conversation-showcase.component)
 *
 * Chat UI is also available in different colors by specifying a `[status]` input:
 *
 * @stacked-example(Colored Chat, chat/chat-colors.component)
 *
 * Also it is possible to configure sizes through `[size]` input:
 *
 * @stacked-example(Chat Sizes, chat/chat-sizes.component)
 *
 * @styles
 *
 * chat-background-color:
 * chat-border:
 * chat-border-radius:
 * chat-shadow:
 * chat-padding:
 * chat-scrollbar-color:
 * chat-scrollbar-background-color:
 * chat-scrollbar-width:
 * chat-text-color:
 * chat-text-font-family:
 * chat-text-font-size:
 * chat-text-font-weight:
 * chat-text-line-height:
 * chat-header-text-font-family:
 * chat-header-text-font-size:
 * chat-header-text-font-weight:
 * chat-header-text-line-height:
 * chat-tiny-height:
 * chat-small-height:
 * chat-medium-height:
 * chat-large-height:
 * chat-giant-height:
 * chat-basic-background-color:
 * chat-basic-text-color:
 * chat-primary-background-color:
 * chat-primary-text-color:
 * chat-success-background-color:
 * chat-success-text-color:
 * chat-info-background-color:
 * chat-info-text-color:
 * chat-warning-background-color:
 * chat-warning-text-color:
 * chat-danger-background-color:
 * chat-danger-text-color:
 * chat-control-background-color:
 * chat-control-text-color:
 * chat-divider-color:
 * chat-divider-style:
 * chat-divider-width:
 * chat-message-background:
 * chat-message-text-color:
 * chat-message-reply-background-color:
 * chat-message-reply-text-color:
 * chat-message-avatar-background-color:
 * chat-message-sender-text-color:
 * chat-message-quote-background-color:
 * chat-message-quote-text-color:
 * chat-message-file-text-color:
 * chat-message-file-background-color:
 */
@Component({
  selector: 'app-chat',
  styleUrls: ['./chat.component.scss'],
  template: `
    <div class="header">{{ title }}</div>
    <div (scroll)="onScroll($event)" class="scrollable overflow-auto flex-1" #scrollable>
      <div class="py-[1rem] px-[1.25rem] overflow-y-auto overflow-x-hidden flex flex-shrink-0 flex-col">
        <ng-content select="app-chat-message"></ng-content>
        <p class="my-[1rem] text-center" *ngIf="!messages?.length">
          {{ noMessagesPlaceholder }}
        </p>
      </div>
    </div>
    <div class="form">
      <div class="py-[1rem] px-[1.25rem] flex flex-col border-t-1 border-t-[var(--chat-divider-color)]">
        <ng-content select="app-chat-form"></ng-content>
      </div>
    </div>
  `,
  standalone: true,
  imports: [CommonModule],
})
export class NbChatComponent implements OnChanges, AfterContentInit, AfterViewInit {
  /**
   * Scroll chat to the bottom of the list when a new message arrives
   */
  @Input()
  get scrollBottom(): boolean {
    return this._scrollBottom;
  }
  set scrollBottom(value: boolean) {
    this._scrollBottom = convertToBoolProperty(value);
  }

  @Input()
  get scrollTop(): boolean {
    return this._scrollTop;
  }
  set scrollTop(value: boolean) {
    this._scrollTop = convertToBoolProperty(value);
  }

  constructor(protected statusService: NbStatusService) {}

  @HostBinding('class.size-tiny')
  get tiny(): boolean {
    return this.size === 'tiny';
  }

  @HostBinding('class.size-small')
  get small(): boolean {
    return this.size === 'small';
  }

  @HostBinding('class.size-medium')
  get medium(): boolean {
    return this.size === 'medium';
  }

  @HostBinding('class.size-large')
  get large(): boolean {
    return this.size === 'large';
  }

  @HostBinding('class.size-giant')
  get giant(): boolean {
    return this.size === 'giant';
  }

  @HostBinding('class.status-primary')
  get primary(): boolean {
    return this.status === 'primary';
  }

  @HostBinding('class.status-success')
  get success(): boolean {
    return this.status === 'success';
  }

  @HostBinding('class.status-info')
  get info(): boolean {
    return this.status === 'info';
  }

  @HostBinding('class.status-warning')
  get warning(): boolean {
    return this.status === 'warning';
  }

  @HostBinding('class.status-danger')
  get danger(): boolean {
    return this.status === 'danger';
  }

  @HostBinding('class.status-basic')
  get basic(): boolean {
    return this.status === 'basic';
  }

  @HostBinding('class.status-control')
  get control(): boolean {
    return this.status === 'control';
  }

  @HostBinding('class')
  get additionalClasses(): string[] {
    if (this.statusService.isCustomStatus(this.status)) {
      return [this.statusService.getStatusClass(this.status)] as string[];
    }
    return [];
  }
  static ngAcceptInputType_scrollBottom: NbBooleanInput;
  static ngAcceptInputType_scrollTop: NbBooleanInput;

  @Output() messageChange = new EventEmitter<boolean>();

  @Input() title?: string;

  /**
   * Chat size, available sizes:
   * `tiny`, `small`, `medium`, `large`, `giant`
   */
  @Input() size?: NbComponentSize;

  /**
   * Chat status color (adds specific styles):
   * `basic` (default), `primary`, `success`, `info`, `warning`, `danger`, `control`.
   */
  @Input() status: NbComponentOrCustomStatus = 'basic';

  @Input() noMessagesPlaceholder = 'No notes yet.';
  protected _scrollBottom = true;
  protected _scrollTop = false;

  @ViewChild('scrollable') scrollable!: ElementRef;
  @ContentChildren(NbChatMessageComponent)
  messages!: QueryList<NbChatMessageComponent>;
  @ContentChild(NbChatFormComponent) chatForm?: NbChatFormComponent;

  ngOnChanges(changes: SimpleChanges) {
    if ('status' in changes) {
      this.updateFormStatus();
    }
  }

  ngAfterContentInit() {
    this.updateFormStatus();
  }

  ngAfterViewInit() {
    this.messages.changes.subscribe((messages) => {
      this.messages = messages;
      this.updateView();
    });

    this.updateView();
  }

  updateView() {
    if (this.scrollTop) {
      this.scrollListTop();
    } else if (this.scrollBottom) {
      this.scrollListBottom();
    }
  }

  scrollListBottom() {
    this.scrollable.nativeElement.scrollTop = this.scrollable.nativeElement.scrollHeight;
  }

  scrollListTop() {
    this.scrollable.nativeElement.scrollTop = 0;
  }

  protected updateFormStatus(): void {
    if (this.chatForm) {
      this.chatForm.setStatus(this.status);
    }
  }

  onScroll(event: any): void {
    if (event.target.scrollTop === 0) {
      this.messageChange.emit(true);
    }
  }
}
