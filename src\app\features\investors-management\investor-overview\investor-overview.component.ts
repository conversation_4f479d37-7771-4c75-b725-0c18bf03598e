import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, NgZone, OnInit, Output, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { RegisterUser } from '@core/models/auth';
import { InvestorStatus, TypeKey } from '@core/models/config';
import { AuthenticationService } from '@core/services/authentication.service';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { PlaceService } from '@core/services/place.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbCheckboxModule,
  NbDialogService,
  NbFormFieldModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxMaskDirective } from 'ngx-mask';
import { SelectModule } from 'primeng/select';
import { SearchInvestorsComponent } from '../search-investors/search-investors.component';
@Component({
  selector: 'app-investor-overview',
  templateUrl: './investor-overview.component.html',
  styleUrls: ['./investor-overview.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NbCheckboxModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    NbAlertModule,
    NbInputModule,
    NbFormFieldModule,
    NbSpinnerModule,
    NbButtonModule,
    NgxMaskDirective,
    SelectModule,
  ],
})
export class InvestorOverviewComponent implements OnInit {
  @Output() userChange = new EventEmitter<RegisterUser>();
  @Output() changeTab = new EventEmitter<boolean>();

  @ViewChild('search')
  public searchElementRef!: ElementRef;
  autocomplete!: google.maps.places.Autocomplete;
  address1Field!: HTMLInputElement;

  overviewForm: UntypedFormGroup;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';
  title = 'Edit Client';
  entityTypeData: any;

  selectedCountry: any;

  pattern = {
    X: {
      pattern: new RegExp('[#+(0-9)]'),
    },
    0: {
      pattern: new RegExp('[(0-9)]'),
    },
  };

  investorId: number | undefined;
  userId: number | undefined;
  existingUserId: number | undefined;
  statusData: any;
  investorPayload: any;

  countries = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private investmentService: InvestmentService,
    private investorsService: InvestorsService,
    private placeService: PlaceService,
    private ngZone: NgZone,
    private dialogService: NbDialogService,
    private authenticationService: AuthenticationService,
  ) {
    this.overviewForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,60}$')]],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      mobile: ['', [Validators.required]],
      countryCode: ['', Validators.required],
      address: ['', [Validators.required]],
      entityName: ['', [Validators.required]],
      entityTypeId: [null, [Validators.required]],
      isExisting: [false, [Validators.required]],
      statusId: [null, [Validators.required]],
    });

    if (this.sharedService.isAdmin()) {
      this.overviewForm.enable();
    } else {
      this.overviewForm.disable();
    }
  }

  ngOnInit(): void {
    this.countries = this.sharedService.getMobileCodes().default;
    this.selectedCountry = this.countries.find((ele: any) => ele.dial_code === '+61');

    this.investorId = this.sharedService.getFormParamValue.investorId;
    this.userId = this.sharedService.getFormParamValue.userId;

    this.getInvestor(this.investorId as number);

    this.getInvestorStatus();
    this.getEntityType();

    this.returnUrl = this.route.snapshot.queryParams.returnUrl || '/';

    this.placeService.api.then((maps) => {
      this.initAutocomplete(maps);
    });
  }

  private getEntityType(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.EntityType,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.entityTypeData = userData.payload;
        }
      });
  }

  private getInvestorStatus(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.InvestorStatus,
      })
      .subscribe((response: any) => {
        if (response.success) {
          this.statusData = response.payload;
          if (!this.investorId) {
            const [selectedStatus] = this.statusData.filter((status: any) => status.id === InvestorStatus.Pending);
            if (selectedStatus) {
              this.overviewForm.patchValue({
                statusId: InvestorStatus.Pending,
              });
            }
            this.overviewForm.controls.statusId.disable();
          }
        }
      });
  }

  private getInvestor(investorId: number): void {
    if (investorId) {
      this.investorsService.getInvestor(investorId).subscribe((data: any) => {
        if (data.success) {
          this.investorPayload = data.payload;
          this.userChange.emit(this.investorPayload);
          this.overviewForm.patchValue(this.investorPayload);

          if (this.investorPayload.countryCode) {
            this.selectedCountry = this.countries.find(
              (ele: any) => ele.dial_code === this.investorPayload.countryCode,
            );
          }

          if (this.investorPayload.isExisting) {
            this.overviewForm.controls.firstName.disable();
            this.overviewForm.controls.lastName.disable();
            this.overviewForm.controls.email.disable();
            this.overviewForm.controls.mobile.disable();
            this.overviewForm.controls.countryCode.disable();
          }
        }
      });
    }
  }

  toggle(checked: boolean): void {
    if (checked) {
      this.dialogService
        .open(SearchInvestorsComponent, {
          context: {},
          autoFocus: false,
        })
        .onClose.subscribe((user: any) => {
          if (user) {
            this.overviewForm.patchValue({
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              mobile: user.mobile.replace('+61', ''),
            });
            this.existingUserId = user.userId;
            this.overviewForm.controls.firstName.disable();
            this.overviewForm.controls.lastName.disable();
            this.overviewForm.controls.email.disable();
            this.overviewForm.controls.mobile.disable();
            this.overviewForm.controls.countryCode.disable();
          } else {
            this.existingUserId = 0;
            this.resetFormControls();
            if (this.investorId) {
              this.getInvestor(this.investorId);
            } else {
              this.overviewForm.patchValue({
                isExisting: false,
              });
            }
          }
        });
    } else {
      this.resetFormControls();
      this.existingUserId = 0;
    }
    this.overviewForm.patchValue({
      isExisting: checked,
    });
  }

  private resetFormControls(): void {
    this.existingUserId = this.userId;
    this.overviewForm.patchValue({
      firstName: '',
      lastName: '',
      email: '',
      mobile: '',
    });
    this.overviewForm.controls.firstName.enable();
    this.overviewForm.controls.lastName.enable();
    this.overviewForm.controls.email.enable();
    this.overviewForm.controls.mobile.enable();
    this.overviewForm.controls.countryCode.enable();
  }

  get f() {
    return this.overviewForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.overviewForm.invalid) {
      return;
    }

    this.loading = true;

    this.createNewInvestors();
  }

  private createNewInvestors(): void {
    const registerData: RegisterUser = {
      entityName: this.overviewForm.value.entityName,
      entityTypeId: this.overviewForm.value.entityTypeId,
      email: this.overviewForm.getRawValue().email,
      mobile: this.overviewForm.getRawValue().mobile,
      countryCode: this.selectedCountry.dial_code, // this.overviewForm.getRawValue().countryCode.dial_code,
      address: this.overviewForm.value.address,
      host: window.location.host,
      isInvestor: true,
      investorId: this.investorId,
      isExisting: this.overviewForm.value.isExisting,
      statusId: this.investorId ? this.overviewForm.value.statusId : InvestorStatus.Pending,
      userId: this.existingUserId || this.userId,
      profileAttributes: [
        {
          key: 'firstName',
          value: this.overviewForm.getRawValue().firstName,
        },
        {
          key: 'lastName',
          value: this.overviewForm.getRawValue().lastName,
        },
      ],
    };

    if (this.overviewForm.value.isExisting) {
      delete registerData.profileAttributes;
      delete registerData.mobile;
      registerData.userId = this.existingUserId || this.userId;
    }

    this.authenticationService.registerWithProfile(registerData).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            if (data.payload) {
              this.loading = false;
              this.toastr.success('Saved Successfully', 'Success!');
              if (data.payload.investorId) {
                this.sharedService.setFormParamValue({
                  investorId: data.payload.investorId,
                  userId: data.payload.userId,
                  changeTab: this.investorPayload ? false : true,
                });
                this.router.navigate(['/investor/edit']);
                if (this.investorPayload) {
                  this.getInvestor(this.investorId as number);
                } else {
                  this.changeTab.emit(true);
                }
              }
            }
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  backtoList(): void {
    this.router.navigate(['/investors']);
  }

  initAutocomplete(maps: any): void {
    this.address1Field = document.querySelector('address') as HTMLInputElement;

    this.autocomplete = new maps.places.Autocomplete(this.searchElementRef.nativeElement, {
      componentRestrictions: { country: ['au'] },
      fields: ['address_components', 'geometry'],
      types: ['address'],
    });

    this.autocomplete.addListener('place_changed', (that) => {
      this.ngZone.run(() => {
        this.fillInAddress(that);
      });
    });
  }

  fillInAddress(that: any): void {
    const addressJson: any = {};
    const place: google.maps.places.PlaceResult = this.autocomplete.getPlace();

    for (const component of place.address_components as google.maps.GeocoderAddressComponent[]) {
      //  remove once typings fixed
      const componentType = component.types[0];
      addressJson[componentType] = component.long_name;
    }

    const address = `${addressJson.subpremise || ''} ${
      addressJson.street_number || ''
    } ${addressJson.route || ''}, ${addressJson.locality || ''} ${
      addressJson.administrative_area_level_1 || ''
    }, ${addressJson.postal_code || ''}, ${addressJson.country}`.trim();
    this.overviewForm.patchValue({
      address,
    });
  }

  getCode(code: string): string {
    return this.sharedService.codeToFlag(code);
  }
}
