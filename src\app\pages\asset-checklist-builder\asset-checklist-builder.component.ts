import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { AssetService, ChecklistDefinition } from '@core/services/asset.service';
import { AssetChecklistPreviewComponent } from '@features/lender/asset-checklist-preview/asset-checklist-preview.component';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';

@Component({
  selector: 'app-asset-checklist-builder',
  templateUrl: './asset-checklist-builder.component.html',
  styleUrls: ['./asset-checklist-builder.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbInputModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbSpinnerModule,
    CdkDrag,
    CdkDropList,
    CkeditorComponent,
    NbButtonModule,
  ],
})
export class AssetChecklistBuilderComponent implements OnInit, AfterViewInit {
  @ViewChild('editor') editor!: ElementRef;
  // public Editor = ClassicEditor;
  // public editorConfig: EditorConfig = {};
  checklistName = '';
  checklistId?: number;
  checklistStructure: any[] = [];
  checklistColumns = [{ field: 'content', header: '' }];
  isLoading = false;
  isLayoutReady = false;
  selectedStatus?: number = 0;
  statusList = [
    { value: 0, name: 'Draft' },
    { value: 1, name: 'Active' },
    { value: 2, name: 'Archived' },
  ];

  constructor(
    private assetService: AssetService,
    private toastr: NbToastrService,
    private dialogService: NbDialogService,
    private changeDetector: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.isLoading = true;
    // this.setConfig();
    this.getChecklistContent();
  }

  ngAfterViewInit(): void {
    this.setConfig();
    this.isLayoutReady = true;
    this.changeDetector.detectChanges();
  }

  getChecklistContent() {
    this.assetService.getChecklistStructureValue.subscribe((t: ChecklistDefinition) => {
      if (t.checklistId) {
        this.checklistName = t.checklistName ? t.checklistName : '';
        this.checklistId = t.checklistId;
        this.selectedStatus = t.checklistStatus;
        this.checklistStructure = t.checklistStructure.map((el) => {
          el.edit = false;
          return el;
        });
      } else {
        this.checklistName = '';
        this.checklistId = undefined;
        this.selectedStatus = 0;
        this.addNewItem('header');
        this.addNewItem('text');
      }
      this.isLoading = false;
    });
  }

  addNewItem(type: string) {
    const content = '';
    this.checklistStructure.push({
      type: type,
      content: content,
      title: '',
      active: 1,
    });
  }

  removeItem(index: number) {
    /**
     * By default we have one Section Header on the top of the page and one Task below
     * The default Section Header on the top of the page is immutable to delete and move options
     */
    if (index > 1) {
      this.checklistStructure.splice(index, 1);
    }
  }

  submitChecklist() {
    this.isLoading = true;
    const orderedChecklistStructure = this.checklistStructure.map((row, index) => {
      row.Id = row.id;
      row.ContentHtml = row.content;
      row.ContentTitle = row.title;
      row.ContentType = row.type;
      row.ContentOrder = index;
      row.Active = row.active;
      return row;
    });
    const checklistForm = {
      ChecklistName: this.checklistName,
      Status: this.selectedStatus,
      Id: this.checklistId,
    };
    const successToasterMessage = this.checklistId
      ? 'Checklist Successfully Updated'
      : 'Checklist Successfully Created';
    const failToasterMessage = this.checklistId
      ? 'Failed to update checklist and checklist contents'
      : 'Failed to save checklist and checklist contents';
    this.assetService.saveChecklist(checklistForm).subscribe((response: any) => {
      if (!this.checklistId) {
        this.checklistId = response.payload;
      }
      const checklistContentform = {
        ChecklistId: this.checklistId,
        ChecklistContents: orderedChecklistStructure.map((el) => {
          el.ChecklistId = this.checklistId;
          return el;
        }),
      };
      if (checklistContentform.ChecklistContents.length > 0) {
        this.assetService.saveChecklistContent(checklistContentform).subscribe((response: any) => {
          this.isLoading = false;
          if (response.success) {
            this.toastr.success(successToasterMessage, 'Success');
          } else {
            this.toastr.danger(response.error.message, 'Error!');
          }
        });
      } else {
        this.toastr.danger('No checklist contents found', 'Error!');
        this.isLoading = false;
      }

      if (response.responseCode !== 1) {
        this.toastr.danger(failToasterMessage, 'Error!');
      }
      this.isLoading = false;
    });
  }

  previewChecklist() {
    if (this.checklistStructure) {
      const tableStructure: any = [];
      let headerIndex = -1,
        idIndex = 1;
      this.checklistStructure.forEach((row: any) => {
        if (row.type === 'header') {
          idIndex = 1;
          tableStructure.push({
            header: row.title,
            checklist_structure: [],
          });
          headerIndex++;
        } else {
          tableStructure[headerIndex]['checklist_structure'].push({
            id: idIndex,
            description: row.content,
          });
          idIndex++;
        }
      });

      this.dialogService
        .open(AssetChecklistPreviewComponent, {
          context: {
            title: 'Preview Checklist',
            tableStructure: tableStructure,
            yesButton: 'CLOSE',
          },
          autoFocus: false,
        })
        .onClose.subscribe((res: any) => { });
    }
  }

  private setConfig(): void {
    // this.editorConfig = {
    //   licenseKey:
    //     'eyJhbGciOiJFUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************.-byC3OSAOAVx0cOexfpE3e8XrESboUuqHIl8bJF2LoeX02R2ABOZhU0g1qwplW5FAqlVMs4RLamSpCVItkhcYA',
    //   plugins: [Autosave, Bold, Essentials, Heading, Italic, List, Paragraph],
    //   heading: {
    //     options: [
    //       {
    //         model: 'paragraph',
    //         title: 'Paragraph',
    //         class: 'ck-heading_paragraph',
    //       },
    //       {
    //         model: 'heading1',
    //         view: 'h1',
    //         title: 'Heading 1',
    //         class: 'ck-heading_heading1',
    //       },
    //       {
    //         model: 'heading2',
    //         view: 'h2',
    //         title: 'Heading 2',
    //         class: 'ck-heading_heading2',
    //       },
    //       {
    //         model: 'heading3',
    //         view: 'h3',
    //         title: 'Heading 3',
    //         class: 'ck-heading_heading3',
    //       },
    //       {
    //         model: 'heading4',
    //         view: 'h4',
    //         title: 'Heading 4',
    //         class: 'ck-heading_heading4',
    //       },
    //       {
    //         model: 'heading5',
    //         view: 'h5',
    //         title: 'Heading 5',
    //         class: 'ck-heading_heading5',
    //       },
    //       {
    //         model: 'heading6',
    //         view: 'h6',
    //         title: 'Heading 6',
    //         class: 'ck-heading_heading6',
    //       },
    //     ],
    //   },
    //   toolbar: {
    //     items: ['heading', 'bold', 'italic', 'bulletedList', 'numberedList'],
    //     shouldNotGroupWhenFull: false,
    //   },
    //   initialData: 'Write something here.....',
    //   placeholder: 'Type or paste your content here!',
    //   // toolbar: [
    //   //   ['Format',
    //   //     'Bold',
    //   //     'Italic',
    //   //     'BulletedList',
    //   //     'NumberedList'
    //   //   ]
    //   // ],
    //   // extraPlugins: ['justify', 'autogrow', 'editorplaceholder'],
    //   // editorplaceholder: 'Write something here.....',
    //   // toolbarLocation: 'bottom',
    // };
  }

  // DRAG & DROP
  onDrop(event: CdkDragDrop<any[]>) {
    const previousIndex = event.previousIndex;
    const currentIndex = event.currentIndex;

    /**
     * first position restricted to header only.
     * if index being dragged into = 0 && item type being moved = 'text', then reject/reset.
     */
    if (currentIndex === 0 && this.checklistStructure[previousIndex].type === 'text') {
      // Don't allow text items to be moved to position 0
      return;
    }

    // Only move if positions are different
    if (previousIndex !== currentIndex) {
      moveItemInArray(this.checklistStructure, previousIndex, currentIndex);
    }
  }

  /**
   * Check if an item can be dragged
   * Items at index 0 and 1 are typically restricted
   */
  canDragItem(index: number): boolean {
    return index > 1;
  }

  /**
   * Get drag disabled state for an item
   */
  isDragDisabled(index: number): boolean {
    return !this.canDragItem(index);
  }

  generateItems(count: number, creator: any): any {
    const result = [];
    for (let i = 0; i < count; i++) {
      result.push(creator(i));
    }
    return result;
  }

  applyDrag(arr: any, dragResult: any): any {
    const { removedIndex, addedIndex, payload } = dragResult;
    if (removedIndex === null && addedIndex === null) return arr;

    const result = [...arr];
    let itemToAdd = payload;

    if (removedIndex !== null) {
      itemToAdd = result.splice(removedIndex, 1)[0];
    }

    if (addedIndex !== null) {
      result.splice(addedIndex, 0, itemToAdd);
    }

    return result;
  }
}
