import { GoogleSigninButtonModule, SocialAuthService } from '@abacritt/angularx-social-login';
import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, inject, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { BaseComponent } from '@core/models/base.component';
import { GoogleUserResponse } from '@core/models/response/user-data.response';
import { EntraAuthService } from '@core/services/auth/msal-entra.service';
import { AuthenticationService, TypeLoginEnum } from '@core/services/authentication.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbCheckboxModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbLayoutModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { takeUntil } from 'rxjs';

@Component({
  templateUrl: 'login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    NbCardModule,
    NbIconModule,
    NbLayoutModule,
    NbCheckboxModule,
    NbFormFieldModule,
    NbSpinnerModule,
    NbButtonModule,
    NbInputModule,
    GoogleSigninButtonModule,
  ],
})
export class LoginComponent extends BaseComponent implements OnInit, AfterViewInit {
  entraAuthService = inject(EntraAuthService);
  router = inject(Router);
  authenticationService = inject(AuthenticationService);
  socialAuthService = inject(SocialAuthService);
  sharedService = inject(SharedService);
  toastr = inject(NbToastrService);
  cdr = inject(ChangeDetectorRef);
  showGoogleButton = false;

  constructor() {
    super();
    this.socialAuthService.authState.pipe(takeUntil(this.destroy$)).subscribe((user: GoogleUserResponse) => {
      if (user && user.idToken) {
        this.authenticationService.externalLogin(user.idToken, TypeLoginEnum.Google).subscribe((data: any) => {
          this.sharedService.authenticate(data?.payload?.token, data);
        });
      }
    });
  }

  ngOnInit() {
    // Small delay to ensure Google API is loaded
    setTimeout(() => {
      this.showGoogleButton = true;
      this.cdr.detectChanges();
    }, 100);
  }

  ngAfterViewInit() {
    // Force change detection
    this.cdr.detectChanges();
  }

  // Log the user in and redirect them if MSAL provides a redirect URI otherwise go to the default URI
  loginWithMicrosoft() {
    this.entraAuthService.login();
  }
}
