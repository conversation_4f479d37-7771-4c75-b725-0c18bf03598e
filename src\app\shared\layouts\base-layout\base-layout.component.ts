import { SocialAuthService } from '@abacritt/angularx-social-login';
import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { HelpPopoverComponent } from '@components/templates/help-popover/help-popover.component';
import { SidebarComponent } from '@components/templates/sidebar/sidebar.component';
import { SwitchAccountComponent } from '@components/templates/switch-account/switch-account.component';
import { BaseComponent } from '@core/models/base.component';
import { AssetService } from '@core/services/asset.service';
import { EntraAuthService } from '@core/services/auth/msal-entra.service';
import { AuthenticationService } from '@core/services/authentication.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import { MessagesComponent } from '@features/messages/messages.component';
import {
  NbActionsModule,
  NbButtonModule,
  NbIconModule,
  NbLayoutModule,
  NbListModule,
  NbMenuModule,
  NbMenuService,
  NbPopoverDirective,
  NbPopoverModule,
  NbSidebarModule,
  NbSidebarService,
  NbTooltipModule,
  NbUserModule,
} from '@nebular/theme';
import { filter, map, switchMap, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-base-layout',
  templateUrl: './base-layout.component.html',
  styleUrls: ['./base-layout.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbLayoutModule,
    NbActionsModule,
    NbIconModule,
    NbUserModule,
    NbPopoverModule,
    NbTooltipModule,
    NbListModule,
    NbMenuModule,
    NbSidebarModule,
    HelpPopoverComponent,
    SwitchAccountComponent,
    MessagesComponent,
    SidebarComponent,
    RouterOutlet,
    NbButtonModule,
  ],
})
export class LayoutComponent extends BaseComponent implements OnInit {
  @ViewChild(NbPopoverDirective) popover!: NbPopoverDirective;

  items = [
    // { title: 'Profile' },
    {
      title: 'Logout',
      icon: 'log-out-outline',
    },
  ];

  name = '';
  username = '';
  visible = false;
  investorId: any;
  chatUserId: any;
  notificationCount = 0;

  entraAuthService = inject(EntraAuthService);
  socialAuthService = inject(SocialAuthService);
  private authenticationService = inject(AuthenticationService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private nbMenuService = inject(NbMenuService);
  private investorsService = inject(InvestorsService);
  private sidebarService = inject(NbSidebarService);
  private sharedService = inject(SharedService);
  private assetService = inject(AssetService);

  constructor() {
    super();
    this.setupInvestorSubscription();
  }

  async ngOnInit() {
    if (!this.sharedService.getURLToken()) {
      window.location.href = '/login';
      return;
    }
    // -----------------------------

    setTimeout(() => {
      this.sidebarService.compact();
    }, 10);
    this.userMenu();
    console.log('this.investorsService.accountValue', this.investorsService.accountValue);
    this.loadData();
    // this.getInvestorNotificationCount();
    // this.getAssetNotificationCount();

    this.sharedService.showMessageSubject.subscribe((value) => {
      this.notificationCount = 0;
      if (value.showChat) {
        this.visible = value.showChat;
        this.investorId = value.investorId;
        this.chatUserId = value.chatUserId;
        this.sharedService.setShowMessageValue({});
      }
      if (value.updateNotification) {
        this.investorsService
          .getNotificationCount(
            this.sharedService.isInvestor()
              ? {
                  investorId: this.investorId,
                }
              : {},
          )
          .subscribe((response: any) => {
            this.notificationCount += response.payload;
          });
        // if (this.sharedService.isAssetUsers()) {
        //   this.assetService
        //     .getNotificationCount(
        //       this.sharedService.isAssetUsers()
        //         ? {
        //             userId: this.chatUserId,
        //           }
        //         : {},
        //     )
        //     .subscribe((response: any) => {
        //       this.notificationCount += response.payload;
        //     });
        // }
      }
    });

    this.socialAuthService.authState
      .pipe(
        filter((authState) => !!authState),
        switchMap(() => this.socialAuthService.signOut()),
        takeUntil(this.destroy$),
      )
      .subscribe();
  }

  private userMenu(): void {
    this.nbMenuService
      .onItemClick()
      .pipe(
        filter(({ tag }) => tag === 'my-context-menu'),
        map(({ item: { title } }) => title),
      )
      .subscribe((title) => {
        if (title === 'Logout') {
          this.logout();
        }
      });
  }

  private async loadData(): Promise<void> {
    this.name = await this.sharedService.getUserNameFromGetUserAPI();
    if (this.isInvestor()) {
      // Don't set investorId here - let the subscription handle it
      // this.investorId = this.investorsService.accountValue?.investorId || 0;
    } else {
      this.username = this.sharedService.getEmailFromToken();
      this.getInvestorNotificationCount();
    }
  }

  private setupInvestorSubscription(): void {
    if (this.isInvestor()) {
      this.investorsService.account
        .pipe(
          takeUntil(this.destroy$), // Don't forget to unsubscribe
          filter((value: any) => value && value.investorId), // Only proceed when we have valid investor data
        )
        .subscribe((value: any) => {
          console.log('setupInvestorSubscription triggered with:', value);
          this.username = this.investorsService.accountValue?.email || '';
          this.investorId = this.investorsService.accountValue?.investorId || 0;

          // Only call notification count if we have a valid investorId
          if (this.investorId > 0) {
            this.getInvestorNotificationCount();
          }
          this.visible = false;
        });
    }
  }

  private getInvestorNotificationCount(): void {
    this.investorsService
      .getNotificationCount(
        this.investorId
          ? {
              investorId: this.investorId,
            }
          : {},
      )
      .subscribe((value: any) => {
        this.notificationCount += value.payload;
      });
  }

  private getAssetNotificationCount(): void {
    this.assetService
      .getNotificationCount(
        this.chatUserId
          ? {
              userId: this.chatUserId,
            }
          : {},
      )
      .subscribe((value: any) => {
        this.notificationCount += value.payload;
      });
  }

  showMessages(): void {
    this.popover.hide();
    if (this.isInvestor()) {
      this.investorId = 0;
      setTimeout(() => {
        this.investorId = this.investorsService.accountValue?.investorId || 0;
      }, 200);
    }
    this.visible = true;
  }

  logout(): void {
    this.authenticationService.logout();
    this.router.navigate(['/login']);
  }

  toggle(): void {
    this.sidebarService.toggle(true, 'left');
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  updateNotificationCount(value: any): void {
    this.getInvestorNotificationCount();
  }

  openMessages(event: any): void {
    this.visible = true;
  }

  hidePopup(): void {
    this.visible = false;
    this.sharedService.setShowMessageValue({
      updateNotification: true,
    });
  }
}
