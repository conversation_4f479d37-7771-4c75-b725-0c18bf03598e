<form class="" [formGroup]="form" (ngSubmit)="onSubmit()">
  <nb-card>
    <nb-card-header>
      <div class="flex flex-wrap -mx-2">
        <h5 class="w-6/12 px-2">
          <div class="title">Add New Document</div>
        </h5>

        <div class="w-6/12 px-2">
          <div class="popup-close float-right">
            <button ghost nbButton (click)="close()">
              <nb-icon icon="close"></nb-icon>
            </button>
          </div>
        </div>
      </div>
    </nb-card-header>
    <nb-card-body>
      <div class="document-popup">
        <div class="w-full px-2 my-[15px]">
          <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
        </div>

        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label>
              <strong for="inStore">Document Type</strong>
              <strong class="text-lime required"> &nbsp; * </strong></label
            >

            <nb-select
              placeholder="Please Select "
              fullWidth
              size="large"
              shape="semi-round"
              status="basic"
              name="investmentId"
              formControlName="investmentDocType"
            >
              <nb-option *ngFor="let docType of docTypes" [value]="docType.id">
                {{ docType.name }}
              </nb-option>
            </nb-select>
            <div *ngIf="submitted && f.investmentDocType.errors" class="invalid-feedback">
              <div *ngIf="f.investmentDocType.errors.required">Document type is required.</div>
            </div>
          </div>

          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]" [hidden]="uploadedDocuments">
            <div class="file-container" for="fileDropRef" appDnd (fileDropped)="onFileDropped($event)">
              <input type="hidden" />

              <input
                type="file"
                #fileDropRef
                id="fileDropRef"
                (change)="fileBrowseHandler($event)"
                accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
              />

              <p class="m-0" for="fileDropRef">
                <nb-icon icon="file-add"></nb-icon>
                Drop Document here or Click to upload.
              </p>
            </div>

            <div *ngIf="submitted && !this.uploadedDocuments" class="invalid-feedback">
              <div *ngIf="!this.uploadedDocuments">Document is required.</div>
            </div>
          </div>

          <div class="w-8/12 px-2" *ngIf="uploadedDocuments && uploadedDocuments.length > 0">
            <div class="files-list">
              <div class="single-file" *ngFor="let file of uploadedDocuments; let i = index">
                <div class="info">
                  <div class="name">
                    {{ file?.name }}
                  </div>
                  <div class="delete" (click)="clearDocuments()">
                    <nb-icon class="file-delete" icon="close-circle"></nb-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <nb-progress-bar style="width: 100%" *ngIf="progress" [value]="progress" status="primary">
              Uploading {{ progress }}%
            </nb-progress-bar>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="w-full px-2">
        <button class="float-right" [nbSpinner]="loading" nbButton status="primary" style="min-width: 135px">
          IMPORT
        </button>
      </div>
    </nb-card-footer>
  </nb-card>
</form>
