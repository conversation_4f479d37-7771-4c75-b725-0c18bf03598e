<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2" style="margin: 10px 20px">
      <h5 class="w-8/12 px-2">{{ title }}</h5>
      <div class="w-4/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="close(false)">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="flex flex-wrap -mx-2 my-[15px]" *ngFor="let structure of tableStructure">
      <p-table [value]="structure.checklist_structure" responsiveLayout="scroll">
        <ng-template class="headerStyle" pTemplate="caption">
          {{ structure.header }}
        </ng-template>
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 120px">ID</th>
            <th style="width: 678px">Description</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
          <tr>
            <td style="width: 120px">{{ rowData.id }}</td>
            <td style="width: 678px" [innerHTML]="sanitizer.bypassSecurityTrustHtml(rowData.description)"></td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="flex flex-wrap -mx-2 float-right">
      <button class="button" (click)="close(true)" nbButton size="large" status="primary">
        {{ yesButton }}
      </button>
    </div>
  </nb-card-footer>
</nb-card>
