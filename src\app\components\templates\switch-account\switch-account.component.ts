import { AfterViewInit, Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  NbPopoverDirective,
  NbToastrService,
  NbIconModule,
  NbPopoverModule,
  NbListModule,
  NbSpinnerModule,
  NbUserModule,
  NbButtonModule,
} from '@nebular/theme';
import { AuthenticationService } from '@core/services/authentication.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-switch-account',
  templateUrl: './switch-account.component.html',
  styleUrls: ['./switch-account.component.scss'],
  standalone: true,
  imports: [CommonModule, NbIconModule, NbPopoverModule, NbListModule, NbSpinnerModule, NbUserModule, NbButtonModule],
})
export class SwitchAccountComponent implements OnInit, AfterViewInit {
  @ViewChild(NbPopoverDirective) popover!: NbPopoverDirective;

  @Output() openMessages = new EventEmitter<boolean>();
  users: any[] = [];

  loading = false;

  constructor(
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private toastr: NbToastrService,
    private router: Router,
    private route: ActivatedRoute,
    private authenticationService: AuthenticationService,
  ) {}

  ngAfterViewInit(): void {
    this.getAccounts();
  }

  private getUserParams(): void {
    if (this.router.url.includes('/dashboard') || this.router.url.includes('/investments')) {
      const token = this.route.snapshot.queryParams.token;
      if (token) {
        this.loading = true;
        this.authenticationService.getUserParams(token).subscribe((userParams: any) => {
          if (userParams.success && userParams.payload) {
            const [selectedUser] = this.users.filter((user: any) => user.investorId === userParams.payload.investorId);
            if (selectedUser) {
              this.investorsService.setAccount(selectedUser);
              this.openMessages.emit(true);
              this.router.navigate(['/dashboard']);
            }
          } else {
            this.setSelectedAccount();
          }
          this.loading = false;
        });
      } else {
        this.setSelectedAccount();
      }
    }
  }

  private setSelectedAccount(): void {
    const [selectedUser] = this.users.filter((user: any) => user.selected);
    if (selectedUser) {
      this.investorsService.setAccount(selectedUser);
    }
    // this.router.navigate(['/dashboard']);
  }

  ngOnInit(): void {}

  private getAccounts(): void {
    this.investorsService.getAccounts().subscribe((response: any) => {
      if (response.success) {
        this.users = response.payload;
        this.getUserParams();
      }
    });
  }

  showHelp(): boolean {
    return this.sharedService.isInvestor();
  }

  switchAccount(user: any): void {
    this.loading = true;
    this.popover.hide();
    this.authenticationService.switchOrg(user.orgKey, user.investorId).subscribe((response: any) => {
      if (response.success) {
        // this.investorsService.setAccount(user);
        this.authenticationService.saveToken(response);
        this.router.navigate(['/dashboard']);
        this.getAccounts();
      } else {
        this.toastr.danger(response.error.message, 'Error!');
      }
      this.loading = false;
    });
  }
}
