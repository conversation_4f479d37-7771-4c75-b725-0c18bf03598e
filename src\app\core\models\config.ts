export class Config {
  inputStyles?: any;
  containerStyles?: Record<string, any>;
  allowKeyCodes?: string[];
  length = 0;
  allowNumbersOnly?: boolean;
  inputClass?: string;
  containerClass?: string;
  isPasswordInput?: boolean;
  disableAutoFocus?: boolean;
  placeholder?: string;
  value?: string;
}

export enum InvestorStatus {
  Pending = 19,
  Active = 20,
  Disable = 21,
  Archive = 22,
}

export enum FacilityStatus {
  Draft = 45,
  Open = 46,
  Closed = 47,
}

export enum UpdatePublishStatus {
  Draft = 48,
  Published = 49,
  Unpublished = 50,
}

export enum DocumentType {
  Image = 1,
  Document = 2,
  FundDocuments = 33,
  QuarterlyUpdates = 34,
}

export enum DashboardMessageType {
  Activity = 1,
  Message = 2,
}

export enum TypeKey {
  EntityType = 'EntityType',
  InvestorStatus = 'InvestorStatus',
  TransactionType = 'TransactionType',
  InvestmentStatus = 'InvestmentStatus',
  AdminInvestmentStatus = 'AdminInvestmentStatus',
  InvestmentState = 'InvestmentState',
  InvestmentType = 'InvestmentType',
  AssetType = 'AssetType',
  InvestmentIcon = 'InvestmentIcon',
  DocumentType = 'DocumentType',
  AssetManagement_LenderOrg = 'AssetManagement_LenderOrg',
  AssetManagement_FacilityType = 'AssetManagement_FacilityType',
  AssetManagement_FacilityStatus = 'AssetManagement_FacilityStatus',
  AssetManagement_UpdatePublishStatus = 'AssetManagement_UpdatePublishStatus',
  AssetManagement_CurrentLoanStatus = 'AssetManagement_CurrentLoanStatus',
  AssetManagement_DocumentType = 'AssetManagement_DocumentType',
  AssetManagement_DocumentCategory = 'AssetManagement_DocumentCategory',
}

export enum AssetTaskType {
  Checklist = 1,
  Covenant = 2,
  NoteTask = 3,
}
