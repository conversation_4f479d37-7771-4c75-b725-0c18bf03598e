import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { DeleteDocumentComponent } from '@components/templates/delete-document/delete-document.component';
import { DocumentType } from '@core/models/config';
import { DocumentService } from '@core/services/document.service';
import { InvestmentService } from '@core/services/investment.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbIconModule,
  NbToastrService,
  NbTooltipModule,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';
import { AddInvestmentDocumentComponent } from '../add-investment-document/add-investment-document.component';

@Component({
  selector: 'app-investment-documents',
  templateUrl: './investment-documents.component.html',
  styleUrls: ['./investment-documents.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbIconModule, TableModule, SkeletonModule, NbButtonModule, NbTooltipModule],
})
export class InvestmentDocumentsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @Output() changeTab = new EventEmitter<boolean>();

  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  documents: any[] = [];
  statusData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  investmentId: any;
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private investmentService: InvestmentService,
    private dialogService: NbDialogService,
    private documentService: DocumentService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 100,
      sortField: 'id',
      sortOrder: 'desc',
    } as Filters;

    this.investmentId = this.sharedService.getFormParamValue.investmentId;
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    this.investmentService
      .getDocuments({
        documentType: DocumentType.Document,
        investmentId: this.investmentId,
        isInvestment: true,
        allDocTypes: true,
        sortField: this.filterParams.sortField,
        sortOrder: this.filterParams.sortOrder,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.documents = (data as any).payload;
          this.totalRecords = (data as any).payload.length;
          this.dtTrigger.next(this.documents);
          this.spinner.hide();
          this.loading = false;
        }
      });
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  exportUser(): void {
    this.filterParams.export = true;
  }

  ngOnDestroy(): void {}

  addNewDocument(): void {
    this.dialogService
      .open(AddInvestmentDocumentComponent, {
        context: {},
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getList();
        }
      });
  }

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  deleteDocument(documentKey: string, userId: any, investmentId: any): void {
    this.dialogService
      .open(DeleteDocumentComponent, {
        context: {
          documentKey,
          userId,
          investmentId,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getList();
        }
      });
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  next(): void {
    this.changeTab.emit(true);
  }
}
