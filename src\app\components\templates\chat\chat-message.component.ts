/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { animate, state, style, transition, trigger } from '@angular/animations';
import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { Dom<PERSON>anitizer, SafeStyle } from '@angular/platform-browser';

import { CommonModule } from '@angular/common';
import { convertToBoolProperty, NbBooleanInput } from '@core/helpers/helpers';
import { NbTooltipModule, NbUserModule } from '@nebular/theme';
import { NbChatMessageFile, NbChatMessageFileComponent } from './chat-message-file.component';
import { NbChatMessageHtmlComponent } from './chat-message-html.component';
import { NbChatMessageMapComponent } from './chat-message-map.component';
import { NbChatMessageQuoteComponent } from './chat-message-quote.component';
import { NbChatMessageTextComponent } from './chat-message-text.component';

/**
 * Chat message component.
 *
 * Multiple message types are available through a `type` property, such as
 * - text - simple text message
 * - file - could be a file preview or a file icon
 * if multiple files are provided grouped files are shown
 * - quote - quotes a message with specific quote styles
 * - map - shows a google map picture by provided [latitude] and [longitude] properties
 *
 * @stacked-example(Available Types, chat/chat-message-types-showcase.component)
 *
 * Message with attached files:
 * ```html
 * <app-chat-message
 *   type="file"
 *   [files]="[ { url: '...' } ]"
 *   message="Hello world!">
 * </app-chat-message>
 * ```
 *
 * Map message:
 * ```html
 * <app-chat-message
 *   type="map"
 *   [latitude]="53.914"
 *   [longitude]="27.59"
 *   message="Here I am">
 * </app-chat-message>
 * ```
 *
 * @styles
 *
 * chat-message-background:
 * chat-message-text-color:
 * chat-message-reply-background-color:
 * chat-message-reply-text-color:
 * chat-message-avatar-background-color:
 * chat-message-sender-text-color:
 * chat-message-quote-background-color:
 * chat-message-quote-text-color:
 * chat-message-file-text-color:
 * chat-message-file-background-color:
 */
@Component({
  selector: 'app-chat-message',
  template: `
    <ng-container *ngIf="!avatarStyle">
      {{ getInitials() }}
    </ng-container>
    <nb-user
      class="avatar"
      [nbTooltip]="sender"
      nbTooltipStatus="control"
      nbTooltipPlacement="bottom"
      size="medium"
      [name]="sender"
      status="info"
      [color]="avatarColor"
      onlyPicture="true"
    >
    </nb-user>
    <div class="message mr-12 ml-2 flex-1">
      <ng-container [ngSwitch]="type">
        <app-chat-message-file
          *ngSwitchCase="'file'"
          [sender]="sender"
          [date]="date"
          [dateFormat]="dateFormat"
          [message]="message"
          [files]="files"
        >
        </app-chat-message-file>

        <app-chat-message-quote
          *ngSwitchCase="'quote'"
          [sender]="sender"
          [date]="date"
          [dateFormat]="dateFormat"
          [message]="message"
          [quote]="quote"
        >
        </app-chat-message-quote>

        <app-chat-message-map
          *ngSwitchCase="'map'"
          [sender]="sender"
          [date]="date"
          [message]="message"
          [latitude]="latitude"
          [longitude]="longitude"
        >
        </app-chat-message-map>

        <app-chat-message-html
          *ngSwitchCase="'html'"
          [reply]="reply"
          [sender]="sender"
          [metaData]="metaData"
          [date]="date"
          [dateFormat]="dateFormat"
          [message]="message"
          [messageTitle]="messageTitle"
          [noteType]="noteType"
          [taskInformation]="taskInformation"
          (taskCompletion)="completeTask()"
          (taskEdit)="editTask()"
        >
        </app-chat-message-html>

        <app-chat-message-text
          *ngSwitchDefault
          [sender]="sender"
          [metaData]="metaData"
          [date]="date"
          [dateFormat]="dateFormat"
          [message]="message"
        >
        </app-chat-message-text>
      </ng-container>
    </div>
  `,
  animations: [
    trigger('flyInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      transition('void => *', [style({ transform: 'translateX(-100%)' }), animate(80)]),
      transition('* => void', [animate(80, style({ transform: 'translateX(100%)' }))]),
    ]),
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    NbUserModule,
    NbTooltipModule,
    NbChatMessageFileComponent,
    NbChatMessageQuoteComponent,
    NbChatMessageMapComponent,
    NbChatMessageHtmlComponent,
    NbChatMessageTextComponent,
  ],
})
export class NbChatMessageComponent {
  readonly flyInOut = true;

  @HostBinding('class.not-reply')
  get notReply() {
    return !this.reply;
  }

  /**
   * Determines if a message is a reply
   */
  @Input()
  @HostBinding('class.reply')
  get reply(): boolean {
    return this._reply;
  }
  set reply(value: boolean) {
    this._reply = convertToBoolProperty(value);
  }

  /**
   * Message send avatar
   * @type {string}
   */
  @Input()
  set avatar(value: string) {
    this.avatarStyle = value ? this.domSanitizer.bypassSecurityTrustStyle(`url(${value})`) : '';
  }

  constructor(protected domSanitizer: DomSanitizer) { }
  static ngAcceptInputType_reply: NbBooleanInput;

  avatarStyle?: SafeStyle;
  protected _reply = false;

  /**
   * Message sender
   * @type {string}
   */
  @Input() message!: string;
  @Input() avatarColor!: string;

  /**
   * Message type
   * @type {boolean}
   */
  @Input() metaData!: any;

  /**
   * Message sender
   * @type {string}
   */
  @Input() sender!: string;

  /**
   * Message send date
   * @type {Date}
   */
  @Input() date!: Date;

  /**
   * Message send date format, default 'shortTime'
   * @type {string}
   */
  @Input() dateFormat!: string;

  /**
   * Array of files `{ url: 'file url', icon: 'file icon class' }`
   */
  @Input() files!: NbChatMessageFile[];

  /**
   * Quoted message text
   * @type {string}
   */
  @Input() quote!: string;

  /**
   * Map latitude
   * @type {number}
   */
  @Input() latitude!: number;

  /**
   * Map longitude
   * @type {number}
   */
  @Input() longitude!: number;

  /**
   * Message type, available options `text|file|map|quote`
   * @type {string}
   */
  @Input() type!: string;

  /**
   * Message type, available options `note or task
   * @type {string}
   */
  @Input() noteType = false;

  /**
   * Message Title if input is a task`
   * @type {string}
   */
  @Input() messageTitle!: string;

  @Input() taskInformation!: any;

  @Output() taskCompletion = new EventEmitter<any>();
  @Output() taskEdit = new EventEmitter<any>();

  getInitials(): string {
    if (this.sender) {
      const names = this.sender.split(' ');

      return names
        .map((n) => n.charAt(0))
        .splice(0, 2)
        .join('')
        .toUpperCase();
    }

    return '';
  }

  completeTask() {
    this.taskCompletion.emit(this.taskInformation);
  }
  editTask() {
    this.taskEdit.emit(this.taskInformation);
  }
}
