import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { DocumentService } from '@core/services/document.service';
import { InvestorsService } from '@core/services/investors.service';
import { NbMenuItem } from '@core/services/menu.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbContextMenuModule,
  NbDialogService,
  NbIconModule,
  NbMenuService,
  NbProgressBarModule,
  NbToastrService,
  NbTooltipModule,
  NbUserModule,
} from '@nebular/theme';
import { Subject } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import { NbChatFormComponent } from '../chat/chat-form.component';
import { NbChatMessageComponent } from '../chat/chat-message.component';
import { NbChatComponent } from '../chat/chat.component';
import { DeleteDocumentComponent } from '../delete-document/delete-document.component';

@Component({
  selector: 'app-investor-chat',
  templateUrl: './investor-chat.component.html',
  styleUrls: ['./investor-chat.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbIconModule,
    NbProgressBarModule,
    NbUserModule,
    NbTooltipModule,
    NbButtonModule,
    NbContextMenuModule,
    NbChatComponent,
    NbChatMessageComponent,
    NbChatFormComponent,
  ],
})
export class InvestorChatComponent implements OnInit, OnChanges {
  @Input() investorId?: any;

  @Input() filter!: any;

  @Input() showFilter = true;

  @Input() isInternalNote = false;

  @Input() isAdmin!: boolean;
  scrollBottom = true;
  messages: any[] = [];
  progress: any;
  attachments: any;
  droppedFiles: any[] = [];

  chatFilter = true;
  filterItems: NbMenuItem[] = [
    {
      title: 'Internal',
      data: true,
      selected: this.chatFilter,
    },
    {
      title: 'Client',
      data: false,
      selected: !this.chatFilter,
    },
  ];
  private ngUnsubscribe = new Subject();
  isFirstScroll = true;

  constructor(
    private documentService: DocumentService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private dialogService: NbDialogService,
    private investorsService: InvestorsService,
    private nbMenuService: NbMenuService,
  ) {}

  ngOnInit(): void {
    this.chatFilter = this.isInternalNote;

    // setTimeout(() => {
    //   this.getMessage(this.investorId);
    // }, 1000);

    this.nbMenuService
      .onItemClick()
      .pipe(takeUntil(this.ngUnsubscribe))
      .pipe(
        filter(({ tag }) => tag === 'chat-filter'),
        map(({ item: { data } }) => data),
      )
      .subscribe((data) => {
        this.chatFilter = data;
        this.getMessage(this.investorId);
        this.filterItems = [
          {
            title: 'Internal',
            data: true,
            selected: this.chatFilter,
          },
          {
            title: 'Client',
            data: false,
            selected: !this.chatFilter,
          },
        ];
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.chatFilter = this.isInternalNote;
    if ('investorId' in changes) {
      this.getMessage(changes.investorId.currentValue);
    } else if ('filter' in changes) {
      this.getMessage(this.investorId);
    } else {
      this.getMessage(this.investorId);
    }
  }

  private getMessage(investorId: any, chatId = 0): void {
    let params: any = {
      investorId,
      chatId,
      isInternalNote: this.chatFilter,
      filterBy: this.filter,
    };
    if (chatId === 0) {
      params = {
        investorId,
        isInternalNote: this.chatFilter,
        filterBy: this.filter,
      };
    }

    setTimeout(() => {
      this.chathistory(params, chatId);
    }, 500);

    this.getAttachments(investorId);
  }

  private chathistory(params: any, chatId: number): void {
    this.investorsService.chathistory(params).subscribe((response: any) => {
      if (response.success) {
        if (chatId === 0) {
          this.scrollBottom = true;
          this.messages = response.payload.chats;
          this.messages = this.messages.reverse();
        } else {
          if (this.isFirstScroll === false) {
            this.scrollBottom = false;
          }
          this.messages = [...response.payload.chats.reverse(), ...this.messages];
          if (this.isFirstScroll) {
            this.isFirstScroll = false;
          }
        }
      }
    });
  }

  private getAttachments(investorId: any): void {
    this.documentService.getAttachments({ investorId }).subscribe((response: any) => {
      if (response.success) {
        this.attachments = response.payload.attachments;
      }
    });
  }

  sendMessage(event: any): void {
    event.message = event.message.trim();

    const formData = new FormData();

    if (this.investorId) {
      formData.append('InvestorId', this.investorId as any);
    }
    formData.append('Message', event.message);
    formData.append('MessageType', '2');
    // if (this.isAdmin) {
    formData.append('IsInternalNote', event.isInternalNote);
    // }
    for (const item of event.files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
      // this.files.push(item);
    }

    if (this.sharedService.isInvestor()) {
      formData.append('IsAdmin', false as any);
    } else {
      formData.append('IsAdmin', true as any);
    }

    formData.append('Host', window.location.host);

    this.investorsService
      .sendMessage(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                if (formData.get('Files')) {
                  this.progress = Math.round((100 * event.loaded) / event.total);
                } else {
                  this.progress = 0;
                }
              }

              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.isFirstScroll = true;
            this.getMessage(this.investorId);
            this.progress = 0;
          }
        },
        (err: any) => console.log(err),
      );
  }

  getOldMessage(event: any): void {
    this.getMessage(this.investorId, this.messages[0]?.chatId);
  }

  async downloadFile(documentKey: any): Promise<void> {
    await this.documentService.getDocument({
      documentKey,
    });
  }

  // onFileDropped(event: any): void {
  //   this.prepareFilesList(event);
  // }

  // fileBrowseHandler(event: any): void {
  //   console.log(event);
  //   this.prepareFilesList(event.target.files);
  // }

  prepareFilesList(files: any[]): void {
    // const extensions = ['png', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'doc', 'docx', 'odt'];
    const allowedExtensions = /(\.jpg|\.jpeg|\.png|\.pdf|\.xls|\.xlsx|\.doc|\.docx|\.odt)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toast.danger('File type not supported', 'Upload Error!');
          // this.fileDropRef.nativeElement.value = '';
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toast.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          // this.fileDropRef.nativeElement.value = '';
          return;
        }
      }
    }

    const formData = new FormData();

    formData.append('FieldId', '0');

    if (this.investorId) {
      formData.append('FormRecordId', this.investorId as any);
    }

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.documentService
      .uploadDocument(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }

              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.toast.success('File Uploaded Successfully!', 'Success!');
            res.payload.forEach((element: any) => {});
          }
          this.getAttachments(this.investorId);
          this.getMessage(this.investorId);
        },
        (err: any) => console.log(err.message),
      );
  }

  deleteDocument(documentKey: string): void {
    this.dialogService
      .open(DeleteDocumentComponent, {
        context: {
          documentKey,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.getAttachments(this.investorId);
          this.getMessage(this.investorId);
        }
      });
  }

  isManager(): boolean {
    return this.sharedService.isManager();
  }
}
