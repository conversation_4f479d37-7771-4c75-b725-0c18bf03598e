server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html index.htm;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json image/svg+xml;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_disable "MSIE [1-6]\.";

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: fonts.googleapis.com fonts.gstatic.com data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;

    # DNS prefetch for Google Fonts
    add_header X-DNS-Prefetch-Control "on" always;

    # Handle Angular routing
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }

    # Cache main Angular bundle files with hash in filename (immutable content)
    location ~* \.(js|css)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        add_header X-Content-Type-Options "nosniff";
    }

    # Cache static assets
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        add_header X-Content-Type-Options "nosniff";
    }

    # Cache fonts
    location ~* \.(ttf|ttc|otf|eot|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        access_log off;
    }

    # Cache rules for specific Angular output files
    location ~* main-.*\.(js|css)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    location ~* polyfills-.*\.js$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    location ~* styles-.*\.css$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Disable access to dotfiles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Return 404 for all other php files not matching the front controller
    location ~* \.php$ {
        return 404;
    }
}
