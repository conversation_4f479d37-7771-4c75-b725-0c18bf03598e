import { GoogleLoginProvider, SocialAuthServiceConfig } from '@abacritt/angularx-social-login';
import { environment } from '@environments/environment';

export const socialAuthConfig: SocialAuthServiceConfig = {
  autoLogin: false,
  lang: 'en',
  providers: [
    {
      id: GoogleLoginProvider.PROVIDER_ID,
      provider: new GoogleLoginProvider(environment.google?.clientId || '', {
        oneTapEnabled: false, // This disables the One Tap popup
        scopes: 'profile email',
      }),
    },
  ],
} as SocialAuthServiceConfig;
