import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { NbChatMessageComponent } from '@components/templates/chat/chat-message.component';
import { NbChatComponent } from '@components/templates/chat/chat.component';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { GlobalMessageType } from '@core/helpers';
import { AssetTaskType } from '@core/models/config';
import { FacilityFilter } from '@core/models/response/facilities-filter.response';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDatepickerModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbMenuService,
  NbProgressBarModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { SelectButtonModule } from 'primeng/selectbutton';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { AssetAddTaskComponent } from '../asset-add-task/asset-add-task.component';
@Component({
  selector: 'app-asset-notes-chat',
  templateUrl: './asset-notes-chat.component.html',
  styleUrls: ['./asset-notes-chat.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbInputModule,
    NbFormFieldModule,
    NbSpinnerModule,
    NbDatepickerModule,
    NbButtonModule,
    NbProgressBarModule,
    SelectButtonModule,
    NbChatComponent,
    NbChatMessageComponent,
    CkeditorComponent,
  ],
})
export class AssetNotesChatComponent implements OnInit {
  @Input() assetId?: any;

  @Input() globalMessageType: GlobalMessageType = GlobalMessageType.Investor;

  @Input() userId?: any;

  @Input() contactOrgId?: number;

  @Input() filter!: any;

  @Input() showFilter = true;

  @Input() isInternalNote = false;

  @Input() isAdmin!: boolean;

  facilities: FacilityFilter[] = [];

  scrollTop = true;
  messages: any[] = [];
  progress: any;
  attachments: any;
  droppedFiles: any[] = [];

  chatFilter = true;
  loading = false;
  updating = false;
  // Editor = ClassicEditor;

  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;
  //
  originalMessage: any[] = [];

  filterSelection: any = [{ name: 'Tasks & Notes' }, { name: 'Tasks' }];

  notesOptions: any = [
    { label: 'All', value: 'all' },
    { label: 'Pending', value: 'pending' },
    { label: 'Completed', value: 'completed' },
  ];
  notesFilter = 'all';
  notesTaskFilter = 'Tasks & Notes';

  noteTaskOptions: any = [
    { label: 'Note', value: 'note', icon: 'assetEditIcon' },
    { label: 'Task', value: 'task', icon: 'addTaskIcon' },
  ];
  selectedNoteTaskOption = 'note';

  public editorConfig: any = {};
  editorContent: any;

  originatorUsers: any;

  assetKeyDataId: number | undefined;

  //task
  taskDate: any;
  taskTitle = '';
  taskAssignedTo: any;
  tasks: any[] = [];
  filterParams: Filters = {};

  //note
  pendingDocuments: any[] = [];
  globalSearchValue = '';
  //
  loadingData = false;
  private ngUnsubscribe = new Subject();
  isFirstScroll = true;
  facilityList: any[] = [];

  constructor(
    private documentService: DocumentService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private dialogService: NbDialogService,
    private assetService: AssetService,
    private nbMenuService: NbMenuService,
  ) {}

  filterNotesTasks() {
    const filteredNotes = this.originalMessage.filter((el) => {
      const searchTitle = el.messageTitle ? el.messageTitle.replace(/<[^>]+>/g, '') : '';
      const searchMessage = el.message ? el.message.replace(/<[^>]+>/g, '') : '';
      if (
        searchMessage.toLowerCase().includes(this.globalSearchValue.toLowerCase()) ||
        searchTitle.toLowerCase().includes(this.globalSearchValue.toLowerCase())
      ) {
        if (
          (this.notesTaskFilter == 'Tasks & Notes' && !el.task) ||
          (el.task &&
            (this.notesFilter == 'pending'
              ? el.completedBy == 0
              : this.notesFilter == 'completed'
                ? el.completedBy !== 0
                : true))
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    });
    this.messages = filteredNotes;
  }
  fileBrowseHandler(event: any): void {
    const files = event.target.files;
    for (const file of files) {
      if (file.size > 20971520) {
        this.toast.warning('File size cannot be larger than 20mb', 'File Size Error');
        return;
      }
    }
    this.pendingDocuments.push(...event.target.files);
  }
  clearDocuments(file: any) {
    const newUploadedDocuments = [...this.pendingDocuments].filter((el) => el.name !== file.name);
    this.pendingDocuments = newUploadedDocuments;
    if (this.pendingDocuments.length == 0) {
      this.fileDropRef.nativeElement.value = '';
    }
    // this.cdr.detectChanges();
  }
  dateChange(event: any) {
    this.taskDate = event;
  }
  ngOnInit(): void {
    this.userId = this.sharedService.getUserIdValue.userId;
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId;

    this.setConfig();
    this.getAssetList();
  }

  checkValid() {
    if (this.selectedNoteTaskOption == 'note') {
      if (this.pendingDocuments.length == 0 && !this.editorContent) {
        this.toast.danger('Note must contain content or files', 'Oops!');
        return false;
      } else {
        return true;
      }
    } else {
      if (this.taskTitle == '') {
        this.toast.danger('Missing Task Title', 'Oops!');
        return false;
      } else if (!this.taskAssignedTo) {
        this.toast.danger('Missing Task Assigned To', 'Oops!');
        return false;
      } else if (!this.taskDate) {
        this.toast.danger('Missing Task Date', 'Oops!');
        return false;
      } else {
        return true;
      }
    }
  }
  updateValue() {
    if (!this.updating) {
      if (this.checkValid()) {
        this.updating = true;
        if (this.selectedNoteTaskOption == 'note') {
          this.sendMessage();
        } else {
          this.saveTask();
        }
      }
    } else {
      return;
    }
  }

  clearInput() {
    if (this.selectedNoteTaskOption != 'note') {
      this.taskTitle = '';
      this.taskAssignedTo = null;
      this.taskDate = null;
    }
    this.pendingDocuments = [];
    this.editorContent = null;
  }

  private getOriginatorList(): void {
    if (this.assetKeyDataId) {
      this.assetService.getLenderOriginatorUsers().subscribe((data: any) => {
        if (data.success) {
          this.originatorUsers = (data as any).payload.userResult.filter(
            (user: any) => user.roleId === 4 || user.roleId == 1,
          );
          this.getMessage(this.assetId);
        }
      });
    }
  }

  setConfig(): void {
    // this.editorConfig = {
    //   height: '100px',
    //   toolbar: [
    //     ['Format',
    //       'Bold',
    //       'Italic',
    //       'Underline',
    //       'BulletedList',
    //       'NumberedList'
    //     ]
    //   ],
    //   extraPlugins: ['justify', 'editorplaceholder'],
    //   editorplaceholder: this.selectedNoteTaskOption == 'task' ? 'Write task description...' : 'Write a comment...',
    //   resize_enabled: false,
    //   toolbarLocation: 'bottom',
    // };
  }

  // ngOnChanges(changes: SimpleChanges): void {
  //   this.chatFilter = this.isInternalNote;
  //   if ('assetId' in changes) {
  //     this.getMessage(changes.assetId.currentValue);
  //   } else if ('filter' in changes) {
  //     this.getMessage(this.assetId);
  //   } else if ('userId' in changes) {
  //     this.getMessage(this.assetId);
  //   } else {
  //     this.getMessage(this.assetId);
  //   }

  //   if ('contactOrgId' in changes) {
  //     this.getFacilities(this.contactOrgId);
  //   }
  // }

  private getMessage(assetId: any, chatId = 0): void {
    const params = {
      assetId,
      userId: null,
      isInternalNote: this.chatFilter,
      filterBy: this.filter,
    };

    setTimeout(() => {
      this.chathistory(params);
    }, 500);

    this.getAttachments(assetId);
  }

  private chathistory(params: any): void {
    this.loadingData = true;
    this.assetService.chathistory(params).subscribe(
      (response: any) => {
        if (response.success) {
          this.getTasks(response.payload);
        } else {
          this.loadingData = false;
          this.toast.danger(response.error.message, 'Oops!');
        }
      },
      () => {
        this.loadingData = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  private getAttachments(assetId: any): void {
    this.documentService.getAttachments({ assetId }).subscribe((response: any) => {
      if (response.success) {
        this.attachments = response.payload.attachments;
      }
    });
  }

  sendMessage(): void {
    const message = this.editorContent.trim();

    const formData = new FormData();

    if (this.assetId) {
      formData.append('assetId', this.assetId as any);
    }

    formData.append('Message', message);
    formData.append('MessageType', '2');

    formData.append('IsInternalNote', 'true');

    for (const item of this.pendingDocuments) {
      item.progress = 0;
      formData.append('Files', item, item.name);
      // this.files.push(item);
    }

    if (this.sharedService.isInvestor()) {
      formData.append('IsAdmin', false as any);
    } else {
      formData.append('IsAdmin', true as any);
    }

    formData.append('Host', window.location.host);

    this.assetService
      .sendMessage(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                if (formData.get('Files')) {
                  this.progress = Math.round((100 * event.loaded) / event.total);
                } else {
                  this.progress = 0;
                }
              }

              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.isFirstScroll = true;
            this.clearInput();
            this.getMessage(this.assetId);
            this.progress = 0;
          }
        },
        (err: any) => {
          console.log(err);
          this.updating = false;
        },
      );
  }

  saveTask(dataInput: any = null): void {
    const taskSubmission = {
      assetId: this.assetId,
      assignedTo: this.taskAssignedTo,
      taskType: AssetTaskType.NoteTask,
      taskTitle: this.taskTitle,
      isArchived: 0,
      dueDate: this.taskDate,
      taskContent: this.editorContent,
      id: 0,
      reviewerId: null,
      reviewerDate: '',
      loanManagerId: null,
      loanManagerDate: '',
      completedBy: null,
      completedByDate: '',
    };
    if (dataInput) {
      const documents = {
        deletedDocuments: [],
        documents: [],
      };
      this.assetService
        .saveNotesTask(dataInput, documents)
        .pipe(
          map((event: any) => {
            switch (event.type) {
              case HttpEventType.UploadProgress:
                return { status: 'progress', message: this.progress };

              case HttpEventType.Response:
                return event.body;
              default:
                return `Unhandled event: ${event.type}`;
            }
          }),
        )
        .subscribe(
          (res: any) => {
            if (res.success) {
              this.isFirstScroll = true;
              this.clearInput();
              this.getMessage(this.assetId);
              this.progress = 0;
            }
          },
          (err: any) => {
            this.loadingData = false;
            this.toast.danger('Something went wrong', 'Oops!');
          },
        );
    } else if (dataInput || (this.taskAssignedTo && this.taskTitle)) {
      const documents = {
        deletedDocuments: [],
        documents: this.pendingDocuments,
      };
      this.assetService
        .saveNotesTask(taskSubmission, documents)
        .pipe(
          map((event: any) => {
            switch (event.type) {
              case HttpEventType.UploadProgress:
                if (event.total) {
                  if (documents.documents.length) {
                    this.progress = Math.round((100 * event.loaded) / event.total);
                  } else {
                    this.progress = 0;
                  }
                }

                return { status: 'progress', message: this.progress };

              case HttpEventType.Response:
                return event.body;
              default:
                return `Unhandled event: ${event.type}`;
            }
          }),
        )
        .subscribe(
          (res: any) => {
            if (res.success) {
              this.isFirstScroll = true;
              this.progress = 0;
              this.clearInput();
              this.getMessage(this.assetId);
            }
          },
          (err: any) => {
            this.loadingData = false;
            this.toast.danger('Something went wrong', 'Oops!');
          },
        );
    }
  }

  async downloadFile(documentKey: any): Promise<void> {
    await this.documentService.getDocument({
      documentKey,
    });
  }
  isManager(): boolean {
    return this.sharedService.isManager();
  }

  getTasks(messages: any): void {
    const facility = this.facilityList.find((el) => el.id == this.assetId);
    const filterParams: any = {
      export: false,
      facility: facility ? facility.facilityName : null,
      pageNumber: 0,
      pageSize: 100,
      sortField: 'dueDate',
      sortOrder: 'asc',
    };
    this.assetService.getActivitiesTasks(filterParams).subscribe(
      (res: any) => {
        if (res.success) {
          filterParams['taskStatus'] = 1;
          this.assetService.getActivitiesTasks(filterParams).subscribe((res2: any) => {
            let tasks: any[] =
              res.payload && res.payload.tasks
                ? res.payload.tasks.filter((el: { taskType: AssetTaskType }) => el.taskType == AssetTaskType.NoteTask)
                : [];
            if (res2.success) {
              const tasks2 =
                res2.payload && res2.payload.tasks
                  ? res2.payload.tasks.filter(
                      (el: { taskType: AssetTaskType }) => el.taskType == AssetTaskType.NoteTask,
                    )
                  : [];
              tasks = tasks.concat(tasks2);
            }
            const mapUserBoolean = tasks.find((row: any) => Boolean(row.loanManagerId) || Boolean(row.reviewerId));
            if (mapUserBoolean) {
              tasks = tasks.reduce((agg: any, item: any) => {
                const o = { ...item };
                if (item.completedBy) {
                  o['completedByDate'] = new Date(item.completedByDate);
                }
                agg.push(o);
                return agg;
              }, []);
            }

            const taskChats = tasks.map((task: any) => {
              const user = this.originatorUsers.find((i: any) => i.userId == task.assignedTo);
              task.icon = task.completedBy ? 'checkmark-circle-2' : 'checkmark-circle-2-outline';
              if (
                new Date().setHours(0, 0, 0, 0) > new Date(task.dueDate).getTime() &&
                !task.completedBy &&
                task.completedBy == 0
              ) {
                task.background = 'rgba(252, 61, 61, 0.05)';
                task.overdue = true;
              } else if (task.completedBy && task.completedBy > 0) {
                task.background = '#F7F7F7';
                task.overdue = false;
              } else {
                task.background = '#FFFFFF';
                task.overdue = false;
              }
              const chatTask = {
                attachmentId: 0,
                attachments: task.noteDocuments || [],
                dateCreated: task.dueDate,
                facilityId: 0,
                facilityName: null,
                isAdmin: false,
                isInternalNote: true,
                isSender: this.userId == task.assignedTo,
                message: task.taskContent || '',
                messageTitle: task.taskTitle,
                messageType: 2,
                recipientId: 0,
                userName: user ? user.contactName : 'No User',
                date: 'dd LLL yyyy',
                completedBy: task.completedBy,
                taskInformation: task,
                task: true,
              };
              return chatTask;
            });
            this.scrollTop = true;
            messages.chats = messages.chats.map((chat: any) => {
              chat.date = 'd MMMM y h:mm a';
              return chat;
            });
            let messageChats = [...messages.chats, ...taskChats];
            messageChats = messageChats.map((el) => {
              el.avatarColor = el.isSender ? '#C5CBD7' : '#002c24';
              return el;
            });
            messageChats.sort((a, b) => {
              return new Date(b.dateCreated).getTime() - new Date(a.dateCreated).getTime();
            });
            this.messages = messageChats;
            this.originalMessage = [...messageChats];
            this.filterNotesTasks();
            this.loadingData = false;
            this.updating = false;
          });
        } else {
          this.loadingData = false;
          this.updating = false;
          this.toast.danger(res.error.message, 'Oops!');
        }
      },
      () => {
        this.loadingData = false;
        this.updating = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  toggleTaskCompletion(task: any) {
    if (this.sharedService.isOriginatorManager() || task.assignedTo == this.userId || task.createdBy == this.userId) {
      // || this.isAdmin()

      this.dialogService
        .open(ConfirmPopupComponent, {
          context: {
            title: 'Complete Task',
            message: 'Are you sure you want to proceed?',
            yesButton: 'Complete Task',
          },
          autoFocus: false,
          hasBackdrop: true,
          closeOnEsc: false,
          closeOnBackdropClick: false,
          hasScroll: false,
        })
        .onClose.subscribe((res: any) => {
          if (res) {
            task['Edited'] = true;
            task['completedBy'] = this.userId;
            task['completedByDate'] = new Date(Date.now());
            this.saveTask(task);
          }
        });
    }
  }

  private getAssetList(): void {
    this.loadingData = true;
    this.assetService.getAssetList().subscribe(
      (data: any) => {
        if (data.success) {
          this.facilityList = data.payload.assets.sort((a: any, b: any) =>
            a.facilityName.localeCompare(b.facilityName),
          );
          this.getOriginatorList();
        } else {
          this.loadingData = false;
          this.toast.danger(data.error.message, 'Oops!');
        }
      },
      () => {
        this.loadingData = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  editTask(task: any) {
    this.dialogService
      .open(AssetAddTaskComponent, {
        context: {
          data: task ? task : null,
          edit: task ? true : false,
          type: AssetTaskType.NoteTask,
          facilityList: this.facilityList,
        },
        autoFocus: false,
        hasBackdrop: true,
        closeOnEsc: false,
        closeOnBackdropClick: false,
        hasScroll: true,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          // this.unsavedItemsEvent.emit(0);
          // this.unsavedItems = false;
          // this.convenantView();
          this.getMessage(this.assetId);
        }
      });
  }
}
