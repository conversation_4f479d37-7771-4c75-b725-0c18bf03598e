import { CommonModule, CurrencyPipe } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { YSAdvancedPieChartComponent } from '@components/templates/advanced-pie-chart/advanced-pie-chart.component';
import { DashboardMessageType } from '@core/models/config';
import { DashboardService } from '@core/services/dashboard.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbIconModule,
  NbListModule,
  NbSpinnerModule,
  NbTabsetModule,
  NbUserModule,
} from '@nebular/theme';
import { Color, LegendPosition, NgxChartsModule, ScaleType } from '@swimlane/ngx-charts';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AssetDashboardComponent } from '../asset-dashboard/asset-dashboard.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NbCardModule,
    NbIconModule,
    NbSpinnerModule,
    NbListModule,
    NbTabsetModule,
    NbUserModule,
    NgxChartsModule,
    CurrencyPipe,
    YSAdvancedPieChartComponent,
    AssetDashboardComponent,
    NbButtonModule,
  ],
})
export class DashboardComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject();

  recentActivities!: any[];
  recentMessages!: any[];

  single = [];
  view: any[] = [700, 400];

  // options
  gradient = false;
  showLegend = true;
  showLabels = false;
  isDoughnut = true;
  legendPosition = LegendPosition.Below;

  colorScheme: Color = {
    name: 'customScheme',
    selectable: true,
    group: ScaleType.Ordinal,
    domain: [
      '#002C24',
      '#86B502',
      '#D9E021',
      '#A6B2AE',
      '#D1F2EF',
      '#3CBBB1',
      '#E7ECEB',
      // '#F28E2B',
      // '#E91E63',
      // '#9C27B0',
      // '#3E4550',
    ],
  };

  fundsDeployedData!: any[];
  fundsInvestedData!: any[];
  interestPaidByOfferingData!: any[];
  activeInvestorsData!: any[];
  applicationsMonthToDateData!: any[];
  currentInvestmentsData: any;
  investorId!: number;
  investedAmountByOfferingData!: any[];
  opportunitiesReviewedData!: any[];

  constructor(
    private route: Router,
    private currencyPipe: CurrencyPipe,
    private investorsService: InvestorsService,
    private dashboardService: DashboardService,
    private router: Router,
    private sharedService: SharedService,
  ) {}
  ngAfterViewInit(): void {
    // this.scrollToInvestor();
    setTimeout(() => {
      this.investorsService.account.pipe(takeUntil(this.destroy$)).subscribe((value: any) => {
        this.investorId = this.investorsService.accountValue?.investorId || 0;
        this.loadDashboard();
      });
    }, 1000);
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  public yAxisTickFormatting = (value: any) => `${this.currencyPipe.transform(value, 'USD', 'symbol', '0.0')}`;

  async ngOnInit(): Promise<void> {
    this.sharedService.showMessageSubject.subscribe((value) => {
      if (value.updateNotification) {
        this.getRecentLogs();
      }
    });
  }

  private loadDashboard(): void {
    if (this.isInvestor()) {
      this.getCurrentInvestments();
      this.getOpportunitiesReviewed();
    } else {
      // this.getFundsDeployed();
      this.getFundsInvested();
      // this.getInterestPaidByOffering();
      this.getActiveInvestors();
      this.getApplicationsMonthToDate();
      // this.getInvestedAmountByOffering();
    }
    this.getRecentLogs();
  }
  private getRecentLogs(): void {
    this.dashboardService
      .getRecentLogs({
        messageType: DashboardMessageType.Activity,
        investorId: this.isInvestor() ? this.investorId : undefined,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.recentActivities = data.payload;
        }
      });
    this.dashboardService
      .getRecentLogs({
        messageType: DashboardMessageType.Message,
        investorId: this.isInvestor() ? this.investorId : undefined,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.recentMessages = data.payload;
        }
      });
  }

  private getCurrentInvestments(): void {
    this.currentInvestmentsData = null;
    this.dashboardService
      .getCurrentInvestments({
        investorId: this.investorId,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.currentInvestmentsData = data.payload;
        }
      });
  }

  private getActiveInvestors(): void {
    this.dashboardService.getActiveInvestors({}).subscribe((data: any) => {
      if (data.success) {
        this.activeInvestorsData = data.payload;
      }
    });
  }

  private getApplicationsMonthToDate(): void {
    this.dashboardService.getApplicationsMonthToDate({}).subscribe((data: any) => {
      if (data.success) {
        this.applicationsMonthToDateData = data.payload;
      }
    });
  }

  private getFundsInvested(): void {
    this.dashboardService.getFundsInvested({}).subscribe((data: any) => {
      if (data.success) {
        this.fundsInvestedData = data.payload;
        // const payload = data.payload.map((elem: any) => {
        //   return {
        //     name: elem.month,
        //     value: elem.amount
        //   };
        // });

        // this.fundsInvestedData = [{
        //   name: 'Amount',
        //   series: payload
        // }];
      }
    });
  }

  private getOpportunitiesReviewed(): void {
    this.dashboardService.getOpportunitiesReviewed({}).subscribe((data: any) => {
      if (data.success) {
        const payload = data.payload.map((elem: any) => {
          return {
            name: elem.month,
            value: elem.amount,
            title: elem.title,
            monthIndex: elem.monthIndex,
          };
        });

        this.opportunitiesReviewedData = payload.sort((a: any, b: any) => {
          return a.monthIndex - b.monthIndex;
        });
        this.scrollToInvestor();
      }
    });
  }

  groupArrayOfObjects(list: any, key: string): any[] {
    return list.reduce((rv: any, x: any) => {
      (rv[x[key]] = rv[x[key]] || []).push({
        name: x.entityName,
        value: x.amount,
      });
      return rv;
    }, {});
  }

  onSelect(data: any): void {
    // console.log('Item clicked', JSON.parse(JSON.stringify(data)));
  }

  onActivate(data: any): void {
    // console.log('Activate', JSON.parse(JSON.stringify(data)));
  }

  onDeactivate(data: any): void {
    // console.log('Deactivate', JSON.parse(JSON.stringify(data)));
  }

  createWorkspace(): void {}

  getTotal(itemArray: any[]): number {
    return itemArray
      .map((a) => a.value)
      .reduce((a, b) => {
        return a + b;
      });
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isInvestorStaffUsers(): boolean {
    return this.sharedService.isInvestorStaffUsers();
  }

  isAssetStaffUsers(): boolean {
    return this.sharedService.isAdmin() || this.sharedService.isOriginatorManager();
  }

  isLender(): boolean {
    return this.sharedService.isLender();
  }

  isAssetDashboard(): boolean {
    return this.route.url.includes('/asset/dashboard') || this.isLender() || this.sharedService.isOriginatorManager();
  }

  scrollToInvestor(): void {
    setTimeout(() => {
      const elementList = document.querySelectorAll('.total-value');
      const element = elementList[0] as HTMLElement;
      if (element && !element.innerHTML.includes('$')) {
        element.innerHTML = `$${element.innerHTML.trim()}`;
      }

      const elementList1 = document.querySelectorAll('.item-value');
      const element1 = elementList1[0] as HTMLElement;
      if (element1 && !element1.innerHTML.includes('$')) {
        element1.innerHTML = `$${element1.innerHTML.trim()}`;
      }
      const element2 = elementList1[1] as HTMLElement;
      if (element2 && !element2.innerHTML.includes('$')) {
        element2.innerHTML = `$${element2.innerHTML.trim()}`;
      }
      const element3 = elementList1[2] as HTMLElement;
      if (element3 && !element3.innerHTML.includes('$')) {
        element3.innerHTML = `$${element3.innerHTML.trim()}`;
      }
    }, 2000);
  }

  setShowMessageValue(log: any): void {
    this.updateNotification(log.investorId);
    setTimeout(() => {
      this.sharedService.setShowMessageValue({
        investorId: log.investorId,
        showChat: true,
        updateNotification: true,
      });
    }, 500);
  }

  investmentDashboard(investment: any): void {
    this.sharedService.setFormParamValue({
      investmentId: investment.id,
      changeTab: false,
    });
    this.router.navigate(['/investor/investment-dashboard']);
  }

  private updateNotification(investorId: number): void {
    this.investorsService.getUpdateNotification({ investorId }).subscribe((response: any) => {
      this.getRecentLogs();
    });
  }
}
