{"name": "sydney-wyde", "version": "0.0.0", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:uat": "ng build --configuration=uat", "build:prod": "ng build --configuration production", "build:dev": "ng build --configuration dev", "test": "ng test", "lint": "ng lint", "lint:fix": "eslint --fix .", "prettier": "prettier --write .", "e2e": "ng e2e", "preinstall": "npx npm-force-resolutions", "prepare": "husky install"}, "engines": {"node": ">=22.14.0"}, "overrides": {"string-width": "^4.2.3"}, "lint-staged": {"src/*.{js,ts}": ["prettier --write", "eslint --fix"], "*/**/*.{html,json,css,md}": ["prettier --write"]}, "dependencies": {"@abacritt/angularx-social-login": "^2.4.0", "@angular/animations": "^19.2.14", "@angular/cdk": "^19.2.18", "@angular/common": "^19.2.14", "@angular/compiler": "^19.2.14", "@angular/core": "^19.2.14", "@angular/forms": "^19.2.14", "@angular/platform-browser": "^19.2.14", "@angular/platform-browser-dynamic": "^19.2.14", "@angular/router": "^19.2.14", "@azure/msal-angular": "^4.0.12", "@azure/msal-browser": "^4.13.2", "@ckeditor/ckeditor5-angular": "^9.1.0", "@nebular/date-fns": "^15.0.0", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@primeng/themes": "^19.1.3", "@swimlane/ngx-charts": "^22.0.0", "bootstrap": "^4.6.0", "country-flag-icons": "^1.4.17", "date-fns": "^2.18.0", "enhanced-resolve": "^3.3.0", "eva-icons": "^1.1.3", "jquery": "^3.4.1", "jwt-decode": "^3.1.2", "ngx-mask": "^19.0.7", "ngx-spinner": "^19.0.0", "primeicons": "^5.0.0", "primeng": "^19.1.3", "rxjs": "^7.8.1", "tailwindcss-animate": "^1.0.7", "tslib": "^2.8.1", "whatwg-fetch": "^3.6.2", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular-eslint/builder": "^20.1.0", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.2.14", "@eslint/js": "^9.29.0", "@schematics/angular": "^19.2.15", "@tailwindcss/postcss": "^4.1.10", "@types/d3-scale": "^4.0.9", "@types/d3-selection": "^3.0.11", "@types/d3-shape": "^3.1.7", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~3.6.0", "@types/jquery": "^3.3.33", "@types/node": "^18.19.0", "angular-eslint": "20.0.0", "ckeditor5": "^45.2.0", "ckeditor5-premium-features": "^45.2.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "globals": "^16.2.0", "husky": "^8.0.0", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.5.3", "protractor": "~7.0.0", "sass": "^1.89.2", "tailwindcss": "^4.1.10", "ts-node": "~10.9.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}}