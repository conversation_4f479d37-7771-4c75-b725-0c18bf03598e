@use "themes" as *;

:host {
  .title h5 {
    text-decoration-line: underline;
    text-underline-offset: 5px;
    font-weight: 400;
    font-size: 24px;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }
}
nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

p {
  font-style: normal;
  font-weight: normal;
  font-size: 15px;
  line-height: 30px;
}

li.li1 {
  line-height: 28px;
}

.title {
  font-weight: 600;
  font-size: 20px;
  line-height: 40px;
  color: #0a0a0a;
}

.outer-circle,
.inner-circle {
  left: 13px !important;
}

.text-mute {
  color: #838d9c;
}

.files-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin: 5px 0px;

  nb-icon {
    font-size: 35px;
    margin-right: 3px;
  }

  .single-file {
    display: flex;

    .name {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-velvet-700);
      margin: 0;
    }

    .info {
      display: flex;
      padding: 10px;
      justify-content: space-around;
      align-items: center;
      margin: 4px 3px;
    }
  }
}
