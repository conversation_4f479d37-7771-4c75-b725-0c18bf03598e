<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <div *ngIf="!assetKeyDataId; then saveText; else updateText"></div>
      <ng-template #saveText>
        <h5>New Asset</h5>
      </ng-template>
      <ng-template #updateText>
        <h5>{{ entityName }}</h5>
      </ng-template>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="w-full px-2">
    <nb-card style="box-shadow: 0px 0px 4px 1px #d3d3d36b; border-radius: 12px">
      <nb-card-body style="padding: 0">
        <ng-container>
          <nb-tabset *ngIf="!isMobile" class="hide-tab-mobile" (changeTab)="tabClick($event)" #tabset>
            <nb-tab
              tabId="1"
              [responsive]="'true'"
              tabTitle="Key Data"
              [tabIcon]="{ icon: 'notesIcon', pack: 'custom' }"
              active
            >
              <div style="width: 100%" *ngIf="tabId === 1">
                <app-asset-key-data (userChange)="userChange($event)" (changeTab)="changeTab($event)">
                </app-asset-key-data>
              </div>
            </nb-tab>

            <nb-tab
              tabId="2"
              [disabled]="!assetKeyDataId"
              tabTitle="Lender Updates"
              [tabIcon]="{ icon: 'overviewIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 2">
                <app-asset-lender-updates (changeTab)="changeTab($event)"></app-asset-lender-updates>
              </div>
            </nb-tab>

            <nb-tab
              tabId="3"
              [disabled]="!assetKeyDataId"
              tabTitle="Documents"
              [tabIcon]="{ icon: 'document30Icon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 3">
                <app-asset-documents (changeTab)="changeTab($event)"></app-asset-documents>
              </div>
            </nb-tab>

            <nb-tab
              [tabId]="checklistIds[0].tabId"
              [disabled]="!assetKeyDataId"
              [tabTitle]="checklistIds[0].title"
              [tabIcon]="{ icon: 'checklistIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === checklistIds[0].tabId">
                <app-asset-dynamic-checklist
                  [assetKeyDataId]="assetKeyDataId"
                  [tabId]="checklistIds[0].tabId"
                  [checkListId]="checklistIds[0].id"
                  (unsavedItemsEvent)="unsavedItems = $event"
                >
                </app-asset-dynamic-checklist>
              </div>
            </nb-tab>

            <nb-tab
              tabId="5"
              [disabled]="!assetKeyDataId"
              tabTitle="Notes & Tasks"
              [tabIcon]="{ icon: 'notesIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 5">
                <app-asset-notes-chat *ngIf="assetKeyDataId" [assetId]="assetKeyDataId" [isInternalNote]="true">
                </app-asset-notes-chat>
              </div>
            </nb-tab>

            <nb-tab
              tabId="6"
              [disabled]="!assetKeyDataId"
              tabTitle="Covenants"
              [tabIcon]="{ icon: 'covenantTabIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === 6">
                <app-asset-covenants (userChange)="userChange($event)" (unsavedItemsEvent)="unsavedItems = $event">
                </app-asset-covenants>
              </div>
            </nb-tab>

            <nb-tab
              [tabId]="checklistIds[1].tabId"
              [disabled]="!assetKeyDataId"
              [tabTitle]="checklistIds[1].title"
              [tabIcon]="{ icon: 'checklistIcon', pack: 'custom' }"
            >
              <div *ngIf="tabId === checklistIds[1].tabId">
                <app-asset-dynamic-checklist
                  [assetKeyDataId]="assetKeyDataId"
                  [tabId]="checklistIds[1].tabId"
                  [checkListId]="checklistIds[1].id"
                  (unsavedItemsEvent)="unsavedItems = $event"
                >
                </app-asset-dynamic-checklist>
              </div>
            </nb-tab>
          </nb-tabset>
        </ng-container>

        <nb-accordion *ngIf="isMobile" class="show-on-mobile">
          <nb-accordion-item>
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="overviewIcon" pack="custom"></nb-icon> Key Data
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-asset-key-data (userChange)="userChange($event)" (changeTab)="changeTab($event)">
              </app-asset-key-data>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item [disabled]="!assetKeyDataId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="financialsIcon" pack="custom"></nb-icon> Lender Updates
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-asset-lender-updates (changeTab)="changeTab($event)"></app-asset-lender-updates>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item [disabled]="!assetKeyDataId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="document30Icon" pack="custom"></nb-icon> Documents
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-asset-documents (changeTab)="changeTab($event)"></app-asset-documents>
            </nb-accordion-item-body>
          </nb-accordion-item>

          <nb-accordion-item [disabled]="!assetKeyDataId">
            <nb-accordion-item-header>
              <nb-icon class="accordion-icon" icon="notesIcon" pack="custom"></nb-icon> Notes
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <app-asset-notes-chat *ngIf="assetKeyDataId" [assetId]="assetKeyDataId" [isInternalNote]="true">
              </app-asset-notes-chat>
            </nb-accordion-item-body>
          </nb-accordion-item>
        </nb-accordion>
      </nb-card-body>
    </nb-card>
  </div>
</div>
