import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetector<PERSON>ef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { TypeKey } from '@core/models/config';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { InvestorPaymentsDeleteComponent } from '@features/investors-management/investor-payments-delete/investor-payments-delete.component';
import { InvestorPaymentsImportComponent } from '@features/investors-management/investor-payments-import/investor-payments-import.component';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { forkJoin, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-investor-payments',
  templateUrl: './investor-payments.component.html',
  styleUrls: ['./investor-payments.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    NbButtonModule,
  ],
})
export class InvestorPaymentsComponent
  implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  payments: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  transactionTypeData: any;
  investmentData: any;

  investorId: number | undefined;
  destroyed$ = new Subject<boolean>();
  dateFilterData: any[] = [];

  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private dialogService: NbDialogService,
    private investmentService: InvestmentService,
    private cdr: ChangeDetectorRef,
  ) { }

  async ngOnInit(): Promise<void> {
    this.dateFilterData = this.sharedService.getDateFilterRows();

    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
    } as Filters;

    if (this.isInvestor) {
      this.investorId = this.investorsService.accountValue.investorId;
    }

    forkJoin([this.getTransactionType(), this.getInvestmentLookup()])
      .pipe(takeUntil(this.destroyed$))
      .subscribe(
        ([tranTypes, lookups]) => {
          this.transactionTypeData = tranTypes.payload;
          this.investmentData = lookups.payload;
        },
        (e) => {
          console.log(e);
        },
      );
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    if (this.isInvestor) {
      this.filterParams.investorId = this.investorId;
    }

    this.investorsService.getPayments(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.payments = (data as any).payload.investorPayments;
        this.totalRecords = (data as any).payload.rows;
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  get isManager(): boolean {
    return this.sharedService.isManager();
  }

  get isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.unsubscribe();
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  dateChange(event: any): void { }

  exportPayment(): void {
    this.filterParams.export = true;
    this.toast.default(`Downloading started`, '', {
      icon: 'download',
    });
    this.investorsService.getPaymentsExport(this.filterParams);
  }

  importPayment(): void {
    this.dialogService
      .open(InvestorPaymentsImportComponent, {
        context: {
          getFormParamValue: {
            investorId: 0,
            investmentId: 0,
          }, // investor id and investment id will be extracted from excel file
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.toast.success('Payments uploaded.', 'Success!');
          this.getList();
        }
      });
  }

  getTransactionType(): Observable<any> {
    return this.investmentService.getEntityType({
      typeKey: TypeKey.TransactionType,
    });
  }

  getInvestmentLookup(): Observable<any> {
    return this.investmentService.getInvestmentLookup({});
  }

  deletePayment(paymentId: number): void {
    if (paymentId) {
      this.dialogService
        .open(InvestorPaymentsDeleteComponent, {
          context: {
            paymentId,
          },
          autoFocus: false,
        })
        .onClose.subscribe((res: any) => {
          if (res) {
            this.getList();
          }
        });
    }
  }
}
