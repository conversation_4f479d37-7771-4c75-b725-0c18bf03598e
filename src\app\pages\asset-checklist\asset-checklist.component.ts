import { ChangeDetectorRef, Component, OnInit, ViewChild, AfterViewChecked, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { AssetService } from '@core/services/asset.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { NbDialogService, NbToastrService, NbCardModule, NbIconModule, NbButtonModule } from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { TableLazyLoadEvent } from 'primeng/table';
import { Subject } from 'rxjs';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';

@Component({
  selector: 'app-asset-checklist',
  templateUrl: './asset-checklist.component.html',
  styleUrls: ['./asset-checklist.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbIconModule, TableModule, NbButtonModule],
})
export class AssetChecklistComponent implements OnInit, AfterViewChecked, OnDestroy {
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  checklists: any[] = [];
  selectedChecklist: any = {};
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private assetService: AssetService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private dialogService: NbDialogService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 50,
      sortField: 'dateCreated',
      sortOrder: 'desc',
    } as Filters;
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    this.assetService.getChecklists(this.filterParams).subscribe(
      (data: any) => {
        if (data.success) {
          // TODO: get created by name from userId
          this.checklists = data.payload.checklists;
          this.totalRecords = data.payload.rows;
          this.dtTrigger.next(this.checklists);
        } else {
          this.toast.danger(data.error.message, 'Oops!');
        }
        this.spinner.hide();
        this.loading = false;
      },
      () => {
        this.spinner.hide();
        this.loading = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  ngOnDestroy(): void {}

  editChecklist(checklist: any): void {
    this.loading = true;
    this.assetService.getChecklistContent(checklist.id).subscribe((res: any) => {
      if (res.success) {
        this.selectedChecklist = {
          checklistName: checklist.checklistName,
          checklistId: checklist.id,
          checklistStatus: checklist.status,
          checklistStructure: [],
        };
        if (res.payload.length) {
          this.selectedChecklist.checklistStructure = res.payload.map((t: any) => {
            return {
              id: t.id,
              type: t.contentType,
              title: t.contentTitle,
              content: t.contentHtml,
              order: t.contentOrder,
              edit: true,
              active: 1,
            };
          });
        }
        this.createChecklist();
      } else {
        this.loading = false;
      }
    });
  }

  createChecklist(): void {
    this.sharedService.setFormParamValue({});
    this.loading = false;
    if (this.selectedChecklist.checklistId) {
      // edit existing
      this.assetService.setChecklistStructureValue(this.selectedChecklist);
    } else {
      // add empty checklist
      this.assetService.setChecklistStructureValue({
        checklistName: '',
        checklistId: undefined,
        checklistStatus: 0,
        checklistStructure: [],
      });
    }
    this.router.navigate(['/asset/checklist-builder']);
  }

  archiveChecklistConfirm(checklist: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Archive Checklist',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Archive Checklist',
          yesButtonIcon: 'archive-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.archiveChecklist(checklist);
        }
      });
  }

  archiveChecklist(checklist: any): void {
    this.assetService
      .archiveChecklist({
        ChecklistName: checklist.checklistName,
        Status: 2,
        Id: checklist.id,
        IsArchived: 1,
      })
      .subscribe((response: any) => {
        if (response.success) {
          this.dialogService
            .open(ConfirmPopupComponent, {
              context: {
                yesButton: 'CLOSE',
                yesSuccessPopUp: '',
                message: 'Checklist successfully archived',
                icon: 'checkmark-circle-2-outline',
              },
              autoFocus: false,
              hasBackdrop: true,
              closeOnEsc: false,
              closeOnBackdropClick: false,
              hasScroll: false,
            })
            .onClose.subscribe((res: any) => {
              if (res) {
                this.getList();
              }
            });
        } else {
          this.toast.danger(response.error.message, 'Oops!');
        }
      });
  }
}
