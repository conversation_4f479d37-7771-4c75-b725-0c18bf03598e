import { UpdatePublishStatus } from '../config';
import { HttpResponse } from './http.response';

export interface AssetLendersResponse extends HttpResponse {
  payload: Lenders[];
}

export interface Lenders {
  id: number;
  updateTitle: string;
  status: string;
  publishStatus: string;
  updatePublishStatusId: UpdatePublishStatus;
  financialClose: Date;
  maturityDate: Date;
  repaymentDate: Date;
}
