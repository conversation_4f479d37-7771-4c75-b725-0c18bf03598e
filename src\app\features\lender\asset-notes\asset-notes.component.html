<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2">
      <h5 class="w-6/12 px-2">
        <div class="title">
          {{ !editMode ? "View Note" : editingNote ? "Edit Note" : "Add Note" }}
        </div>
      </h5>
      <div class="w-6/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="close()">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div style="width: 800px; margin: 22px 50px">
      <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
        <p style="font-weight: bold; font-size: 16px; margin-bottom: 0px">Description</p>
        <app-ckeditor [ngClass]="editMode ? '' : 'removeToolbar'" [(ngModel)]="editorContent"> </app-ckeditor>
      </div>
      <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
        <nb-progress-bar *ngIf="progress" style="width: 100%" [value]="progress" status="primary">
          Uploading {{ progress }}%
        </nb-progress-bar>
      </div>
      <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]" *ngIf="editMode">
        <p style="font-weight: bold; font-size: 16px; margin-bottom: 0px">Attach Files</p>
        <div class="file-container" appDnd (fileDropped)="onFileDropped($event)">
          <input type="hidden" />
          <input
            type="file"
            multiple
            #fileDropRef
            id="fileDropRef"
            (change)="fileBrowseHandler($event)"
            accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
          />
          <p class="m-0" for="fileDropRef">
            <nb-icon icon="file-add"></nb-icon>
            Drop Document here or Click to upload.
          </p>
        </div>
      </div>
      <div class="w-8/12 px-2" *ngIf="pendingDocuments && pendingDocuments.length > 0">
        <div class="files-list">
          <div class="single-file" *ngFor="let file of pendingDocuments; let i = index">
            <div class="info">
              <div class="attachment">
                <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
              </div>
              <div class="name">
                {{ file?.name }}
              </div>
              <div class="delete" *ngIf="editMode" (click)="clearDocuments(file, false)">
                <nb-icon class="file-delete" icon="close-circle"></nb-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-8/12 px-2" *ngIf="uploadedDocuments && uploadedDocuments.length > 0">
        <div class="files-list">
          <div class="single-file" *ngFor="let file of uploadedDocuments; let i = index">
            <div class="info">
              <div class="attachment">
                <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
              </div>
              <div class="name" (click)="downloadFile(file)">
                {{ file?.documentName }}
              </div>
              <div class="delete" *ngIf="editMode" (click)="clearDocuments(file, true)">
                <nb-icon class="file-delete" icon="close-circle"></nb-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer *ngIf="editMode">
    <div class="flex flex-wrap -mx-2" style="margin-right: 9px; justify-content: end">
      <button
        [nbSpinner]="saveLoading"
        nbButton
        status="primary"
        style="margin-left: 10px; min-width: 135px"
        [disabled]="updating"
        nbTooltip="Save"
        (click)="onSubmit()"
        nbTooltipStatus="control"
        nbTooltipPlacement="bottom"
      >
        SAVE
      </button>
    </div>
  </nb-card-footer>
</nb-card>
