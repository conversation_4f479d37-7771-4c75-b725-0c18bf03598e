import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { TypeKey } from '@core/models/config';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbCheckboxModule,
  NbIconModule,
  NbInputModule,
  NbPopoverModule,
  NbToastrService,
  NbTooltipModule,
} from '@nebular/theme';
import { SkeletonModule } from 'primeng/skeleton';

@Component({
  selector: 'app-investment-financials',
  templateUrl: './investment-financials.component.html',
  styleUrls: ['./investment-financials.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbCheckboxModule,
    NbAlertModule,
    NbInputModule,
    NbPopoverModule,
    NbTooltipModule,
    SkeletonModule,
    NbButtonModule,
    CkeditorComponent,
  ],
})
export class InvestmentFinancialsComponent implements OnInit {
  @Output() changeTab = new EventEmitter<boolean>();
  @ViewChild('editor') editor!: ElementRef;
  @Input() investmentId: any;

  financialsForm: UntypedFormGroup = new UntypedFormGroup({});
  financialsData: any;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';
  editMode = false;
  investorId: any;
  iconsData: any;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private investmentService: InvestmentService,
    private investorsService: InvestorsService,
    protected cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.investmentId = this.sharedService.getFormParamValue.investmentId;

    if (this.isInvestor()) {
      this.investorId = this.investorsService.accountValue.investorId;
    } else if (this.isAdmin()) {
      this.editMode = true;
    }

    this.financialsForm = this.formBuilder.group({
      financials: this.formBuilder.array([]),
    });

    if (this.investmentId) {
      this.getFinancials();
    }
    // else {
    //   this.addFinancials();
    // }

    this.getInvestmentIcons();
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isManager(): boolean {
    return this.sharedService.isManager();
  }

  private getInvestmentIcons(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.InvestmentIcon,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.iconsData = userData.payload;
        }
      });
  }

  private getFinancials(): void {
    this.financials.clear();
    this.investmentService.getFinancial(this.investmentId).subscribe((response: any) => {
      if (response.success) {
        if (response.payload && response.payload.length > 0) {
          this.financialsData = response.payload;
          if (this.financialsData) {
            response.payload.forEach((ele: any) => {
              ele.investmentDate = new Date(ele.investmentDate);
              ele.editMode = false;
              const data = this.formBuilder.group(ele);
              data.disable();
              this.financials.push(data);
            });
            for (let index = 3; index > response.payload.length; index--) {
              if (!this.isInvestor()) {
                const indexPassNew = index - response.payload.length;
                const value = this.newFinancials(indexPassNew);
                value.disable();
                this.financials.push(value);
              }
            }
          }
        } else {
          if (!this.isInvestor()) {
            for (let index = 3; index > 0; index--) {
              const value = this.newFinancials(index);
              value.disable();
              this.financials.push(value);
            }
          }
        }
      }
    });
  }

  newFinancials(index: number): UntypedFormGroup {
    let title = '';
    if (index === 3) {
      title = 'Facility Limit';
    } else if (index === 2) {
      title = 'Security';
    } else if (index === 1) {
      title = 'Cost Expenses';
    }

    return this.formBuilder.group({
      id: 0,
      title: [title, Validators.required], // title is never null now
      description: ['', Validators.required],
      iconId: [null, Validators.required],
      iconUrl: [null],
      investmentId: this.investmentId,
      editMode: [false],
    });
  }

  enableEditMode(control: any, value: boolean): void {
    if (value) {
      control.enable();
    } else {
      this.onSubmit(control);
      control.disable();
    }
    control.patchValue({
      editMode: value,
    });

    this.cd.detectChanges();
  }

  public get financials(): UntypedFormArray {
    return this.financialsForm.get('financials') as UntypedFormArray;
  }

  getValue(i: number, controlName: string): string {
    const control = ((this.financialsForm.get('financials') as UntypedFormArray).controls[i] as UntypedFormGroup)
      .controls[controlName];
    return control.value;
  }

  getFinancialsValidity(i: number, controlName: string): string {
    const control = ((this.financialsForm.get('financials') as UntypedFormArray).controls[i] as UntypedFormGroup)
      .controls[controlName];
    return this.submitted && control.errors?.required && (control.dirty || control.touched || control.invalid)
      ? 'danger'
      : 'basic';
  }

  // addFinancials(): void {
  //   this.submitted = false;
  //   this.financials.push(this.newFinancials());
  // }

  selectIcon(index: number, icon: any): void {
    const valueControl = (
      (this.financialsForm.get('financials') as UntypedFormArray).controls[index] as UntypedFormGroup
    ).controls.iconUrl;
    valueControl.setValue(icon.value);
    const idControl = ((this.financialsForm.get('financials') as UntypedFormArray).controls[index] as UntypedFormGroup)
      .controls.iconId;
    idControl.setValue(icon.id);
  }

  onSubmit(investmentData: any): void {
    this.submitted = true;

    this.loading = true;

    const payload = {
      id: investmentData.value.id ? investmentData.value.id : undefined,
      investmentId: investmentData.value.investmentId,
      title: investmentData.value.title,
      description: investmentData.value.description,
      isDeleted: investmentData.value.isDeleted,
      iconId: investmentData.value.iconId,
    };
    this.investmentService.saveFinancial(payload).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            this.toastr.success('Saved Successfully', 'Success!');
            this.loading = false;
            if (this.financialsData) {
              this.getFinancials();
            }
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        console.log(err);
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  backtoList(): void {
    this.router.navigate(['/users']);
  }
}
