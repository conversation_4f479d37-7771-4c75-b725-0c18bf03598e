<form class="w-full px-2" [formGroup]="overviewForm" (ngSubmit)="onSubmit()">
  <div class="w-full px-2 my-[15px]">
    <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Entity Name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="entityName"
        status="{{ submitted && f.entityName.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.entityName.errors" class="invalid-feedback">
        <div *ngIf="f.entityName.errors.required">Entity Name is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Entity Type </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="entityTypeId"
        formControlName="entityTypeId"
        status="{{ submitted && f.entityTypeId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let entityType of entityTypeData" [value]="entityType.id" [disabled]="entityType.disabled">
          {{ entityType.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.entityTypeId.errors" class="invalid-feedback">
        <div *ngIf="f.entityTypeId.errors.required">Entity Type is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong> Entity Address </strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <input
        nbInput
        fullWidth
        placeholder="Search address"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="address"
        status="{{ submitted && f.address.errors ? 'danger' : 'basic' }}"
        autocomplete="off"
        #search
        id="address"
        name="address"
      />
      <div *ngIf="submitted && f.address.errors" class="invalid-feedback">
        <div *ngIf="f.address.errors.required">Address is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Entity Status </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="statusId"
        formControlName="statusId"
        status="{{ submitted && f.statusId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let status of statusData" [value]="status.id" [disabled]="status.disabled">
          {{ status.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.statusId.errors" class="invalid-feedback">
        <div *ngIf="f.statusId.errors.required">Entity Status is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <nb-checkbox formControlName="isExisting" (checkedChange)="toggle($event)">
        <strong> Is Contact an Existing User? </strong>
      </nb-checkbox>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Contact First name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="firstName"
        status="{{ submitted && f.firstName.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
        <div *ngIf="f.firstName.errors.required">First name is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong>Contact Last name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="lastName"
        status="{{ submitted && f.lastName.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.lastName.errors" class="invalid-feedback">
        <div *ngIf="f.lastName.errors.required">Last name is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong> Email </strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <input
        nbInput
        fullWidth
        placeholder="<EMAIL>"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="email"
        status="{{ submitted && f.email.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
        <div *ngIf="f.email.errors.required">Email is required.</div>
        <div *ngIf="f.email.errors.pattern">Invalid email format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong> Mobile </strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <nb-form-field>
        <!-- <span nbPrefix class="flagIcon"> <img class="flag" src="assets/images/flag.png" />
                    <span class="label code-text">+61</span> </span> -->

        <span nbPrefix class="flagIcon">
          <!-- <img class="flag" src="assets/images/flag.png" />
                        <span class="label code-text">+61</span> -->

          <p-select
            [options]="countries"
            optionLabel="name"
            [(ngModel)]="selectedCountry"
            formControlName="countryCode"
            [filter]="true"
            filterBy="name"
            styleClass="shadow-none"
          >
            <ng-template pTemplate="selectedItem">
              <div class="flex items-center gap-1 text-velvet-700" *ngIf="selectedCountry">
                <img
                  src="https://flagcdn.com/16x12/{{ selectedCountry.code.toLowerCase() }}.png"
                  width="16"
                  height="12"
                  alt="{{ selectedCountry.code }}"
                />

                {{ selectedCountry.dial_code }}
              </div>
            </ng-template>
            <ng-template let-country pTemplate="item">
              <div class="flex items-center gap-1 text-velvet-700">
                <img
                  src="https://flagcdn.com/16x12/{{ country.code.toLowerCase() }}.png"
                  width="16"
                  height="12"
                  alt="{{ country.code }}"
                />
                {{ country.name }} {{ country.dial_code }}
              </div>
            </ng-template>
          </p-select>
        </span>

        <input
          nbInput
          fullWidth
          placeholder="4XXXXXXXX"
          mask="X00000000"
          [patterns]="pattern"
          [dropSpecialCharacters]="false"
          maxlength="10"
          minlength="9"
          shape="semi-round"
          type="text"
          fieldSize="large"
          formControlName="mobile"
          status="{{ submitted && f.mobile.errors ? 'danger' : 'basic' }}"
        />
      </nb-form-field>

      <div *ngIf="submitted && f.mobile.errors" class="invalid-feedback">
        <div *ngIf="f.mobile.errors.required">Mobile is required.</div>
        <div *ngIf="f.mobile.errors.pattern">Invalid mobile format.</div>
        <div *ngIf="f.mobile.errors.mask">Invalid mobile format.</div>
      </div>
    </div>
  </div>

  <div class="w-full px-2 my-[15px]">
    <button
      [disabled]="loading || overviewForm.disabled"
      class="float-right"
      [nbSpinner]="loading"
      nbButton
      status="primary"
      style="min-width: 135px"
    >
      <div *ngIf="!investorId; then saveText; else updateText"></div>
      <ng-template #saveText> SAVE </ng-template>
      <ng-template #updateText> UPDATE </ng-template>
    </button>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>
