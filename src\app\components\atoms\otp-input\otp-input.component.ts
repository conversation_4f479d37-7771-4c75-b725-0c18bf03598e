import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, Input, OnInit, output } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { NumberOnlyDirective } from '@core/directives/number-only.directive';
import { Config } from '@core/models/config';
import { KeysPipe } from '@core/pipes/keys.pipe';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  selector: 'app-otp-input',
  templateUrl: './otp-input.component.html',
  styleUrls: ['./otp-input.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, KeysPipe, NumberOnlyDirective, NgxMaskDirective],
})
export class OtpInputComponent implements OnInit, AfterViewInit {
  @Input() config: Config = { length: 4 };
  inputChange = output<string>();
  otpForm: any;
  inputControls: UntypedFormControl[] = new Array(this.config.length);
  componentKey = Math.random().toString(36).substring(2) + new Date().getTime().toString(36);
  inputType?: string;
  constructor(private keysPipe: KeysPipe) {}

  ngOnInit() {
    this.otpForm = new UntypedFormGroup({});
    for (let index = 0; index < this.config.length; index++) {
      this.otpForm.addControl(this.getControlName(index), new UntypedFormControl());
    }
    this.inputType = this.getInputType();
    if (this.config.value) {
      this.setValue(this.config.value);
    }
  }
  ngAfterViewInit(): void {
    if (!this.config.disableAutoFocus) {
      const containerItem = document.getElementById(`c_${this.componentKey}`);
      if (containerItem) {
        containerItem.addEventListener('paste', (evt) => this.handlePaste(evt));
        const ele: any = containerItem.getElementsByClassName('otp-input')[0];
        if (ele && ele.focus) {
          ele.focus();
        }
      }
    }
  }
  private getControlName(idx: number) {
    return `ctrl_${idx}`;
  }

  ifLeftArrow(event: any) {
    return this.ifKeyCode(event, 37);
  }

  ifRightArrow(event: any) {
    return this.ifKeyCode(event, 39);
  }

  ifBackspaceOrDelete(event: any) {
    return event.key === 'Backspace' || event.key === 'Delete' || this.ifKeyCode(event, 8) || this.ifKeyCode(event, 46);
  }

  ifKeyCode(event: any, targetCode: any) {
    const key = event.keyCode || event.charCode;
    return key == targetCode ? true : false;
  }
  onKeyDown(event: any) {
    const isSpace = this.ifKeyCode(event, 32);
    if (isSpace) {
      // prevent space
      return false;
    } else {
      return true;
    }
  }

  onKeyUp(event: any, inputIdx: any) {
    const nextInputId = this.appendKey(`otp_${inputIdx + 1}`);
    const prevInputId = this.appendKey(`otp_${inputIdx - 1}`);
    if (this.ifRightArrow(event)) {
      this.setSelected(nextInputId);
      return;
    }
    if (this.ifLeftArrow(event)) {
      this.setSelected(prevInputId);
      return;
    }
    const isBackspace = this.ifBackspaceOrDelete(event);
    if (isBackspace && !event.target.value) {
      this.setSelected(prevInputId);
      this.rebuildValue();
      return;
    }
    if (!event.target.value) {
      return;
    }
    if (this.ifValidEntry(event)) {
      this.setSelected(nextInputId);
    }
    this.rebuildValue();
  }

  appendKey(id: any) {
    return `${id}_${this.componentKey}`;
  }

  setSelected(eleId: any) {
    this.focusTo(eleId);
    const ele: any = document.getElementById(eleId);
    if (ele && ele.setSelectionRange) {
      setTimeout(() => {
        ele.setSelectionRange(0, 1);
      }, 0);
    }
  }

  ifValidEntry(event: any) {
    const inp = String.fromCharCode(event.keyCode);
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    return (
      isMobile ||
      /[a-zA-Z0-9-_]/.test(inp) ||
      (this.config.allowKeyCodes && this.config.allowKeyCodes.includes(event.keyCode)) ||
      (event.keyCode >= 96 && event.keyCode <= 105)
    );
  }

  focusTo(eleId: any) {
    const ele: any = document.getElementById(eleId);
    if (ele) {
      ele.focus();
    }
  }

  // method to set component value
  setValue(value: any) {
    if (this.config.allowNumbersOnly && isNaN(value)) {
      return;
    }
    this.otpForm?.reset();
    if (!value) {
      this.rebuildValue();
      return;
    }
    value = value.toString().replace(/\s/g, ''); // remove whitespace
    Array.from(value).forEach((c, idx: any) => {
      if (this.otpForm?.get(this.getControlName(idx))) {
        this.otpForm.get(this.getControlName(idx))?.setValue(c);
      }
    });
    if (!this.config.disableAutoFocus) {
      const containerItem = document.getElementById(`c_${this.componentKey}`);
      const indexOfElementToFocus = value.length < this.config.length ? value.length : this.config.length - 1;
      const ele: any = containerItem?.getElementsByClassName('otp-input')[indexOfElementToFocus];
      if (ele && ele.focus) {
        ele.focus();
      }
    }
    this.rebuildValue();
  }

  rebuildValue() {
    let val = '';
    this.keysPipe.transform(this.otpForm?.controls).forEach((k) => {
      if (this.otpForm?.controls[k].value) {
        val += this.otpForm?.controls[k].value;
      }
    });
    this.inputChange.emit(val);
  }
  getInputType(): string {
    return this.config.isPasswordInput ? 'password' : this.config.allowNumbersOnly ? 'tel' : 'text';
  }
  handlePaste(e: any) {
    let pastedData: any;
    // Get pasted data via clipboard API
    const clipboardData = e.clipboardData || window.Clipboard;
    if (clipboardData) {
      pastedData = clipboardData.getData('Text');
    }
    // Stop data actually being pasted into div
    e.stopPropagation();
    e.preventDefault();

    if (!pastedData) {
      return;
    }

    this.setValue(pastedData);
  }
}
