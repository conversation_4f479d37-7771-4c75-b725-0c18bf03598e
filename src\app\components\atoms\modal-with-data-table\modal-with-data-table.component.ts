import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BaseComponent } from '@core/models/base.component';
import { NbAlertModule, NbButtonModule, NbCardModule, NbIconModule, NbSpinnerModule } from '@nebular/theme';
import { TableModule } from 'primeng/table';

@Component({
  selector: 'app-modal-with-data-table',
  imports: [CommonModule, NbCardModule, NbIconModule, NbButtonModule, NbAlertModule, NbSpinnerModule, TableModule],
  standalone: true,
  templateUrl: './modal-with-data-table.component.html',
  styleUrl: './modal-with-data-table.component.scss',
})
export class ModalWithDataTableComponent extends BaseComponent {
  @Input() data: any;
  @Input() title = '';
  @Input() loading = false;
  @Input() error = '';
  @Input() totalRecords = 0; // Total number of records in the data table.
  @Output() eventSave = new EventEmitter<any>(); // Emits the selected item when the user clicks the "Save" button.
  @Output() eventClose = new EventEmitter<boolean>(); // Emits when the user clicks the "Close" button.
  selectedItem: any;
  columns = [
    { field: 'id', header: 'ID' },
    { field: 'title', header: 'Title' },
    { field: 'borrower', header: 'Borrower' },
  ];

  constructor() {
    super();
    setTimeout(() => {
      console.log('data:', this.data);
    }, 1000);
  }

  onClose() {
    this.eventClose.emit(true);
  }

  onSave(item: any) {
    this.eventSave.emit(item);
  }
}
