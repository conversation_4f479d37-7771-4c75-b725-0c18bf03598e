<p-skeleton *ngIf="!documents"></p-skeleton>

<div *ngIf="documents">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="documents"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="false"
        [rows]="200"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="['id', 'description', 'investment', 'dateCreated', 'fileSize']"
        sortField="lastLogin"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 100px; max-width: 100px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>
            <th style="width: 250px" [pSortableColumn]="'fileName'">
              <div>
                <div>Description</div>
                <p-sortIcon [field]="'fileName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 250px" [pSortableColumn]="'documentType'">
              <div>
                <div>Document Type</div>
                <p-sortIcon [field]="'documentType'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 150px" [pSortableColumn]="'dateCreated'">
              <div>
                <div>Date</div>
                <p-sortIcon [field]="'dateCreated'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'fileSize'">
              <div>
                <div>Size</div>
                <p-sortIcon [field]="'fileSize'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 150px" *ngIf="isAdmin()">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-document>
          <tr>
            <td style="min-width: 100px; max-width: 100px">{{ document.id }}</td>
            <td style="width: 250px">
              <div class="text-velvet-700 hover:text-lime cursor-pointer" (click)="downloadFile(document)">
                {{ document.fileName }}
              </div>
            </td>
            <td style="min-width: 250px">{{ document.documentType }}</td>
            <td style="min-width: 150px">{{ document.dateCreated | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 150px" class="text-velvet-700">{{ document.fileSize }}KB</td>
            <td style="min-width: 150px" *ngIf="isAdmin()">
              <button
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                (click)="deleteDocument(document.documentKey, document.createdBy, investmentId)"
                nbTooltip="Delete Document"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="trash-2-outline"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" [attr.colspan]="10">No documents yet.</td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
<div class="w-full px-2">
  <div class="flex flex-wrap -mx-2" *ngIf="isAdmin()">
    <div class="w-6/12 px-2 my-[15px]">
      <button
        class="bg-velvet-700 hover:bg-velvet-600 text-white"
        *ngIf="true"
        nbButton
        status="default"
        type="button"
        (click)="addNewDocument()"
      >
        ADD NEW DOCUMENT
        <nb-icon icon="plus-outline"> </nb-icon>
      </button>
    </div>
    <!-- <div class="w-6/12 px-2 my-[15px]">
            <button class="float-right" (click)="next()" nbButton status="primary" style="min-width: 135px;">
                NEXT
            </button>
        </div> -->
  </div>
</div>
