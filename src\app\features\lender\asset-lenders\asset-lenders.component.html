<div>
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="lenders"
        [loading]="loading"
        [paginator]="false"
        [rows]="200"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="[
          'id',
          'updateTitle',
          'transactionType',
          'investment',
          'investor',
          'dateCreated',
          'amount',
          'investmentId',
          'transactionTypeId',
        ]"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 80px; max-width: 80px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 300px; max-width: 300px" [pSortableColumn]="'updateTitle'">
              <div>
                <div>Update Title</div>
                <p-sortIcon [field]="'updateTitle'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'status'">
              <div>
                <div>Status</div>
                <p-sortIcon [field]="'status'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 125px" [pSortableColumn]="'financialClose'">
              <div>
                <div>Financial Close</div>
                <p-sortIcon [field]="'financialClose'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 125px" [pSortableColumn]="'maturityDate'">
              <div>
                <div>Maturity Date</div>
                <p-sortIcon [field]="'maturityDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 125px" [pSortableColumn]="'repaymentDate'">
              <div>
                <div>Repayment Date</div>
                <p-sortIcon [field]="'repaymentDate'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'publishStatus'">
              <div>
                <div>Publish Status</div>
                <p-sortIcon [field]="'publishStatus'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px">
              <div>Action</div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-lender>
          <tr>
            <td style="min-width: 80px; max-width: 80px">{{ lender.id }}</td>
            <td style="min-width: 300px; max-width: 300px">{{ lender.updateTitle }}</td>
            <td style="min-width: 150px">{{ lender.status }}</td>
            <td style="min-width: 125px">{{ lender.financialClose | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 125px">{{ lender.maturityDate | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 125px">{{ lender.repaymentDate | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 125px">{{ lender.publishStatus }}</td>
            <td style="min-width: 100px">
              <button
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                nbTooltip="Edit"
                (click)="editLender(lender)"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="editIcon" pack="custom"></nb-icon>
              </button>

              <button
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                nbTooltip="Archive"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
                (click)="confirmArchive(lender)"
              >
                <nb-icon icon="archiveIcon" pack="custom"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" [attr.colspan]="isInvestor ? 5 : 7">
              No lender updates yet.
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
