import { CommonModule, CurrencyPipe } from '@angular/common';
import { AfterContentChecked, AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { YSAdvancedPieChartComponent } from '@components/templates/advanced-pie-chart/advanced-pie-chart.component';
import { LoanPortfolioBytype } from '@core/helpers';
import { RecentMessages } from '@core/models/response/asset-recent-messges.response';
import {
  LoanPortfolioBytypePayload,
  LoanPortfolioBytypeResponse,
} from '@core/models/response/get-loan-portfolio-bytype.response';
import { AssetService } from '@core/services/asset.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbIconModule,
  NbInputModule,
  NbListModule,
  NbSidebarService,
  NbSpinnerModule,
  NbTabsetModule,
  NbUserModule,
} from '@nebular/theme';
import { LegendPosition, NgxChartsModule, ScaleType } from '@swimlane/ngx-charts';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-asset-dashboard',
  templateUrl: './asset-dashboard.component.html',
  styleUrls: ['./asset-dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSpinnerModule,
    NbTabsetModule,
    NbUserModule,
    NbListModule,
    NbInputModule,
    NgxChartsModule,
    YSAdvancedPieChartComponent,
    CurrencyPipe,
    NbButtonModule,
  ],
})
export class AssetDashboardComponent implements OnInit, OnDestroy, AfterViewInit, AfterContentChecked {
  private destroy$ = new Subject();

  recentMessages!: RecentMessages[];

  single = [];
  view: any[] = [700, 400];

  // options
  gradient = false;
  showLegend = true;
  showLabels = false;
  isDoughnut = true;
  legendPosition = LegendPosition.Below;

  colorScheme = {
    name: 'customScheme',
    selectable: true,
    group: ScaleType.Ordinal,
    domain: [
      '#38B6FF',
      '#FFA726',
      '#4DB6AC',
      '#4CAF50',
      '#EDC948',
      '#FEF2D5',
      '#856AD6',
      '#F28E2B',
      '#E91E63',
      '#9C27B0',
      '#3E4550',
    ],
  };

  activeLoanByTypeData!: any[];
  geoLocationSummaryData!: any[];
  loanInvestmentsData!: LoanPortfolioBytypePayload[];
  constructionData!: LoanPortfolioBytypePayload[];
  watchlistData!: LoanPortfolioBytypePayload[];

  investorId!: number;
  drawnBalanceSummary: any;

  constructor(
    private currencyPipe: CurrencyPipe,
    private investorsService: InvestorsService,
    private assetService: AssetService,
    private router: Router,
    protected cd: ChangeDetectorRef,
    private sharedService: SharedService,
    private nbSidebarService: NbSidebarService,
  ) { }
  ngAfterContentChecked(): void {
    this.cd.detectChanges();
  }
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.investorsService.account.pipe(takeUntil(this.destroy$)).subscribe((value: any) => {
        this.investorId = this.investorsService.accountValue?.investorId || 0;
        this.loadDashboard();
        this.cd.detectChanges();
      });
    }, 1000);

    this.nbSidebarService.onExpand().subscribe(() => {
      this.cd.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  public yAxisTickFormatting = (value: any) => `${this.currencyPipe.transform(value, 'USD', 'symbol', '0.0')}`;

  async ngOnInit(): Promise<void> {
    this.sharedService.showMessageSubject.subscribe((value) => {
      if (value.updateNotification) {
        this.getRecentLogs();
      }
    });
  }

  private loadDashboard(): void {
    this.getActiveLoanByType();
    this.getGeoLocationSummary();
    this.getRecentLogs();
    this.getDrawnBalanceSummary();
    this.getLoanInvestments();
    this.getConstructions();
    this.getWatchlist();
  }
  private getRecentLogs(): void {
    this.assetService.getRecentMessages().subscribe((data: any) => {
      if (data.success) {
        this.recentMessages = data.payload;
      }
    });
  }

  private getLoanInvestments(): void {
    this.assetService
      .getLoanPortfolioBytype(LoanPortfolioBytype.LoanInvestment)
      .subscribe((data: LoanPortfolioBytypeResponse) => {
        if (data.success) {
          this.loanInvestmentsData = data.payload;
        }
      });
  }

  private getConstructions(): void {
    this.assetService
      .getLoanPortfolioBytype(LoanPortfolioBytype.Construction)
      .subscribe((data: LoanPortfolioBytypeResponse) => {
        if (data.success) {
          this.constructionData = data.payload;
        }
      });
  }

  private getWatchlist(): void {
    this.assetService
      .getLoanPortfolioBytype(LoanPortfolioBytype.Watchlist)
      .subscribe((data: LoanPortfolioBytypeResponse) => {
        if (data.success) {
          this.watchlistData = data.payload;
        }
      });
  }

  private getActiveLoanByType(): void {
    this.assetService.getActiveLoanByType().subscribe((data: any) => {
      if (data.success) {
        this.activeLoanByTypeData = data.payload;
      }
    });
  }

  private getDrawnBalanceSummary(): void {
    this.assetService.getDrawnBalanceSummary().subscribe((data: any) => {
      if (data.success) {
        this.drawnBalanceSummary = data.payload;
      }
    });
  }

  private getGeoLocationSummary(): void {
    this.assetService.getGeoLocationSummary().subscribe((data: any) => {
      if (data.success) {
        this.geoLocationSummaryData = data.payload;
        this.geoLocationSummaryData.forEach((element) => {
          element.name = element.name || 'Unknown';
        });
      }
    });
  }

  // groupArrayOfObjects(list: any, key: string): any[] {
  //   return list.reduce((rv: any, x: any) => {
  //     (rv[x[key]] = rv[x[key]] || []).push({ name: x.entityName, value: x.amount });
  //     return rv;
  //   }, {});
  // }

  onSelect(data: any): void {
    // console.log('Item clicked', JSON.parse(JSON.stringify(data)));
  }

  onActivate(data: any): void {
    // console.log('Activate', JSON.parse(JSON.stringify(data)));
  }

  onDeactivate(data: any): void {
    // console.log('Deactivate', JSON.parse(JSON.stringify(data)));
  }

  createWorkspace(): void { }

  getTotal(itemArray: any[]): number {
    return itemArray
      .map((a) => a.value)
      .reduce((a, b) => {
        return a + b;
      });
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  scrollToInvestor(): void {
    setTimeout(() => {
      const elementList = document.querySelectorAll('.total-value');
      const element = elementList[0] as HTMLElement;
      if (element && !element.innerHTML.includes('$')) {
        element.innerHTML = `$${element.innerHTML.trim()}`;
      }

      const elementList1 = document.querySelectorAll('.item-value');
      const element1 = elementList1[0] as HTMLElement;
      if (element1 && !element1.innerHTML.includes('$')) {
        element1.innerHTML = `$${element1.innerHTML.trim()}`;
      }
      const element2 = elementList1[1] as HTMLElement;
      if (element2 && !element2.innerHTML.includes('$')) {
        element2.innerHTML = `$${element2.innerHTML.trim()}`;
      }
      const element3 = elementList1[2] as HTMLElement;
      if (element3 && !element3.innerHTML.includes('$')) {
        element3.innerHTML = `$${element3.innerHTML.trim()}`;
      }
    }, 2000);
  }

  setShowMessageValue(log: any): void {
    this.updateNotification(log.investorId);
    setTimeout(() => {
      this.sharedService.setShowMessageValue({
        investorId: log.investorId,
        showChat: true,
        updateNotification: true,
      });
    }, 500);
  }

  investmentDashboard(asset: LoanPortfolioBytypePayload): void {
    this.sharedService.setFormParamValue({
      userId: this.sharedService.getUserIdValue.userId,
      assetKeyDataId: asset.assetId,
      changeTab: false,
    });
    if (this.sharedService.isAdmin() || this.sharedService.isOriginatorManager()) {
      this.router.navigate(['/asset/edit']);
    } else {
      this.router.navigate(['/asset/updates']);
    }
  }

  private updateNotification(investorId: number): void {
    this.investorsService.getUpdateNotification({ investorId }).subscribe((response: any) => {
      this.getRecentLogs();
    });
  }

  getView(ref: HTMLElement): [number, number] {
    return [ref.offsetWidth, ref.offsetHeight];
  }

  getMessageText(logs: any): string {
    return `${logs.message} ${logs.facilityName ? '[ ' + logs.facilityName + ' ]' : ''
      }`;
  }
}
