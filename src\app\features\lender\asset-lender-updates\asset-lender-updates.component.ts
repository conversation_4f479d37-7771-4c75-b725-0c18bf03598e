import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators
} from '@angular/forms';
import { Router } from '@angular/router';
import { CkeditorComponent } from '@components/atoms/ckeditor/ckeditor.component';
import { TypeKey, UpdatePublishStatus } from '@core/models/config';
import { SaveLenderDataRequest } from '@core/models/request/save-lender-data.request';
import { AssetService } from '@core/services/asset.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbDatepickerModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxMaskDirective } from 'ngx-mask';
import { CalendarModule } from 'primeng/calendar';
import { AssetLendersComponent } from '../asset-lenders/asset-lenders.component';
@Component({
  selector: 'app-asset-lender-updates',
  templateUrl: './asset-lender-updates.component.html',
  styleUrls: ['./asset-lender-updates.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbSelectModule,
    NbInputModule,
    NbFormFieldModule,
    NbDatepickerModule,
    NbSpinnerModule,
    NbButtonModule,
    CalendarModule,
    NgxMaskDirective,
    AssetLendersComponent,
    CkeditorComponent,
  ],
})
export class AssetLenderUpdatesComponent implements OnInit {
  @Output() changeTab = new EventEmitter<boolean>();

  @Input() getFormParamValue: any;

  lenderUpdatesForm: UntypedFormGroup;
  financialsData: any;
  loading = false;
  invalidVal = false;
  submitted = false;
  returnUrl = '';
  error = '';

  pattern = {
    X: {
      pattern: new RegExp('[#+(0-9)]'),
    },
    0: {
      pattern: new RegExp('[(0-9)]'),
    },
  };
  assetKeyDataId: any;
  updatePublishStatusData: any;
  currentLoanStatusData: any;

  selectedLenderId: number | undefined;
  createNew = false;

  // public editorConfig: any = {
  // toolbar: [
  //   ['Format',
  //     'Bold',
  //     'Italic',
  //     'Underline',
  //     'BulletedList',
  //     'NumberedList',
  //     'Blockquote',
  //     'JustifyLeft',
  //     'JustifyCenter',
  //     'JustifyRight',
  //     'JustifyBlock']
  //   , ['Undo',
  //     'Redo']
  // ],
  // resize_enabled: false,
  // maxHeight: 188,
  // extraPlugins: ['justify', 'editorplaceholder'],
  // editorplaceholder: 'Write something here.....',
  // toolbarLocation: 'bottom',
  // };
  latestLenderId!: number;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    protected cd: ChangeDetectorRef,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private assetService: AssetService,
  ) {
    this.getFormParamValue = {};

    this.lenderUpdatesForm = this.formBuilder.group({
      id: 0,
      updateTitle: ['', Validators.required],
      updatePublishStatusId: [null, Validators.required],
      facilityLimit: [null, Validators.required],
      drawnBalance: [null, Validators.required],
      term: [null, Validators.required],
      financialClose: [null, Validators.required],
      maturityDate: [null, Validators.required],
      repaymentDate: [null, Validators.required],
      lvrLimit: [null, Validators.required],
      ltc: [null, Validators.required],
      lvrActual: [null, Validators.required],
      currentLoanStatusId: [null, Validators.required],
      reportingMonth: [null, Validators.required],
      loanManagementUpdate: [null],
      loanManagementUpdateLabel: [null],
      upcomingReporting: [''],
      upcomingReportingLabel: [''],
    });

    if (this.sharedService.isAdmin() || this.sharedService.isOriginatorManager()) {
      this.lenderUpdatesForm.enable();
    } else {
      this.lenderUpdatesForm.disable();
    }
  }

  ngOnInit(): void {
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId;
    if (this.selectedLenderId) {
      this.getLenderDetails(this.selectedLenderId);
    }
    this.getUpdatePublishStatus();
    this.getCurrentLoanStatus();
  }

  private getLenderDetails(selectedLenderId: number, loadRecent = false): void {
    if (selectedLenderId) {
      this.assetService.getLender(selectedLenderId, this.assetKeyDataId).subscribe((data: any) => {
        if (data.success) {
          if (data.payload) {
            if (data.payload.financialClose) {
              data.payload.financialClose = new Date(data.payload.financialClose);
            }
            if (data.payload.maturityDate) {
              data.payload.maturityDate = new Date(data.payload.maturityDate);
            }
            if (data.payload.repaymentDate) {
              data.payload.repaymentDate = new Date(data.payload.repaymentDate);
            }
            if (data.payload.reportingMonth) {
              data.payload.reportingMonth = new Date(data.payload.reportingMonth);
            } else {
              data.payload.reportingMonth = new Date();
            }
            if (loadRecent) {
              data.payload.id = 0;
            } else {
              this.financialsData = data.payload;
            }
            this.lenderUpdatesForm.patchValue(data.payload);
          }
        } else {
          this.toastr.danger(data.error.message, 'Error!');
          this.loading = false;
        }
      });
    }
  }

  private getUpdatePublishStatus(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_UpdatePublishStatus).subscribe((response: any) => {
      if (response.success) {
        this.updatePublishStatusData = response.payload;
      }
    });
  }

  private getCurrentLoanStatus(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_CurrentLoanStatus).subscribe((response: any) => {
      if (response.success) {
        this.currentLoanStatusData = response.payload;
      }
    });
  }

  get f() {
    return this.lenderUpdatesForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.lenderUpdatesForm.invalid) {
      return;
    }

    this.loading = true;

    this.saveLender();
  }

  dateChange(event: any): void { }

  private saveLender(): void {
    this.invalidVal = false;
    const lenderData: SaveLenderDataRequest = {
      id: this.selectedLenderId || 0,
      assetId: this.assetKeyDataId,
      updateTitle: this.lenderUpdatesForm.value.updateTitle,
      updatePublishStatusId: this.lenderUpdatesForm.getRawValue().updatePublishStatusId,
      facilityLimit: this.lenderUpdatesForm.value.facilityLimit,
      drawnBalance: this.lenderUpdatesForm.value.drawnBalance,
      term: this.lenderUpdatesForm.value.term,
      financialClose: this.lenderUpdatesForm.value.financialClose,
      maturityDate: this.lenderUpdatesForm.value.maturityDate,
      repaymentDate: this.lenderUpdatesForm.value.repaymentDate,
      reportingMonth: this.lenderUpdatesForm.value.reportingMonth,
      lvrLimit: this.lenderUpdatesForm.value.lvrLimit,
      ltc: this.lenderUpdatesForm.value.ltc,
      lvrActual: this.lenderUpdatesForm.value.lvrActual,
      currentLoanStatusId: this.lenderUpdatesForm.value.currentLoanStatusId,
      loanManagementUpdate: this.lenderUpdatesForm.value.loanManagementUpdate,
      loanManagementUpdateLabel: this.lenderUpdatesForm.value.loanManagementUpdateLabel,
      upcomingReporting: '',
      upcomingReportingLabel: '',
    };

    this.getFormValidationErrors();
    if (!this.invalidVal) {
      this.assetService.saveLenderData(lenderData).subscribe(
        (data: any) => {
          setTimeout(() => {
            if (data.success) {
              this.loading = false;
              this.toastr.success('Saved Successfully', 'Success!');
              // if (!this.financialsData) {
              //   // this.changeTab.emit(true);
              // }
              this.submitted = false;
              this.cancelEdit();
            } else {
              this.toastr.danger(data.error.message, 'Error!');
              this.loading = false;
            }
          }, 200);
        },
        (err: any) => {
          this.toastr.danger(err.error.message, 'Error!');
          this.loading = false;
        },
      );
    }
  }

  backtoList(): void {
    this.router.navigate(['/']);
  }

  editLender(lenderId: number): void {
    this.selectedLenderId = lenderId;
    this.updateFormStatus();
    this.getLenderDetails(this.selectedLenderId);
  }

  getLatestLender(latestLenderId: number): void {
    this.latestLenderId = latestLenderId;
  }

  cancelEdit(): void {
    this.lenderUpdatesForm.reset();
    this.createNew = false;
    this.updateFormStatus();
    this.selectedLenderId = undefined;
  }

  createNewEntry(): void {
    this.createNew = true;
    this.getLenderDetails(this.latestLenderId, true);
    this.updateFormStatus();
  }

  private updateFormStatus(): void {
    if (this.createNew) {
      const [selectedStatus] = this.updatePublishStatusData.filter(
        (status: any) => status.id === UpdatePublishStatus.Draft,
      );
      if (selectedStatus) {
        this.lenderUpdatesForm.patchValue({
          updatePublishStatusId: UpdatePublishStatus.Draft,
        });
      }
      this.lenderUpdatesForm.controls.updatePublishStatusId.disable();
    } else {
      this.lenderUpdatesForm.controls.updatePublishStatusId.enable();
    }
  }

  getFormValidationErrors(): any {
    const checkVals = ['ltc', 'lvrLimit', 'lvrActual'];
    Object.keys(this.lenderUpdatesForm.controls).forEach((key) => {
      if (checkVals.includes(key)) {
        const controlErrors = this.lenderUpdatesForm.controls[key].value;
        if (controlErrors === 'NaN') {
          this.toastr.danger(`Validation error: Please enter a valid value for ${key}. `, 'Error!');
          this.loading = false;
          this.invalidVal = true;
        }
      }
    });
  }
}
