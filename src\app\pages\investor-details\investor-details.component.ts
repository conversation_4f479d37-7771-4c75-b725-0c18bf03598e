import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NbTabsetComponent } from '@components/molecules/tabset/tabset.component';
import { InvestorChatComponent } from '@components/templates/investor-chat/investor-chat.component';
import { SharedService } from '@core/services/shared.service';
import { InvestorDocumentsComponent } from '@features/investors-management/investor-document/investor-documents/investor-documents.component';
import { InvestorFinancialsComponent } from '@features/investors-management/investor-financials/investor-financials.component';
import { InvestorInvestmentsComponent } from '@features/investors-management/investor-investments/investor-investments.component';
import { InvestorOverviewComponent } from '@features/investors-management/investor-overview/investor-overview.component';
import {
  NbAccordionModule,
  NbCardModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbTabsetModule,
} from '@nebular/theme';
@Component({
  selector: 'app-investor-details',
  templateUrl: './investor-details.component.html',
  styleUrls: ['./investor-details.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbTabsetModule,
    NbInputModule,
    NbFormFieldModule,
    NbAccordionModule,
    InvestorOverviewComponent,
    InvestorFinancialsComponent,
    InvestorInvestmentsComponent,
    InvestorDocumentsComponent,
    InvestorChatComponent,
  ],
})
export class InvestorDetailsComponent implements OnInit {
  @ViewChild('tabset') tabsetEl!: NbTabsetComponent;

  investorId: any;
  entityName: any;
  tabId = 1;
  userId: any;
  constructor(
    private route: Router,
    private sharedService: SharedService,
    protected cd: ChangeDetectorRef,
  ) { }

  async ngOnInit(): Promise<void> {
    this.investorId = this.sharedService.getFormParamValue.investorId;
    this.userId = this.sharedService.getFormParamValue.userId;

    const changeTab = this.sharedService.getFormParamValue.changeTab;

    if (changeTab) {
      this.sharedService.setFormParamValue({
        investorId: this.investorId,
        userId: this.userId,
        changeTab: false,
      });
      this.changeTab(null);
    }
  }

  userChange(event: any): void {
    this.entityName = event.entityName;
  }

  tabClick(event: any): void {
    this.tabId = Number(event.tabId);
  }

  changeTab(event: any): void {
    setTimeout(() => {
      if (this.tabsetEl.tabs) {
        const activateTab = this.tabsetEl.tabs.find((t: any) => t.tabId === (this.tabId + 1).toString());
        if (activateTab) {
          this.tabsetEl.selectTab(activateTab);
        }
        this.cd.detectChanges();
      }
    }, 1000);
  }
}
