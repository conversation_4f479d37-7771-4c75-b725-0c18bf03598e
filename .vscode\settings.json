{"eslint.validate": ["javascript", "typescript"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "git.ignoreLimitWarning": true, "editor.formatOnSave": true, "editor.fontSize": 13, "typescript.autoClosingTags": true, "editor.formatOnPaste": true, "angular-schematics.schematicsDefaultOptions": {"angular-*": {"skipStyle": true, "externalTemplate": true}, "angular-component": {"skipChangeDetectionOnPush": true}}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}