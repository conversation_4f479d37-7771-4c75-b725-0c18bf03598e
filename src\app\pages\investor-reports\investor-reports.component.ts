import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { forkJoin, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-investor-reports',
  templateUrl: './investor-reports.component.html',
  styleUrls: ['./investor-reports.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    NbButtonModule,
  ],
})
export class InvestorReportsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  reports: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  // Report types
  selectedReportType = 'account-activity';
  reportTypes = [
    { id: 'investment-portfolio', name: 'Investment Portfolio', icon: 'briefcase-outline' },
    { id: 'funding-activity', name: 'Funding Activity', icon: 'trending-up-outline' },
    { id: 'account-activity', name: 'Account Activity', icon: 'activity-outline' },
    { id: 'trust-activity', name: 'Trust Activity', icon: 'shield-outline' },
  ];

  investmentData: any;
  investorId: number | undefined;
  destroyed$ = new Subject<boolean>();
  dateFilterData: any[] = [];

  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private investmentService: InvestmentService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.dateFilterData = this.sharedService.getDateFilterRows();

    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
    } as Filters;

    if (this.isInvestor) {
      this.investorId = this.investorsService.accountValue.investorId;
    }

    forkJoin([this.getInvestmentLookup()])
      .pipe(takeUntil(this.destroyed$))
      .subscribe(
        ([lookups]) => {
          this.investmentData = lookups.payload;
        },
        (e) => {
          console.log(e);
        },
      );
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    if (this.isInvestor) {
      this.filterParams.investorId = this.investorId;
    }

    // Add report type to filter params
    this.filterParams.reportType = this.selectedReportType;

    // For now, we'll use the payments endpoint as a placeholder
    // In a real implementation, you'd have a dedicated reports endpoint
    this.investorsService.getPayments(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.reports = this.transformDataForReportType((data as any).payload.investorPayments);
        this.totalRecords = (data as any).payload.rows;
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  private transformDataForReportType(data: any[]): any[] {
    // Transform data based on selected report type
    switch (this.selectedReportType) {
      case 'investment-portfolio':
        return this.transformToPortfolioData(data);
      case 'funding-activity':
        return this.transformToFundingData(data);
      case 'account-activity':
        return this.transformToAccountData(data);
      case 'trust-activity':
        return this.transformToTrustData(data);
      default:
        return data;
    }
  }

  private transformToPortfolioData(data: any[]): any[] {
    // Transform to portfolio structure
    return data.map((item) => ({
      ...item,
      portfolioValue: item.amount,
      units: Math.floor(item.amount / 100), // Example calculation
      unitPrice: 100, // Example fixed price
    }));
  }

  private transformToFundingData(data: any[]): any[] {
    // Transform to funding structure
    return data.map((item) => ({
      ...item,
      fundingAmount: item.amount,
      fundingType: item.transactionType,
      status: 'Completed',
    }));
  }

  private transformToAccountData(data: any[]): any[] {
    // Transform to account activity structure (similar to the image)
    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate,
      checkoutOrLoan: item.transactionType,
      transactionAmount: item.amount,
      servFees: 0,
      gst: 0,
      interest: 0,
      distributionPrincipal: item.transactionType === 'Distribution' ? item.amount : 0,
      charges: 0,
      other: 0,
      escrow: 0,
    }));
  }

  private transformToTrustData(data: any[]): any[] {
    // Transform to trust structure
    return data.map((item) => ({
      ...item,
      trustActivity: item.transactionType,
      trustAmount: item.amount,
      trustDate: item.paymentDate,
    }));
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  get isManager(): boolean {
    return this.sharedService.isManager();
  }

  get isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.unsubscribe();
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  onReportTypeChange(reportType: string): void {
    this.selectedReportType = reportType;
    this.getList();
  }

  exportReport(): void {
    this.filterParams.export = true;
    this.toast.default(`Downloading started`, '', {
      icon: 'download',
    });
    // Use payments export for now, in real implementation you'd have a reports export
    this.investorsService.getPaymentsExport(this.filterParams);
  }

  getInvestmentLookup(): Observable<any> {
    return this.investmentService.getInvestmentLookup({});
  }

  applyFilters(): void {
    this.getList();
  }
}
