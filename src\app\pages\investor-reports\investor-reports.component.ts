import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDatepickerModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { forkJoin, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-investor-reports',
  templateUrl: './investor-reports.component.html',
  styleUrls: ['./investor-reports.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    NbButtonModule,
    NbDatepickerModule,
  ],
})
export class InvestorReportsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  reports: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  // Report types
  selectedReportType = '';
  reportTypes = [
    { id: 'investment-portfolio', name: 'Investment Portfolio', icon: 'briefcase-outline' },
    { id: 'funding-activity', name: 'Funding Activity', icon: 'trending-up-outline' },
    { id: 'account-activity', name: 'Account Activity', icon: 'activity-outline' },
    { id: 'trust-activity', name: 'Trust Activity', icon: 'shield-outline' },
  ];

  investmentData: any;
  investorId: number | undefined;
  destroyed$ = new Subject<boolean>();
  dateFilterData: any[] = [];

  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private investmentService: InvestmentService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.dateFilterData = this.sharedService.getDateFilterRows();

    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
    } as Filters;

    if (this.isInvestor) {
      this.investorId = this.investorsService.accountValue.investorId;
    }

    forkJoin([this.getInvestmentLookup()])
      .pipe(takeUntil(this.destroyed$))
      .subscribe(
        ([lookups]) => {
          this.investmentData = lookups.payload;
        },
        (e) => {
          console.log(e);
        },
      );
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    if (this.isInvestor) {
      this.filterParams.investorId = this.investorId;
    }

    // Add report type to filter params
    this.filterParams.reportType = this.selectedReportType;

    // For now, we'll use the payments endpoint as a placeholder
    // In a real implementation, you'd have a dedicated reports endpoint
    this.investorsService.getPayments(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.reports = this.transformDataForReportType((data as any).payload.investorPayments);
        this.totalRecords = (data as any).payload.rows;
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  private transformDataForReportType(data: any[]): any[] {
    // Transform data based on selected report type
    switch (this.selectedReportType) {
      case 'investment-portfolio':
        return this.transformToPortfolioData(data);
      case 'funding-activity':
        return this.transformToFundingData(data);
      case 'account-activity':
        return this.transformToAccountData(data);
      case 'trust-activity':
        return this.transformToTrustData(data);
      default:
        return data;
    }
  }

  private transformToPortfolioData(data: any[]): any[] {
    // Transform to portfolio structure
    return data.map((item) => ({
      ...item,
      portfolioValue: item.amount,
      units: Math.floor(item.amount / 100), // Example calculation
      unitPrice: 100, // Example fixed price
    }));
  }

  private transformToFundingData(data: any[]): any[] {
    // Transform to funding structure
    return data.map((item) => ({
      ...item,
      fundingAmount: item.amount,
      fundingType: item.transactionType,
      status: 'Completed',
    }));
  }

  private transformToAccountData(data: any[]): any[] {
    // Transform to account activity structure matching the image data
    const mockData = [
      {
        id: 1,
        transactionDate: new Date('2024-05-02'),
        checkoutOrLoan: '0062094',
        transactionAmount: 713.33,
        servFees: 0,
        gst: 0,
        interest: 713.33,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        loanAccount: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        transactionDate: new Date('2024-05-02'),
        checkoutOrLoan: '0062094',
        transactionAmount: 713.33,
        servFees: 0,
        gst: 0,
        interest: 713.33,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        loanAccount: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 3,
        transactionDate: new Date('2024-05-02'),
        checkoutOrLoan: '0062094',
        transactionAmount: 5000.0,
        servFees: 0,
        gst: 0,
        interest: 0,
        distributionPrincipal: 5000.0,
        charges: 0,
        other: 0,
        escrow: 0,
        loanAccount: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 4,
        transactionDate: new Date('2024-05-02'),
        checkoutOrLoan: '0062094',
        transactionAmount: -1.47,
        servFees: 0,
        gst: 0,
        interest: -1.47,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        loanAccount: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 5,
        transactionDate: new Date('2024-06-28'),
        checkoutOrLoan: '0063349',
        transactionAmount: 668.75,
        servFees: 0,
        gst: 0,
        interest: 668.75,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        loanAccount: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 6,
        transactionDate: new Date('2024-06-28'),
        checkoutOrLoan: '0063349',
        transactionAmount: 11000.0,
        servFees: 0,
        gst: 0,
        interest: 0,
        distributionPrincipal: 11000.0,
        charges: 0,
        other: 0,
        escrow: 0,
        loanAccount: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockData;
    }

    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate || new Date(),
      checkoutOrLoan: item.transactionType || 'N/A',
      transactionAmount: item.amount || 0,
      servFees: 0,
      gst: 0,
      interest: item.transactionType === 'Interest' ? item.amount : 0,
      distributionPrincipal: item.transactionType === 'Distribution' ? item.amount : 0,
      charges: 0,
      other: 0,
      escrow: 0,
      loanAccount: item.loanAccount || 'N/A', // Add loan account if available, otherwise default to 'N/A'
    }));
  }

  private transformToTrustData(data: any[]): any[] {
    // Transform to trust structure
    return data.map((item) => ({
      ...item,
      trustActivity: item.transactionType,
      trustAmount: item.amount,
      trustDate: item.paymentDate,
    }));
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  get isManager(): boolean {
    return this.sharedService.isManager();
  }

  get isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.unsubscribe();
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  onReportTypeChange(reportType: string): void {
    this.selectedReportType = reportType;
    this.getList();
  }

  exportReport(): void {
    this.filterParams.export = true;
    this.toast.default(`Downloading started`, '', {
      icon: 'download',
    });
    // Use payments export for now, in real implementation you'd have a reports export
    this.investorsService.getPaymentsExport(this.filterParams);
  }

  getInvestmentLookup(): Observable<any> {
    return this.investmentService.getInvestmentLookup({});
  }

  applyFilters(): void {
    this.getList();
  }

  deleteReport(reportId: number): void {
    // Placeholder for delete functionality
    // In a real implementation, you would have a delete dialog similar to payments
    console.log('Delete report:', reportId);
  }
}
