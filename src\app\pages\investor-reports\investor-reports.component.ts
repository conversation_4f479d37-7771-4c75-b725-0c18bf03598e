import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDatepickerModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-investor-reports',
  templateUrl: './investor-reports.component.html',
  styleUrls: ['./investor-reports.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    NbButtonModule,
    NbDatepickerModule,
  ],
})
export class InvestorReportsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  reports: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;
  firstLoad = true;

  // Report types
  selectedReportType = 'investment-portfolio';
  reportTypes = [
    { id: 'investment-portfolio', name: 'Investment Portfolio', icon: 'briefcase-outline' },
    { id: 'funding-activity', name: 'Funding Activity', icon: 'trending-up-outline' },
    { id: 'account-activity', name: 'Account Activity', icon: 'activity-outline' },
    { id: 'trust-activity', name: 'Trust Activity', icon: 'shield-outline' },
  ];

  investorId: number | undefined;
  destroyed$ = new Subject<boolean>();

  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
    } as Filters;

    if (this.isInvestor) {
      this.investorId = this.investorsService.accountValue.investorId;
    }
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    this.loading = true;
    if (this.isInvestor) {
      this.filterParams.investorId = this.investorId;
    }

    // Add report type to filter params
    this.filterParams.reportType = this.selectedReportType;

    // For now, we'll use the payments endpoint as a placeholder
    // In a real implementation, you'd have a dedicated reports endpoint
    // this.investorsService.getPayments(this.filterParams).subscribe((data: any) => {
    //   if (data.success) {
    //     this.reports = this.transformDataForReportType((data as any).payload.investorPayments);
    //     this.totalRecords = (data as any).payload.rows;
    //     this.spinner.hide();
    //     this.loading = false;
    //   }
    // });

    this.reports = this.transformDataForReportType([]);
    this.totalRecords = this.reports.length;
    this.spinner.hide();
    this.loading = false;
  }

  private transformDataForReportType(data: any[]): any[] {
    // Transform data based on selected report type
    switch (this.selectedReportType) {
      case 'investment-portfolio':
        return this.transformToPortfolioData(data);
      case 'funding-activity':
        return this.transformToFundingData(data);
      case 'account-activity':
        return this.transformToAccountData(data);
      case 'trust-activity':
        return this.transformToTrustData(data);
      default:
        return data;
    }
  }

  private transformToPortfolioData(data: any[]): any[] {
    // Transform to portfolio structure matching the image data
    const mockPortfolioData = [
      {
        id: 1,
        loanAccount: 'MA00100702',
        borrowerName: 'Qartaba Homes Pty Ltd',
        pctOwned: 1.722,
        interestRate: 10.7,
        maturityDate: new Date('2023-11-03'),
        termLeft: -7,
        nextPayment: new Date('2024-01-08'),
        regularPayment: 579.19,
        loanBalance: 64000.0,
        currentPortfolioYield: 10.7,
        description: 'Investment Portfolio Entry',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        loanAccount: 'MA00100703',
        borrowerName: 'Another Borrower',
        pctOwned: 2.345,
        interestRate: 11.2,
        maturityDate: new Date('2024-12-31'),
        termLeft: 365,
        nextPayment: new Date('2025-01-15'),
        regularPayment: 600.0,
        loanBalance: 58000.0,
        currentPortfolioYield: 11.2,
        description: 'Investment Portfolio Entry 2',
        investment: 'Sample Investment 2',
        investor: 'Sample Investor 2',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockPortfolioData;
    }

    return data.map((item) => ({
      ...item,
      loanAccount: item.description || 'N/A',
      borrowerName: item.investor || 'N/A',
      pctOwned: Math.random() * 5, // Example calculation
      interestRate: 10.7,
      maturityDate: new Date(),
      termLeft: Math.floor(Math.random() * 365) - 180,
      nextPayment: new Date(),
      regularPayment: item.amount * 0.1 || 0,
      loanBalance: item.amount || 0,
      currentPortfolioYield: 10.7,
    }));
  }

  private transformToFundingData(data: any[]): any[] {
    // Transform to funding structure matching the image data
    const mockFundingData = [
      {
        id: 1,
        transactionDate: new Date('2024-04-30'),
        reference: 'SALE 12 MULTAN',
        loanAccount: 'MA00100702',
        borrowerName: 'Qartaba Homes Pty Ltd',
        amountFunded: -5000.0,
        description: 'Funding Activity Entry',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        transactionDate: new Date('2024-06-25'),
        reference: 'Sale 21 Persea',
        loanAccount: 'MA00100702',
        borrowerName: 'Qartaba Homes Pty Ltd',
        amountFunded: -11000.0,
        description: 'Funding Activity Entry 2',
        investment: 'Sample Investment 2',
        investor: 'Sample Investor 2',
      },
      {
        id: 3,
        transactionDate: new Date('2024-06-28'),
        reference: 'Discharges',
        loanAccount: 'MA00100702',
        borrowerName: 'Qartaba Homes Pty Ltd',
        amountFunded: -6000.0,
        description: 'Funding Activity Entry 3',
        investment: 'Sample Investment 3',
        investor: 'Sample Investor 3',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockFundingData;
    }

    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate || new Date(),
      reference: item.transactionType || 'N/A',
      loanAccount: item.description || 'N/A',
      borrowerName: item.investor || 'N/A',
      amountFunded: item.amount || 0,
    }));
  }

  private transformToAccountData(data: any[]): any[] {
    // Transform to account activity structure matching the image data
    const mockData = [
      {
        id: 1,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: 713.33,
        servFees: 0,
        gst: 0,
        interest: 713.33,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: 713.33,
        servFees: 0,
        gst: 0,
        interest: 713.33,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 3,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: 5000.0,
        servFees: 0,
        gst: 0,
        interest: 0,
        distributionPrincipal: 5000.0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 4,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: -1.47,
        servFees: 0,
        gst: 0,
        interest: -1.47,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 5,
        transactionDate: new Date('2024-06-28'),
        reference: '0063349',
        loanAccount: 'MA00100702',
        transactionAmount: 668.75,
        servFees: 0,
        gst: 0,
        interest: 668.75,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 6,
        transactionDate: new Date('2024-06-28'),
        reference: '0063349',
        loanAccount: 'MA00100702',
        transactionAmount: 11000.0,
        servFees: 0,
        gst: 0,
        interest: 0,
        distributionPrincipal: 11000.0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockData;
    }

    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate || new Date(),
      checkoutOrLoan: item.transactionType || 'N/A',
      loanAccount: item.loanAccount || 'N/A',
      transactionAmount: item.amount || 0,
      servFees: 0,
      gst: 0,
      interest: item.transactionType === 'Interest' ? item.amount : 0,
      distributionPrincipal: item.transactionType === 'Distribution' ? item.amount : 0,
      charges: 0,
      other: 0,
      escrow: 0,
    }));
  }

  private transformToTrustData(data: any[]): any[] {
    // Transform to trust structure matching the image data
    const mockTrustData = [
      {
        id: 1,
        transactionDate: new Date('2024-04-30'),
        reference: 'APRIL',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 1.47,
        amountReceived: 0.0,
        dailyBalance: -1.47,
        description: 'Trust Activity Entry',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        transactionDate: new Date('2024-04-30'),
        reference: 'APRIL',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 711.86,
        description: 'Trust Activity Entry 2',
        investment: 'Sample Investment 2',
        investor: 'Sample Investor 2',
      },
      {
        id: 3,
        transactionDate: new Date('2024-04-30'),
        reference: 'DECEMBER',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 1425.19,
        description: 'Trust Activity Entry 3',
        investment: 'Sample Investment 3',
        investor: 'Sample Investor 3',
      },
      {
        id: 4,
        transactionDate: new Date('2024-04-30'),
        reference: 'FEBRUARY',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 2138.52,
        description: 'Trust Activity Entry 4',
        investment: 'Sample Investment 4',
        investor: 'Sample Investor 4',
      },
      {
        id: 5,
        transactionDate: new Date('2024-04-30'),
        reference: 'JANUARY',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 2851.85,
        description: 'Trust Activity Entry 5',
        investment: 'Sample Investment 5',
        investor: 'Sample Investor 5',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockTrustData;
    }

    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate || new Date(),
      checkOrReference: item.transactionType || 'N/A',
      fromWhomReceivedOrToWhomPaid: item.investor || 'N/A',
      descriptionMemo: item.description || 'N/A',
      balanceForward: item.balanceForward || 0,
      amountPaidOut: item.amountPaidOut || 0,
      amountReceived: item.amountReceived || 0,
      dailyBalance: item.dailyBalance || 0,
    }));
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  get isManager(): boolean {
    return this.sharedService.isManager();
  }

  get isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.unsubscribe();
  }

  filterGlobal(event: any): void {
    // this.dt.filterGlobal(event.target.value, 'contains');
    window.alert('Coming soon...');
  }

  onReportTypeChange(reportType: string): void {
    this.selectedReportType = reportType;
    this.getList();
  }

  exportReport(): void {
    this.filterParams.export = true;
    this.toast.default(`Coming soon...`, '', {
      icon: 'download',
    });
    // Use payments export for now, in real implementation you'd have a reports export
    // this.investorsService.getPaymentsExport(this.filterParams);
  }

  applyFilters(): void {
    this.getList();
  }

  deleteReport(reportId: number): void {
    // Placeholder for delete functionality
    // In a real implementation, you would have a delete dialog similar to payments
    console.log('Delete report:', reportId);
  }

  getTotalInterestRate(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    // Calculate weighted average interest rate based on loan balance
    let totalWeightedRate = 0;
    let totalBalance = 0;

    this.reports.forEach((report) => {
      const balance = report.loanBalance || 0;
      const rate = report.interestRate || 0;
      totalWeightedRate += balance * rate;
      totalBalance += balance;
    });

    return totalBalance > 0 ? totalWeightedRate / totalBalance : 0;
  }

  getTotalRegularPayment(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.regularPayment || 0);
    }, 0);
  }

  getTotalLoanBalance(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.loanBalance || 0);
    }, 0);
  }

  getTotalAmountFunded(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.amountFunded || 0);
    }, 0);
  }

  // Account Activity Total Methods
  getTotalTransactionAmount(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.transactionAmount || 0);
    }, 0);
  }

  getTotalServFees(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.servFees || 0);
    }, 0);
  }

  getTotalGst(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.gst || 0);
    }, 0);
  }

  getTotalInterest(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.interest || 0);
    }, 0);
  }

  getTotalDistributionPrincipal(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.distributionPrincipal || 0);
    }, 0);
  }

  getTotalCharges(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.charges || 0);
    }, 0);
  }

  getTotalOther(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.other || 0);
    }, 0);
  }

  getTotalEscrow(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.escrow || 0);
    }, 0);
  }

  onDateRangeChange(event: any): void {
    console.log('event', event);
    this.filterParams.startDate = event.start;
    this.filterParams.endDate = event.end;
    if (event.end) {
      window.alert('Coming soon...');
    }
    console.log('Filter params after date range change:', this.filterParams);
    // this.getList();
  }
}
