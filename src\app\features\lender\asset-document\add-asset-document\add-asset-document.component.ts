import { HttpEventType } from '@angular/common/http';
import { AfterViewChecked, ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AssetLendersResponse } from '@core/models/response/asset-lenders.response';
import { AssetService } from '@core/services/asset.service';
import {
  NbDialogRef,
  NbToastrService,
  NbCardModule,
  NbIconModule,
  NbSelectModule,
  NbProgressBarModule,
  NbSpinnerModule,
  NbButtonModule,
} from '@nebular/theme';
import { map } from 'rxjs/operators';
import { TypeKey } from '@core/models/config';
import { DocumentService } from '@core/services/document.service';
import { SharedService } from '@core/services/shared.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-add-asset-document',
  templateUrl: './add-asset-document.component.html',
  styleUrls: ['./add-asset-document.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbProgressBarModule,
    NbSpinnerModule,
    NbButtonModule,
  ],
})
export class AddAssetDocumentComponent implements OnInit, AfterViewChecked {
  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;

  form: UntypedFormGroup;
  loading = false;
  submitted = false;
  uploadedDocuments: any[] = [];
  progress: any;
  docTypes: any;
  docTypesSub: any;
  assetKeyDataId: number | undefined;
  lenders: any;
  subDocumentInvalid = false;
  constructor(
    protected dialogRef: NbDialogRef<any>,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    public toastr: NbToastrService,
    private cdr: ChangeDetectorRef,
    private sharedService: SharedService,
    private documentService: DocumentService,
    private toast: NbToastrService,
    private assetService: AssetService,
  ) {
    this.form = this.formBuilder.group({
      documentType: [null, Validators.required],
      documentTypeSub: [null],
      lenderId: [null],
    });
  }

  ngOnInit(): void {
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId;

    if (this.assetKeyDataId) {
      this.getDocumentType();
      this.getLenderList();
    }
  }

  private getDocumentType(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_DocumentType).subscribe((response: any) => {
      if (response.success) {
        this.docTypes = response.payload;
      }
    });
    this.assetService.getTypeBy(TypeKey.AssetManagement_DocumentCategory).subscribe((response: any) => {
      if (response.success) {
        this.docTypesSub = response.payload.filter((el: any) => el.id !== 62);
      }
    });
  }

  private getLenderList(): void {
    if (this.assetKeyDataId) {
      this.assetService
        .getLenders({
          assetId: this.assetKeyDataId,
          includeDraft: true,
        })
        .subscribe((data: AssetLendersResponse) => {
          if (data.success) {
            this.lenders = (data as any).payload;
            this.loading = false;
          }
        });
    }
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  close(): void {
    this.dialogRef.close(false);
  }

  clearDocuments(file: any): void {
    const newUploadedDocuments = [...this.uploadedDocuments].filter((el) => el.name !== file.name);
    this.uploadedDocuments = newUploadedDocuments;
    if (this.uploadedDocuments.length == 0) {
      this.fileDropRef.nativeElement.value = '';
    }
    this.loading = false;
    this.cdr.detectChanges();
  }

  get f() {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;
    this.subDocumentInvalid = false;

    // stop here if form is invalid
    if (this.form.invalid || !this.uploadedDocuments) {
      return;
    }
    if (this.form.value.documentType == 54) {
      if (this.form.value.documentTypeSub == '' || !this.form.value.documentTypeSub) {
        this.subDocumentInvalid = true;
        return;
      }
    } else {
      this.form.controls['lenderId'].setValue(null);
    }

    this.loading = true;

    this.prepareFilesList(this.uploadedDocuments);
    this.cdr.detectChanges();
  }

  onFileDropped(event: any): void {
    this.uploadedDocuments.push(...event);
  }

  fileBrowseHandler(event: any): void {
    this.uploadedDocuments.push(...event.target.files);
  }

  prepareFilesList(files: any[]): void {
    const allowedExtensions = /(\.jpg|\.jpeg|\.png|\.pdf|\.xls|\.xlsx|\.doc|\.docx|\.odt)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toast.danger('File type not supported', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = [];
          this.loading = false;
          this.submitted = false;
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 20) {
          this.toast.danger('Please ensure the file size does not exceed 20MB.', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = [];
          this.submitted = false;
          this.loading = false;
          return;
        }
      }
    }

    const formData = new FormData();

    if (this.assetKeyDataId) {
      formData.append('assetId', this.assetKeyDataId as any);
    }

    if (this.form.value.documentType) {
      formData.append('DocumentType', this.form.value.documentType as any);
    }
    if (this.form.value.documentTypeSub) {
      formData.append('DocumentCategory', this.form.value.documentTypeSub as any);
    }

    if (this.form.value.lenderId) {
      formData.append('LenderId', this.form.value.lenderId as any);
    }

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.documentService
      .uploadAssetDocument(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          if (res.success) {
            this.toast.success('File Uploaded Successfully!', 'Success!');
            this.dialogRef.close(true);
            this.uploadedDocuments = [];
          }
        },
        (err: any) => this.toast.success(err.error.message, 'Failed!'),
      );
  }
}
