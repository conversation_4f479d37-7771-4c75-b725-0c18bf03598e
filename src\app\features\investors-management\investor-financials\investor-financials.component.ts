import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  AbstractControl,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Financial, InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbFormFieldModule,
  NbInputModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
@Component({
  selector: 'app-investor-financials',
  templateUrl: './investor-financials.component.html',
  styleUrls: ['./investor-financials.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbAlertModule,
    NbInputModule,
    NbSpinnerModule,
    NbButtonModule,
    NbFormFieldModule,
  ],
})
export class InvestorFinancialsComponent implements OnInit {
  @Output() changeTab = new EventEmitter<boolean>();

  @Input() getFormParamValue: any;

  financialsForm: UntypedFormGroup;
  financialsData: any;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';

  pattern = {
    X: {
      pattern: new RegExp('[#+(0-9)]'),
    },
    0: {
      pattern: new RegExp('[(0-9)]'),
    },
  };
  investorId: any;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    protected cd: ChangeDetectorRef,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
  ) {
    this.getFormParamValue = {};

    this.financialsForm = this.formBuilder.group({
      id: 0,
      bankAccountName: ['', Validators.required],
      accountNo: ['', Validators.required],
      bank: ['', [Validators.required]],
      bsb: ['', [Validators.required]],
      taxFileNo: ['', [Validators.required]],
      taxFileNoExcemptionCode: ['', [Validators.required]],
    });

    if (this.sharedService.isAdmin()) {
      this.financialsForm.enable();
    } else {
      this.financialsForm.disable();
    }
  }

  ngOnInit(): void {
    this.investorId = this.sharedService.getFormParamValue.investorId;

    if (this.investorId) {
      this.investorsService.getFinancial(this.investorId).subscribe((data: any) => {
        if (data.success) {
          if (data.payload) {
            this.financialsData = data.payload;
            this.financialsForm.patchValue(data.payload);
          }
        } else {
          this.toastr.danger(data.error.message, 'Error!');
          this.loading = false;
        }
      });
    }

    this.financialsForm.controls.taxFileNoExcemptionCode.valueChanges.subscribe((updatedValue) => {
      this.updateControls();
    });
    this.financialsForm.controls.taxFileNo.valueChanges.subscribe((updatedValue) => {
      this.updateControls();
    });
  }

  private updateControls(): void {
    setTimeout(() => {
      if (this.financialsForm.value.taxFileNo && !this.financialsForm.value.taxFileNoExcemptionCode) {
        this.financialsForm.controls.taxFileNoExcemptionCode.clearValidators();
        this.financialsForm.controls.taxFileNoExcemptionCode.setErrors(null);
      } else if (this.financialsForm.value.taxFileNoExcemptionCode && !this.financialsForm.value.taxFileNo) {
        this.financialsForm.controls.taxFileNo.clearValidators();
        this.financialsForm.controls.taxFileNo.setErrors(null);
      } else if (!this.financialsForm.value.taxFileNoExcemptionCode && !this.financialsForm.value.taxFileNo) {
        this.financialsForm.controls.taxFileNoExcemptionCode.setValidators([Validators.required]);
        this.financialsForm.controls.taxFileNo.setValidators([Validators.required]);
        this.financialsForm.controls.taxFileNoExcemptionCode.setErrors({
          required: true,
        });
        this.financialsForm.controls.taxFileNo.setErrors({ required: true });
      }
    }, 500);
    this.cd.detectChanges();
  }

  validator(control: string): boolean {
    if (this.financialsForm && control) {
      const cont = this.financialsForm.get(control);
      if (!cont || !cont.validator) {
        return false;
      }
      const validator = cont.validator({} as AbstractControl);
      return validator && validator.required;
    } else {
      return false;
    }
  }

  get f() {
    return this.financialsForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.financialsForm.invalid) {
      return;
    }

    this.loading = true;

    this.updateFinancials();
  }

  private updateFinancials(): void {
    const financialsData: Financial = {
      id: this.financialsForm.value.id,
      investorId: this.investorId,
      bankAccountName: this.financialsForm.value.bankAccountName,
      accountNo: this.financialsForm.value.accountNo,
      bank: this.financialsForm.value.bank,
      host: window.location.host,
      bsb: this.financialsForm.value.bsb,
      taxFileNo: this.financialsForm.value.taxFileNo,
      taxFileNoExcemptionCode: this.financialsForm.value.taxFileNoExcemptionCode,
    };

    this.investorsService.saveFinancial(financialsData).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            this.loading = false;
            this.toastr.success('Saved Successfully', 'Success!');
            if (!this.financialsData) {
              this.changeTab.emit(true);
            }
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  backtoList(): void {
    this.router.navigate(['/users']);
  }
}
