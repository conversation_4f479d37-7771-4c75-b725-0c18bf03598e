// Sydney Wyde Brand Guide - Optimized for Tailwind CSS v4
// =======================================================
// This file focuses on brand-specific utilities that complement Tailwind
// Removes duplicated utilities that Tailwind already provides

@use "./variables" as *;
@use "./mixins" as *;

// CSS Custom Properties (for runtime flexibility and Tailwind integration)
// ========================================================================

:root {
    // Primary Brand Colors
    --sw-velvet-green: #{$sw-velvet-green};
    --sw-velvet-green-rgb: #{$sw-velvet-green-rgb};

    --sw-velvet-green-light: #{$sw-velvet-green-light};
    --sw-velvet-green-light-rgb: #{$sw-velvet-green-light-rgb};

    --sw-closing-bell: #{$sw-closing-bell};
    --sw-closing-bell-rgb: #{$sw-closing-bell-rgb};

    // Neutral Colors
    --sw-mist: #{$sw-mist};
    --sw-mist-rgb: #{$sw-mist-rgb};

    --sw-white: #{$sw-white};
    --sw-white-rgb: #{$sw-white-rgb};

    // Text Colors
    --sw-grey: #{$sw-grey};
    --sw-grey-rgb: #{$sw-grey-rgb};

    --sw-light-grey: #{$sw-light-grey};
    --sw-light-grey-rgb: #{$sw-light-grey-rgb};

    // Accent Color
    --sw-lime: #{$sw-lime};
    --sw-lime-rgb: #{$sw-lime-rgb};

    // Semantic Color Assignments
    --sw-primary: var(--sw-velvet-green);
    --sw-primary-light: var(--sw-velvet-green-light);
    --sw-primary-dark: var(--sw-closing-bell);

    --sw-background: var(--sw-white);
    --sw-background-alt: var(--sw-mist);

    --sw-text-primary: var(--sw-velvet-green);
    --sw-text-secondary: var(--sw-grey);
    --sw-text-tertiary: var(--sw-light-grey);
    --sw-text-inverse: var(--sw-white);

    --sw-accent: var(--sw-lime);

    // Typography Variables
    --sw-font-feature: #{$sw-font-feature};
    --sw-font-heading: #{$sw-font-heading};
    --sw-font-body: #{$sw-font-body};

    --sw-font-weight-light: #{$sw-font-weight-light};
    --sw-font-weight-regular: #{$sw-font-weight-regular};
    --sw-font-weight-medium: #{$sw-font-weight-medium};

    // Opacity Variables for Layering
    --sw-primary-10: #{sw-color-opacity($sw-velvet-green, 0.1)};
    --sw-primary-20: #{sw-color-opacity($sw-velvet-green, 0.2)};
    --sw-primary-30: #{sw-color-opacity($sw-velvet-green, 0.3)};
    --sw-primary-50: #{sw-color-opacity($sw-velvet-green, 0.5)};
    --sw-primary-70: #{sw-color-opacity($sw-velvet-green, 0.7)};
    --sw-primary-90: #{sw-color-opacity($sw-velvet-green, 0.9)};

    --sw-accent-10: #{sw-color-opacity($sw-lime, 0.1)};
    --sw-accent-20: #{sw-color-opacity($sw-lime, 0.2)};
    --sw-accent-30: #{sw-color-opacity($sw-lime, 0.3)};

    // Shadow Variables
    --sw-shadow-light: 0 2px 4px #{sw-color-opacity($sw-velvet-green, 0.1)};
    --sw-shadow-medium: 0 4px 8px #{sw-color-opacity($sw-velvet-green, 0.15)};
    --sw-shadow-heavy: 0 8px 16px #{sw-color-opacity($sw-velvet-green, 0.2)};

    // Border Variables
    --sw-border-light: 1px solid var(--sw-light-grey);
    --sw-border-medium: 1px solid var(--sw-grey);
    --sw-border-primary: 1px solid var(--sw-primary);
    --sw-border-accent: 1px solid var(--sw-accent);

    // Spacing Variables
    --sw-space-xs: #{$sw-space-xs};
    --sw-space-sm: #{$sw-space-sm};
    --sw-space-md: #{$sw-space-md};
    --sw-space-lg: #{$sw-space-lg};
    --sw-space-xl: #{$sw-space-xl};
    --sw-space-2xl: #{$sw-space-2xl};
    --sw-space-3xl: #{$sw-space-3xl};

    // Border Radius Variables
    --sw-radius-sm: #{$sw-radius-sm};
    --sw-radius-md: #{$sw-radius-md};
    --sw-radius-lg: #{$sw-radius-lg};
    --sw-radius-xl: #{$sw-radius-xl};

    // Transition Variables
    --sw-transition-fast: #{$sw-transition-fast};
    --sw-transition-normal: #{$sw-transition-normal};
    --sw-transition-slow: #{$sw-transition-slow};

    // Z-index Variables
    --sw-z-dropdown: #{$sw-z-dropdown};
    --sw-z-sticky: #{$sw-z-sticky};
    --sw-z-fixed: #{$sw-z-fixed};
    --sw-z-modal-backdrop: #{$sw-z-modal-backdrop};
    --sw-z-modal: #{$sw-z-modal};
    --sw-z-popover: #{$sw-z-popover};
    --sw-z-tooltip: #{$sw-z-tooltip};
}

// Component Classes using SCSS mixins
// ====================================

// Logo and Branding
.sw-logo-primary {
    color: $sw-primary;
    background-color: $sw-mist;
    padding: $sw-space-md;
    border-radius: $sw-radius-md;
    transition: all $sw-transition-normal;
}

.sw-logo-secondary {
    color: $sw-mist;
    background-color: $sw-primary;
    padding: $sw-space-md;
    border-radius: $sw-radius-md;
    transition: all $sw-transition-normal;
}

.sw-logo-contrast {
    color: $sw-closing-bell;
    background-color: $sw-white;
    padding: $sw-space-md;
    border-radius: $sw-radius-md;
    box-shadow: 0 2px 4px sw-color-opacity($sw-velvet-green, 0.1);
    transition: all $sw-transition-normal;
}

// Buttons using mixins
.sw-btn {
    @include sw-button-style(transparent, $sw-text-primary);
}

.sw-btn-primary {
    @include sw-button-style($sw-primary, $sw-text-inverse, $sw-primary);

    &:hover,
    &:focus {
        background-color: $sw-primary-dark;
        border-color: $sw-primary-dark;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px sw-color-opacity($sw-velvet-green, 0.15);
    }
}

.sw-btn-secondary {
    @include sw-button-style(transparent, $sw-primary, $sw-primary);

    &:hover,
    &:focus {
        background-color: sw-color-opacity($sw-primary, 0.1);
        border-color: $sw-primary-dark;
        color: $sw-primary-dark;
    }
}

.sw-btn-accent {
    @include sw-button-style($sw-accent, $sw-primary, $sw-accent);

    &:hover,
    &:focus {
        filter: brightness(0.9);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px sw-color-opacity($sw-lime, 0.15);
    }
}

.sw-btn-ghost {
    @include sw-button-style(transparent, $sw-text-secondary, transparent);

    &:hover,
    &:focus {
        background-color: sw-color-opacity($sw-primary, 0.1);
        color: $sw-primary;
    }
}

// Button sizes
.sw-btn-sm {
    padding: $sw-space-xs $sw-space-sm;
    font-size: 0.75rem;
    min-height: 2rem;
}

.sw-btn-lg {
    padding: $sw-space-md $sw-space-lg;
    font-size: 1rem;
    min-height: 3rem;
}

// Button states
.sw-btn:disabled,
.sw-btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

// Typography Classes
.sw-heading-feature {
    @include sw-text-style($sw-font-feature, $sw-font-weight-regular, $sw-text-primary, 1.2);
    margin-bottom: $sw-space-md;
}

.sw-heading {
    @include sw-text-style($sw-font-heading, $sw-font-weight-medium, $sw-text-primary, 1.25);
    margin-bottom: $sw-space-sm;
}

.sw-heading-semibold {
    @include sw-text-style($sw-font-heading, $sw-font-weight-semibold, $sw-text-primary, 1.25);
    margin-bottom: $sw-space-sm;
}

.sw-font-feature {
    font-family: $sw-font-feature;
}

.sw-font-heading {
    font-family: $sw-font-heading;
}

.sw-font-body {
    font-family: $sw-font-body;
}

.sw-text-body {
    @include sw-text-style($sw-font-body, $sw-font-weight-regular, $sw-text-secondary, 1.6);
    margin-bottom: $sw-space-sm;
}

.sw-text-light {
    @include sw-text-style($sw-font-body, $sw-font-weight-light, $sw-text-secondary, 1.6);
}

.sw-text-small {
    font-size: 0.875rem;
    line-height: 1.5;
}

.sw-text-large {
    font-size: 1.125rem;
    line-height: 1.5;
}

// Background Classes
@each $name, $color in $sw-colors {
    .sw-bg-#{$name} {
        background-color: $color;

        @if $name == "primary" or $name == "primary-dark" {
            color: $sw-text-inverse;
        } @else if $name == "accent" {
            color: $sw-primary;
        } @else {
            color: $sw-text-primary;
        }
    }
}

// Background with opacity classes
.sw-bg-primary-10 {
    background-color: sw-color-opacity($sw-primary, 0.1);
}
.sw-bg-primary-20 {
    background-color: sw-color-opacity($sw-primary, 0.2);
}
.sw-bg-primary-30 {
    background-color: sw-color-opacity($sw-primary, 0.3);
}
.sw-bg-accent-10 {
    background-color: sw-color-opacity($sw-accent, 0.1);
}
.sw-bg-accent-20 {
    background-color: sw-color-opacity($sw-accent, 0.2);
}

// Text Color Classes
@each $name, $color in $sw-text-colors {
    .sw-text-#{$name} {
        color: $color !important;
    }
}

// Card Components using mixins
.sw-card {
    @include sw-card();

    &:hover {
        box-shadow: 0 4px 8px sw-color-opacity($sw-velvet-green, 0.15);
        transform: translateY(-2px);
    }
}

.sw-card-header {
    padding: $sw-space-lg;
    border-bottom: 1px solid $sw-light-grey;
    background-color: $sw-mist;
}

.sw-card-body {
    padding: $sw-space-lg;
}

.sw-card-footer {
    padding: $sw-space-lg;
    border-top: 1px solid $sw-light-grey;
    background-color: $sw-mist;
}

// Form Components using mixins
.sw-form-group {
    margin-bottom: $sw-space-lg;
}

.sw-form-label {
    display: block;
    margin-bottom: $sw-space-xs;
    @include sw-text-style($sw-font-heading, $sw-font-weight-medium, $sw-text-primary);
    font-size: 0.875rem;
}

.sw-form-control {
    @include sw-form-control();
}

// Alert Components
.sw-alert {
    padding: $sw-space-md;
    border-radius: $sw-radius-md;
    border: 1px solid transparent;
    margin-bottom: $sw-space-md;
}

.sw-alert-primary {
    background-color: sw-color-opacity($sw-primary, 0.1);
    border-color: sw-color-opacity($sw-primary, 0.3);
    color: $sw-primary-dark;
}

.sw-alert-accent {
    background-color: sw-color-opacity($sw-accent, 0.1);
    border-color: sw-color-opacity($sw-accent, 0.2);
    color: $sw-primary;
}

.sw-alert-info {
    background-color: sw-color-opacity($sw-primary, 0.1);
    border-color: sw-color-opacity($sw-primary, 0.2);
    color: $sw-text-primary;
}

// Brand-Specific Utility Classes (Tailwind-Compatible)
// ===================================================
// These utilities are brand-specific and complement Tailwind CSS v4
// Removed duplicate utilities that Tailwind already provides

// Brand Shadow Utilities (using Sydney Wyde colors)
.sw-shadow-light {
    box-shadow: 0 2px 4px sw-color-opacity($sw-velvet-green, 0.1);
}
.sw-shadow-medium {
    box-shadow: 0 4px 8px sw-color-opacity($sw-velvet-green, 0.15);
}
.sw-shadow-heavy {
    box-shadow: 0 8px 16px sw-color-opacity($sw-velvet-green, 0.2);
}

// Brand Border Utilities (using Sydney Wyde colors)
.sw-border-primary {
    border: 1px solid $sw-primary;
}
.sw-border-accent {
    border: 1px solid $sw-accent;
}
.sw-border-mist {
    border: 1px solid $sw-mist;
}

// Animation utilities (brand-specific)
.sw-fade-in {
    animation: swFadeIn $sw-transition-normal ease-in-out;
}

.sw-slide-up {
    animation: swSlideUp $sw-transition-normal ease-in-out;
}

@keyframes swFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes swSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Responsive Brand Utilities (for brand-specific responsive behavior)
// ==================================================================

@each $breakpoint, $value in $sw-breakpoints {
    @include sw-breakpoint($breakpoint) {
        .sw-#{$breakpoint}-card-stacked {
            .sw-card {
                margin-bottom: $sw-space-md;
            }
        }
    }
}

// Print Styles (Brand-specific print optimizations)
// =================================================

@media print {
    :root {
        --sw-primary: #000000;
        --sw-text-primary: #000000;
        --sw-text-secondary: #333333;
        --sw-background: #ffffff;
        --sw-accent: #666666;
        --sw-shadow-light: none;
        --sw-shadow-medium: none;
        --sw-shadow-heavy: none;
    }

    .sw-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .sw-btn {
        border: 1px solid #000;
        background: transparent;
        color: #000;
    }

    .sw-bg-primary,
    .sw-bg-accent {
        background: transparent !important;
        color: #000 !important;
        border: 1px solid #000;
    }
}

// Accessibility
// =============

.sw-focus-visible:focus-visible {
    outline: 2px solid $sw-accent;
    outline-offset: 2px;
}

.sw-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

// High contrast mode support
@media (prefers-contrast: high) {
    :root {
        --sw-border-light: 2px solid currentColor;
        --sw-border-medium: 2px solid currentColor;
        --sw-border-primary: 2px solid currentColor;
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .sw-fade-in,
    .sw-slide-up {
        animation: none;
    }
}
