import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { SharedService } from '@core/services/shared.service';

@Component({
  selector: 'app-auth-layout',
  templateUrl: './auth-layout.component.html',
  standalone: true,
  imports: [RouterOutlet],
})
export class AuthLayoutComponent implements OnInit {
  constructor(private sharedService: SharedService) {}

  ngOnInit(): void {
    this.sharedService.loadImages();
  }
}
