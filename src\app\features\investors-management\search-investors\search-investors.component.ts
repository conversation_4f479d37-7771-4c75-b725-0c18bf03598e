import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AuthenticationService } from '@core/services/authentication.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { UserManagementService } from '@core/services/user-management.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogRef,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-search-investors',
  templateUrl: './search-investors.component.html',
  styleUrls: ['./search-investors.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    SkeletonModule,
    NbButtonModule,
  ],
})
export class SearchInvestorsComponent implements OnInit, OnDestroy {
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  users: any[] = [];
  statusData: any[] = [];
  sourceData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  eventFilters: any;
  constructor(
    protected dialogRef: NbDialogRef<any>,
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private router: Router,
    private authenticationService: AuthenticationService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private dialogService: NbDialogService,
    private userManagementService: UserManagementService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      lookup: true,
      userTypeId: 1, // Client users only
    } as Filters;

    await this.getStatus();
  }

  private getList(): void {
    this.investorsService.getInvestors(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.users = (data as any).payload.investors;
        this.totalRecords = (data as any).payload.rows;
        this.dtTrigger.next(this.users);
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  close(value = null): void {
    this.dialogRef.close(value);
  }
  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    delete this.filterParams.pageNumber;
    this.getList();
  }

  exportUser(): void {
    this.filterParams.export = true;
  }

  public async getStatus(): Promise<void> {
    this.statusData = (await this.userManagementService.getUserStatusAdmin().toPromise()).payload;
  }

  ngOnDestroy(): void { }

  editUser(user: any): void {
    this.sharedService.setFormParamValue({
      investorId: user.investorId,
      userId: user.userId,
    });
    this.router.navigate(['/investor/edit']);
  }

  createInvestor(): void {
    this.sharedService.setFormParamValue({});
    this.router.navigate(['/investor/new']);
  }

  onSelectChange(user: any, statusId: string): void { }

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }
}
