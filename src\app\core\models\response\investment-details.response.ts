export interface ImageUrl {
  url: string;
  sequence: number;
  documentKey: string;
  attachedBy: number;
}

export interface InvestmentDetailsResponse {
  id?: number;
  overview?: string;
  title?: string;
  borrower?: string;
  totalOpportunity?: number;
  investmentType?: string;
  assetType?: string;
  term?: number;
  investmentReturn?: number;
  lvr?: number;
  minInvestment?: number;
  assetTypeId?: number | null;
  investmentTypeId?: number | null;
  showApply?: boolean;
  startDate?: Date;
  stateId?: number;
  suburb?: string;
  statusId?: number;
  templateId?: number;
  imageUrls?: ImageUrl[];
  loanId?: string; // TMO Loan ID field
}

export interface KeyData extends InvestmentDetailsResponse {
  investmentId: number;
  isDeleted?: boolean;
  userId?: number;
}
