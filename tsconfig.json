/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "lib": ["ES2022", "dom"],
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "useDefineForClassFields": false,
    "skipLibCheck": true,

    // Alias
    "baseUrl": ".",
    "paths": {
      "@environments/*": ["./src/environments/*"],
      "@assets/*": ["./src/assets/*"],
      "@core/*": ["./src/app/core/*"],
      "@features/*": ["./src/app/features/*"],
      "@pages/*": ["./src/app/pages/*"],
      "@components/*": ["./src/app/components/*"],
      "@shared/*": ["./src/app/shared/*"]
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
