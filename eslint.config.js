// @ts-check
const eslint = require("@eslint/js");
const tseslint = require("typescript-eslint");
const angular = require("angular-eslint");

module.exports = tseslint.config(
  {
    ignores: ["dist", "node_modules", ".angular"],
  },
  {
    files: ["**/*.ts"],
    extends: [
      eslint.configs.recommended,
      ...tseslint.configs.recommended,
      ...tseslint.configs.stylistic,
      ...angular.configs.tsRecommended,
    ],

    processor: angular.processInlineTemplates,
    rules: {
      // TODO: uncomment when ready

      // "@angular-eslint/directive-selector": [
      //   "error",
      //   {
      //     type: "attribute",
      //     prefix: "app",
      //     style: "camelCase",
      //   },
      // ],
      // "@angular-eslint/component-selector": [
      //   "error",
      //   {
      //     type: "element",
      //     prefix: "app",
      //     style: "kebab-case",
      //   },
      // ],
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": "off",
      // -------------------------------

      "@angular-eslint/prefer-inject": "off", // NOTE: REMOVE LATER
      "@typescript-eslint/no-empty-function": "off", // NOTE: REMOVE LATER
      "@angular-eslint/no-empty-lifecycle-method": "off", // NOTE: REMOVE LATER
      "@angular-eslint/component-selector": "off", // NOTE: REMOVE LATER
      "@angular-eslint/prefer-standalone": "off", // NOTE: REMOVE LATER
      "@angular-eslint/directive-selector": "off", // NOTE: REMOVE LATER
      "no-useless-escape": "off",
    },
  },
  {
    files: ["**/*.html"],
    extends: [...angular.configs.templateRecommended, ...angular.configs.templateAccessibility],
    rules: {
      "@angular-eslint/template/interactive-supports-focus": "off",
      "@angular-eslint/template/click-events-have-key-events": "off",
      "@angular-eslint/template/alt-text": "off", // NOTE: REMOVE LATER
      "@angular-eslint/template/label-has-associated-control": "off", // NOTE: REMOVE LATER
    },
  },
);
