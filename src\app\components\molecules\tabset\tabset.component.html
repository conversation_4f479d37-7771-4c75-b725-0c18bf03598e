<ul class="tabset">
  <li
    *ngFor="let tab of tabs"
    (click)="selectTab(tab)"
    (keyup.space)="selectTab(tab)"
    (keyup.enter)="selectTab(tab)"
    [class.responsive]="tab.responsive"
    [class.active]="tab.active"
    [class.disabled]="tab.disabled"
    [attr.tabindex]="tab.disabled ? -1 : 0"
    class="tab"
  >
    <a href (click)="$event.preventDefault()" tabindex="-1" class="tab-link">
      <nb-icon *ngIf="tab.tabIcon" [config]="tab.tabIcon"></nb-icon>

      <span *ngIf="tab.tabTitle && !tab.badgeDot" class="tab-text">{{ tab.tabTitle }}</span>

      <nb-user
        *ngIf="tab.tabTitle && tab.badgeDot"
        size="small"
        name="{{ tab.tabTitle }}"
        title="{{ tab.tabTitle }}"
        [showName]="tab.active"
        [showTitle]="false"
      >
      </nb-user>
    </a>

    <!-- <nb-badge *ngIf="tab.badgeText || tab.badgeDot" [text]="tab.badgeText" [dotMode]="tab.badgeDot"
      [status]="tab.badgeStatus" [position]="tab.badgePosition">
    </nb-badge> -->
  </li>
</ul>
<ng-content select="nb-tab"></ng-content>
