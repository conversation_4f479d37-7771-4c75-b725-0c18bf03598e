@use "themes" as *;

:host {
  --user-picture-box-background-color: #0f3b33;
  // --chat-padding: 45px 10px;
}

:host ::ng-deep {
  .nb-form-control-container {
    height: 100%;
  }

  .select-button {
    height: 100%;
  }

  // svg:active path {
  //     fill: blue !important;
  // }

  p-selectbutton {
    height: 100%;
    display: inline-flex;
  }

  .nb-form-field-addon {
    width: 0px;
    margin-left: -40px;
  }

  .cke_contents {
    border: none !important;
    border-radius: 10px;
  }

  .roundedSelect .p-button:last-of-type {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 10px !important;
  }

  .roundedSelect .p-button:first-of-type {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 10px !important;
  }

  .allRoundedSelect .p-button:last-of-type {
    border-top-right-radius: 10px !important;
    border-bottom-right-radius: 10px !important;
  }

  .allRoundedSelect .p-button:first-of-type {
    border-top-left-radius: 10px !important;
    border-bottom-left-radius: 10px !important;
  }

  .allRoundedSelect .p-buttonset .p-button {
    height: 100% !important;
    width: 120px;
  }

  .p-selectbutton .p-button {
    background: #f8f8f8;
    // border: 1px solid #ced4da;
    color: #c5cbd7;
    // transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }

  .p-selectbutton .p-button.p-highlight {
    color: var(--color-velvet-700);
    background: #ffffff !important;
    border-color: #e7e9ec !important;

    svg path {
      fill: var(--color-velvet-700);
    }
  }

  .p-button:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0 #a6d5fa;
  }

  app-chat-message.reply .message {
    display: flex;
    flex-direction: column;
    align-items: stretch !important;
    margin-left: 0;
  }
}

.attachmentNumber {
  display: flex;
  font-size: 19px;
  margin-right: 25px;
  align-items: center;
}

.attachmentNumberTask {
  display: flex;
  font-size: 19px;
  margin-right: 25px;
  align-items: center;
}

.search-filter {
  width: auto !important;
}

.fullHeight,
.nb-form-control-container {
  height: 100%;
}

.submitSection {
  display: inline-flex;
  justify-content: flex-end;
  width: 100%;
}

.submitSectionEnd {
  display: inline-flex;
  justify-content: space-between;
  width: 100%;
}

.largeIcon {
  font-size: 35px !important;
  color: #838d9c;
  min-height: 35px !important;
  min-width: 35px !important;
}

nb-user {
  margin-left: 7px;
}

.button {
  height: 60px;
  width: 176px;
}

.filter-bar {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  height: 50px;
  align-items: flex-start;
}

.add-doc-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 9px;
}

.file-container {
  width: 20px;
  padding: 0.5rem;
  position: relative;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90px;

  p {
    font-size: 16px;
    line-height: 24px;
    color: #bfc6d0;
  }

  input {
    opacity: 0;
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    cursor: pointer;
  }

  label {
    color: white;
    width: 183px;
    height: 44px;
    border-radius: 21.5px;
    background-color: #db202f;
    padding: 8px 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #38424c;
  }
}

hr.solid {
  margin: 2px 58px 30px -15px;
  width: 109%;
}

.files-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 5px 0px;

  .single-file {
    display: flex;

    .delete {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .name {
      font-size: 14px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
      cursor: pointer;
    }

    .attachment {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: center;
    }

    .size {
      font-size: 12px;
      font-weight: 500;
      color: #a4a4a4;
      margin: 0;
      margin-bottom: 0.25rem;
    }

    .info {
      display: flex;
      padding: 8px;
      border: 2px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
      margin: 4px 3px;
    }
  }
}
