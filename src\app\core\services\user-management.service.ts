import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Filters } from './shared.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserManagementService {
  constructor(private http: HttpClient) {}

  getUsers(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/User/get-users`, userFilters);
  }

  getRoleForAdmin(usertypeId?: number): any {
    if (usertypeId) {
      return this.http.get(`${environment.apiURL}/api/User/roles/${usertypeId}`);
    } else {
      return this.http.get(`${environment.apiURL}/api/User/roles`);
    }
  }

  getUserTypesAdmin(): any {
    return this.http.get(`${environment.apiURL}/api/User/usertypes`);
  }

  getUserByIdForAdmin(userId: number): any {
    return this.http.get(`${environment.apiURL}/api/User/${userId}`);
  }

  getUserStatusAdmin(): any {
    return this.http.get(`${environment.apiURL}/api/User/get-status`);
  }

  updateUser(userInfo: any): any {
    return this.http.put(`${environment.apiURL}/api/User/update-user`, userInfo);
  }

  updateStatus(data: any): any {
    return this.http.post(`${environment.apiURL}/api/User/update-status`, data);
  }

  archiveUser(data: any): any {
    return this.http.post(`${environment.apiURL}/api/User/archive`, data);
  }

  copyLink(data: any): any {
    return this.http.post(`${environment.apiURL}/api/user/copy-link`, data);
  }
}
