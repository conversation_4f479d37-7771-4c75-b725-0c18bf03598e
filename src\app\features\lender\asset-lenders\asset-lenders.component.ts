import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { AssetLendersResponse, Lenders } from '@core/models/response/asset-lenders.response';
import { AssetService } from '@core/services/asset.service';
import {
  NbDialogService,
  NbToastrService,
  NbCardModule,
  NbIconModule,
  NbButtonModule,
  NbTooltipModule,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SharedService } from '@core/services/shared.service';
import { Filters } from '@core/services/shared.service';
import { TableModule } from 'primeng/table';

@Component({
  selector: 'app-asset-lenders',
  templateUrl: './asset-lenders.component.html',
  styleUrls: ['./asset-lenders.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbIconModule, NbTooltipModule, NbButtonModule, TableModule],
})
export class AssetLendersComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  lenders: any[] = [];
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  @Input() assetKeyDataId!: number | undefined;
  @Output() lenderSelect = new EventEmitter<number>();

  @Output() latestLender = new EventEmitter<number>();

  constructor(
    private spinner: NgxSpinnerService,
    private sharedService: SharedService,
    private assetService: AssetService,
    private cdr: ChangeDetectorRef,
    private dialogService: NbDialogService,
    private toast: NbToastrService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId || undefined;
    this.getList();
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    if (this.assetKeyDataId) {
      this.assetService
        .getLenders({
          assetId: this.assetKeyDataId,
          includeDraft: true,
        })
        .subscribe((data: AssetLendersResponse) => {
          if (data.success) {
            this.lenders = (data as any).payload;
            if (this.lenders && this.lenders.length > 0) {
              this.latestLender.emit(this.lenders[0].id);
            } else {
              this.latestLender.emit(undefined);
            }
            this.spinner.hide();
            this.loading = false;
          }
        });
    }
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  ngOnDestroy(): void {}

  editLender(lender: Lenders): void {
    this.lenderSelect.emit(lender.id);
  }

  confirmArchive(lender: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Archive Lender',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Archive Lender',
          yesButtonIcon: 'archive-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.deleteLender(lender);
        }
      });
  }

  deleteLender(lender: any): void {
    this.assetService
      .archiveLender({
        assetId: this.assetKeyDataId,
        lenderId: lender.id,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.toast.success('Lender Archived Successfully.', 'Success!');
          this.getList();
        } else {
          this.toast.danger(data.error.message, 'Error!');
        }
      });
  }
}
