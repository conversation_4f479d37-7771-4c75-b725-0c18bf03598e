<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Asset Portfolio</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div *ngIf="!isLender()">
      <nb-select
        fullWidth
        placeholder="Status"
        name="statuses"
        id="value"
        (selectedChange)="dt.filter($event, 'facilityStatusId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let source of statusData" [value]="source.id">
          {{ source.name }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <nb-select
        fullWidth
        placeholder="Facility Type"
        name="facilityType"
        id="value"
        (selectedChange)="dt.filter($event, 'facilityTypeId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let facilityType of facilityTypeData" [value]="facilityType.id">
          {{ facilityType.name }}
        </nb-option>
      </nb-select>
    </div>

    <div *ngIf="!isLender()">
      <nb-select
        fullWidth
        placeholder="Lender"
        name="lenderData"
        id="value"
        (selectedChange)="dt.filter($event, 'lenderId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let lenderOrg of lenderData" [value]="lenderOrg.id">
          {{ lenderOrg.name }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>

    <!-- <div>
            <button class="float-right" status="basic" (click)="exportasset()" nbButton>
                <nb-icon icon="download"></nb-icon>
            </button>
        </div> -->
  </div>
  <div *ngIf="!isLender()" class="lg:w-3/12 px-2 my-[15px]">
    <button class="float-right" nbButton status="primary" (click)="createAsset()">
      <nb-icon icon="plus-outline"></nb-icon> Add new Asset
    </button>
  </div>
</div>

<p-skeleton *ngIf="!assets"></p-skeleton>

<div *ngIf="assets">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="assets"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="true"
        [rows]="50"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="[
          'facilityName',
          'lendeReference',
          'address',
          'project',
          'borrower',
          'facilityType',
          'term',
        ]"
        sortField="financialClose"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 110px" [pSortableColumn]="'facilityName'">
              <div>
                <div>Facility Name</div>
                <p-sortIcon [field]="'facilityName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 110px" [pSortableColumn]="'lendeReference'">
              <div>
                <div>Lender Reference</div>
                <p-sortIcon [field]="'lendeReference'"></p-sortIcon>
              </div>
            </th>
            <th *ngIf="!isLender()" style="min-width: 110px" [pSortableColumn]="'facilityStatus'">
              <div>
                <div>Facility Status</div>
                <p-sortIcon [field]="'facilityStatus'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 170px" [pSortableColumn]="'project'">
              <div>
                <div>Project</div>
                <p-sortIcon [field]="'project'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 170px" [pSortableColumn]="'borrower'">
              <div>
                <div>Borrower</div>
                <p-sortIcon [field]="'borrower'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 110px" [pSortableColumn]="'facilityType'">
              <div>
                <div>Facility Type</div>
                <p-sortIcon [field]="'facilityType'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'facilityLimit'">
              <div>
                <div>Facility Limit</div>
                <p-sortIcon [field]="'facilityLimit'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 110px" [pSortableColumn]="'financialClose'">
              <div>
                <div>Financial Close</div>
                <p-sortIcon [field]="'financialClose'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'term'">
              <div>
                <div>Term (months)</div>
                <p-sortIcon [field]="'term'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 110px" [pSortableColumn]="'maturityDate'">
              <div>
                <div>Maturity Date</div>
                <p-sortIcon [field]="'maturityDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 80px" [pSortableColumn]="'lvrLimit'">
              <div>
                <div>LVR Limit</div>
                <p-sortIcon [field]="'lvrLimit'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 80px" [pSortableColumn]="'ltc'">
              <div>
                <div>LTC</div>
                <p-sortIcon [field]="'ltc'"></p-sortIcon>
              </div>
            </th>
            <th *ngIf="!isLender()" style="min-width: 80px">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-asset>
          <tr>
            <td style="min-width: 110px">
              <span
                *ngIf="isLender() && asset.publishedCount > 0"
                class="text-blue-600 cursor-pointer"
                (click)="assetUpdates(asset)"
              >
                {{ asset.facilityName }}
              </span>
              <span *ngIf="isLender() && asset.publishedCount === 0"> {{ asset.facilityName }} </span>
              <span *ngIf="isAdmin() || isOriginatorManager()">{{ asset.facilityName }} </span>
            </td>
            <td style="min-width: 110px">{{ asset.lenderReference }}</td>
            <td *ngIf="!isLender()" style="min-width: 110px">{{ asset.facilityStatus }}</td>
            <td style="min-width: 170px">{{ asset.projectSummary }}</td>
            <td style="min-width: 170px">{{ asset.borrower }}</td>
            <td style="min-width: 110px">{{ asset.facilityType }}</td>
            <td style="min-width: 100px">{{ asset.facilityLimit | currency: "USD" : "symbol" : "1.0" }}</td>
            <td style="min-width: 110px">{{ asset.financialClose | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 100px">{{ asset.term }}</td>
            <td style="min-width: 110px">{{ asset.maturityDate | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 80px">{{ asset.lvrLimit | mask: "separator.2" }}%</td>
            <td style="min-width: 80px">{{ asset.ltc | mask: "separator.2" }}%</td>
            <td style="min-width: 80px" *ngIf="isAdmin() || isOriginatorManager()">
              <button
                nbButton
                ghost
                shape="round"
                status="default"
                class="button-icon"
                (click)="editAsset(asset)"
                nbTooltip="Edit"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="editIcon" pack="custom"></nb-icon>
              </button>

              <button
                *ngIf="isAdmin()"
                nbButton
                ghost
                shape="round"
                status="default"
                class="button-icon"
                (click)="archiveAssetConfirm(asset)"
                nbTooltip="Archive"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="archiveIcon" pack="custom"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" *ngIf="!eventFilters" [attr.colspan]="10">
              No assets found
            </td>
            <td style="text-align: center; display: block" *ngIf="eventFilters" [attr.colspan]="10">
              Sorry, your search did not return any matching results. Please try again
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
