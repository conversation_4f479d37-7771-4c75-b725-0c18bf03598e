nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

p {
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 30px;
}

.title {
  font-weight: 600;
  font-size: 20px;
  line-height: 40px;
  color: #0a0a0a;
}

.outer-circle,
.inner-circle {
  left: 13px !important;
}

.image-asset {
  width: 100%;
  height: auto;
  border: 1px solid #e6e6e6;
  border-radius: 12px;
}

.image {
  position: relative;
  display: inline-block;
  margin: 10px;
}
.overlay {
  display: none;
}
.image:hover .overlay {
  width: 100%;
  height: 200px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 1;
  display: inline-block;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-align: right;
  padding: 12px;
}
img {
  vertical-align: top;
}
