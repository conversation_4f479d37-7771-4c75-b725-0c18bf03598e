<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2">
      <h5 class="w-6/12 px-2">
        <div class="title">Import Payments</div>
      </h5>

      <div class="w-6/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="close()">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>

  <nb-card-body>
    <div
      class="lg:w-full px-2 md:w-full px-2 w-full px-2 my-[15px]"
      style="width: 600px; margin: 22px 50px"
      [hidden]="uploadedDocuments"
    >
      <div class="file-container" for="fileDropRef" appDnd (fileDropped)="onFileDropped($event)">
        <input type="hidden" />

        <input
          type="file"
          #fileDropRef
          id="fileDropRef"
          (change)="fileBrowseHandler($event)"
          accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
        />

        <p class="m-0" for="fileDropRef">
          <nb-icon icon="file-add"></nb-icon>
          Drop Document here or Click to upload.
        </p>
      </div>
      <div *ngIf="submitted && !this.uploadedDocuments" class="invalid-feedback">
        <div *ngIf="!this.uploadedDocuments">Document is required.</div>
      </div>
    </div>
    <div
      class="w-8/12 px-2"
      style="width: 600px; margin: 22px 50px"
      *ngIf="uploadedDocuments && uploadedDocuments.length > 0"
    >
      <div class="files-list">
        <div class="single-file" *ngFor="let file of uploadedDocuments; let i = index">
          <div class="info">
            <div class="name">
              {{ file?.name }}
            </div>
            <div class="delete" style="cursor: pointer" (click)="clearDocuments()">
              <nb-icon class="file-delete" icon="close-circle"></nb-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="w-full px-2">
      <button
        class="float-right"
        [nbSpinner]="loading"
        nbButton
        status="primary"
        style="min-width: 135px"
        (click)="importPayments()"
      >
        Import
      </button>
    </div>
  </nb-card-footer>
</nb-card>
