<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5 *ngIf="isInvestor" style="margin: 20px 12px">Investments</h5>
      <h5 *ngIf="!isInvestor" style="margin: 20px 12px">View Investments</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="md:w-8/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select fullWidth placeholder="Status" [(ngModel)]="selectedStatus" (selectedChange)="onStatusChange($event)">
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let status of statusData" [value]="status.id">
          {{ status.name }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input
          type="text"
          fullWidth
          placeholder="Search investments"
          [(ngModel)]="searchText"
          (ngModelChange)="onSearchChange()"
          nbInput
        />
      </nb-form-field>
    </div>
  </div>
  <div class="md:w-4/12 px-2 text-right" style="margin: auto; text-align: right">
    <button *ngIf="isAdmin()" nbButton status="primary" (click)="addNewInvestment()">ADD NEW INVESTMENT</button>

    <button *ngIf="isAdmin()" nbButton ghost status="primary" (click)="exportInvestment()">
      <i class="pi pi-download"></i>
    </button>
  </div>
</div>

<div class="investments-list">
  <nb-card class="investment-card" *ngFor="let investment of investments">
    <nb-card-body>
      <div class="image-container">
        <img *ngIf="investment.cdnUrl" [src]="investment.imageUrl" loading="lazy" />

        <!-- <img *ngIf="!investment.cdnUrl && investment.imageData" [src]="investment.imageData" /> -->
        <ng-container *ngIf="!investment.cdnUrl && imageHelper.isImage(investment.imageUrl)">
          <img [src]="investment.imageUrl" loading="lazy" />
        </ng-container>

        <ng-container *ngIf="!investment.cdnUrl && !imageHelper.isImage(investment.imageUrl)">
          <nb-icon class="img" icon="image-outline"></nb-icon>
        </ng-container>

        <nb-icon class="img" icon="image-outline" *ngIf="!investment.cdnUrl && !investment.imageUrl"></nb-icon>

        <div class="invested" *ngIf="investment.investedAmount > 0">
          Invested - {{ investment.investedAmount | currency: "USD" : "symbol" : "1.0" }}
        </div>

        <div class="invested" *ngIf="!(investment.investedAmount > 0) && investment.applicationAmount > 0">
          Application - {{ investment.applicationAmount | currency: "USD" : "symbol" : "1.0" }}
        </div>

        <div class="location">
          <nb-icon icon="imageLocationIcon" pack="custom"></nb-icon> &nbsp; {{ investment.title }}
        </div>
      </div>
      <div class="heading-title">
        <div>
          <div class="total">Total Opportunity</div>
          <div class="total-value">{{ investment.totalOpportunity | currency: "USD" : "symbol" : "2.0" }}</div>
        </div>
        <div class="status" *ngIf="!isInvestor">
          <div class="no-hover" [ngClass]="investment.statusName">
            {{ investment.statusName }}
          </div>
        </div>
      </div>
      <hr class="w-full rounded-[17px] mt-[13px] mb-[13px] border-gray-200" />
      <div class="flex flex-wrap -mx-2" style="margin: 10px">
        <div class="w-6/12 px-2">
          <label>Investment Type</label>
          <p>{{ investment.investmentType ? investment.investmentType : "-" }}</p>
        </div>
        <div class="w-6/12 px-2">
          <label>Asset Type</label>
          <p>{{ investment.assetType ? investment.assetType : "-" }}</p>
        </div>
        <div class="w-6/12 px-2">
          <label>Term</label>
          <p>{{ investment.term }} Months</p>
        </div>
        <div class="w-6/12 px-2">
          <label>Target Return</label>
          <p>{{ investment.investmentReturn }}</p>
        </div>
        <div class="w-6/12 px-2">
          <label>LVR</label>
          <p>{{ investment.lvr }}</p>
        </div>
        <div class="w-6/12 px-2">
          <label>Minimum Investment</label>
          <p>{{ investment.minInvestment | currency: "USD" : "symbol" : "2.0" }}</p>
        </div>
      </div>
      <hr class="w-full rounded-[17px] mt-[13px] mb-[13px] border-gray-200" />
      <div class="text-center investment-button">
        <ng-container *ngIf="isInvestor">
          <button
            *ngIf="investment.statusName === 'Open'"
            fullWidth
            nbButton
            status="primary"
            (click)="editInvestor(investment)"
          >
            REVIEW INVESTMENT
          </button>
          <div *ngIf="investment.statusName === 'Closed'" fullWidth style="margin: auto" class="draft-button">
            CLOSED
          </div>
          <div *ngIf="investment.statusName === 'Funded'" fullWidth style="margin: auto" class="draft-button">
            FUNDED
          </div>
        </ng-container>
        <ng-container *ngIf="isAdmin()">
          <button nbButton fullWidth status="primary" (click)="editInvestor(investment)">EDIT INVESTMENT</button>
        </ng-container>
        <ng-container *ngIf="isManager()">
          <button nbButton fullWidth status="primary" (click)="editInvestor(investment)">VIEW INVESTMENT</button>
        </ng-container>
      </div>
    </nb-card-body>
  </nb-card>
</div>

<!-- No results message -->
<div *ngIf="!firstLoad && !loading && investments?.length === 0" class="no-results-container">
  <nb-card>
    <nb-card-body class="text-center">
      <nb-icon icon="alert-circle-outline" status="warning" class="no-results-icon"></nb-icon>
      <h3>No investments found</h3>
      <p>We couldn’t find any investments that match your search criteria</p>
    </nb-card-body>
  </nb-card>
</div>

<ng-container *ngIf="loading || firstLoad">
  <nb-card [nbSpinner]="loading" nbSpinnerStatus="danger" style="background: transparent">
    <nb-card-body style="background: transparent"></nb-card-body>
  </nb-card>
</ng-container>

<div class="pagination-container bottom-pagination mb-[20px]">
  <p-paginator
    #paginator
    [rows]="pageSize"
    [totalRecords]="totalRecords"
    [rowsPerPageOptions]="[10, 20, 50]"
    [showCurrentPageReport]="true"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} investments"
    (onPageChange)="onPageChange($event)"
  >
  </p-paginator>
</div>
