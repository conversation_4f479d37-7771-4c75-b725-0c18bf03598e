export interface ProfileAttribute {
  key: string;
  value: string;
}

export interface RegisterUser {
  profileAttributes?: ProfileAttribute[];
  roleId?: number;
  userId?: number;
  userName?: string;
  email?: string;
  mobile?: string;
  countryCode?: string;
  host: string;
  clientType?: number;
  userTypeId?: number;
  isInvestor?: boolean;
  entityName?: string;
  entityTypeId?: number;
  address?: string;
  investorId?: number;
  statusId?: number;
  isExisting?: boolean;
  lenderOrgId?: number;
  assetManagementUser?: boolean;
}
