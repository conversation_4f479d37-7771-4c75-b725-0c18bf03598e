import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NbMenuItem, NbIconModule } from '@nebular/theme';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-sidebar-footer',
  templateUrl: './sidebar-footer.component.html',
  styleUrls: ['./sidebar-footer.component.scss'],
  standalone: true,
  imports: [CommonModule, NbIconModule],
})
export class SidebarFooterComponent implements OnInit {
  steps: any;
  progress: any = 99.99;
  minute = 0;
  constructor(private router: Router) {}

  menuItems: NbMenuItem[] = [];

  ngOnInit(): void {}

  showSteps(): boolean {
    return this.router.url.includes('/user-form');
  }
}
