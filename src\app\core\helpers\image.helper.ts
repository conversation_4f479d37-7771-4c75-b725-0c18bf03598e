import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ImageHelper {
  constructor() {}

  static isHttpsImage(url: string) {
    return /(https?:\/\/.*)|(\/\/.*)/.test(url);
  }

  static noImage(w = 600, h = 400, text = 'No Image') {
    const queryString = w + 'x' + h + '/ccc/fff.png&text=' + text;
    return '//dummyimage.com/' + queryString;
  }

  static encodeUrl(url: string) {
    if (url.includes(' ')) {
      return encodeURI(url);
    } else {
      return url;
    }
  }

  static isImage(mimetype: string) {
    return /^image/.test(mimetype);
  }

  static isVideo(mimetype: string) {
    return /^video/.test(mimetype) || /\/mp4$/.test(mimetype) || /\/MP4$/.test(mimetype) || /\/mov$/.test(mimetype);
  }

  static isBase64(input: string) {
    return /^data:image\//.test(input);
  }

  static isPdfFile(mime_type: string) {
    return /\/pdf/.test(mime_type);
  }

  static isDocx(mimetype: string) {
    return /^docx/.test(mimetype);
  }

  static isAudio(mimetype: string) {
    return /^m4v/.test(mimetype);
  }

  static isCSV(mimetype: string) {
    return mimetype.includes('text/csv') || mimetype.includes('text/comma-separated-values');
  }
}
