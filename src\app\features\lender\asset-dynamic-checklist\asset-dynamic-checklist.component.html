<div class="w-full px-2">
  <nb-card>
    <nb-card-body>
      <div class="flex flex-wrap -mx-2 my-[15px]" *ngFor="let structure of checkListContent">
        <p-table [value]="structure.checklist_structure" responsiveLayout="scroll" [loading]="tableLoading">
          <ng-template class="headerStyle" pTemplate="caption">
            {{ structure.header }}
          </ng-template>
          <ng-template pTemplate="header">
            <tr>
              <th style="width: 150px">ID</th>
              <th style="width: 500px">Title & Description</th>
              <th style="width: 200px">
                <div style="margin-left: 36%">Loan Manager</div>
              </th>
              <th style="width: 200px">
                <div style="margin-left: 40%">Reviewer</div>
              </th>
              <th style="width: 200px">
                <div style="margin-left: 36%">Not Applicable</div>
              </th>
              <th style="width: 200px">
                <div style="margin-left: 22%">Notes</div>
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-rowData let-index="rowIndex">
            <tr>
              <td style="width: 150px">{{ index + 1 }}</td>
              <td style="width: 500px" [innerHTML]="sanitizer.bypassSecurityTrustHtml(rowData.contentHtml)"></td>
              <td style="width: 200px">
                <div [ngSwitch]="rowData.loanManagerId > 0">
                  <div *ngSwitchCase="false">
                    <div style="margin-left: 42%; margin-bottom: 15px">
                      <nb-icon
                        icon="checkBoxIcon"
                        pack="custom"
                        (click)="toggleTaskCompletion('loanManager', structure.checklist_structure[index])"
                        style="cursor: pointer; width: 53px; padding-right: 6px; height: 40px"
                      ></nb-icon>
                    </div>
                  </div>
                  <div *ngSwitchCase="true">
                    <div style="margin-left: 44%; padding-top: 2px">
                      <nb-user
                        style="font-size: 30px !important; cursor: pointer"
                        color="#002c24"
                        [name]="rowData.loanManagerName"
                        status="success"
                        [showName]="false"
                        [showTitle]="false"
                        nbTooltip="{{ rowData.loanManagerName }}"
                        nbTooltipPlacement="left"
                        size="medium"
                        (click)="resetTaskCompletion('loanManager', structure.checklist_structure[index])"
                      >
                      </nb-user>
                    </div>
                    <div style="margin-left: 37%; margin-top: 5px">
                      {{ rowData.loanManagerDate | date: "dd/MM/YYYY" }}
                    </div>
                  </div>
                </div>
              </td>
              <td style="width: 200px">
                <div [ngSwitch]="rowData.reviewerId > 0">
                  <div *ngSwitchCase="false">
                    <div style="margin-left: 42%; margin-bottom: 15px">
                      <nb-icon
                        icon="checkBoxIcon"
                        pack="custom"
                        (click)="toggleTaskCompletion('reviewer', structure.checklist_structure[index])"
                        style="cursor: pointer; width: 53px; padding-right: 6px; height: 40px"
                      ></nb-icon>
                    </div>
                  </div>
                  <div *ngSwitchCase="true">
                    <div style="margin-left: 40%; padding-left: 10px; padding-top: 2px">
                      <nb-user
                        style="font-size: 30px !important; cursor: pointer"
                        color="#002c24"
                        [name]="rowData.reviewerName"
                        size="medium"
                        status="success"
                        [showName]="false"
                        [showTitle]="false"
                        nbTooltip="{{ rowData.reviewerName }}"
                        nbTooltipPlacement="left"
                        (click)="resetTaskCompletion('reviewer', structure.checklist_structure[index])"
                      >
                      </nb-user>
                    </div>
                    <div style="margin-left: 38%; margin-top: 5px">
                      {{ rowData.reviewerDate | date: "dd/MM/YYYY" }}
                    </div>
                  </div>
                </div>
              </td>
              <td style="width: 200px">
                <div [ngSwitch]="rowData.notApplicableId > 0">
                  <div *ngSwitchCase="false">
                    <div style="margin-left: 42%; margin-bottom: 15px">
                      <nb-icon
                        icon="checkBoxIcon"
                        pack="custom"
                        (click)="toggleTaskCompletion('notApplicable', structure.checklist_structure[index])"
                        style="cursor: pointer; width: 53px; padding-right: 6px; height: 40px"
                      ></nb-icon>
                    </div>
                  </div>
                  <div *ngSwitchCase="true">
                    <div style="margin-left: 44%; padding-top: 2px">
                      <nb-user
                        style="font-size: 30px !important; cursor: pointer"
                        color="#002c24"
                        [name]="rowData.notApplicableName"
                        size="medium"
                        status="success"
                        [showName]="false"
                        [showTitle]="false"
                        nbTooltip="{{ rowData.notApplicableName }}"
                        nbTooltipPlacement="left"
                        (click)="resetTaskCompletion('notApplicable', structure.checklist_structure[index])"
                      >
                      </nb-user>
                    </div>
                    <div style="margin-left: 37%; margin-top: 5px">
                      {{ rowData.notApplicableDate | date: "dd/MM/YYYY" }}
                    </div>
                  </div>
                </div>
              </td>
              <td style="width: 200px">
                <div style="display: flex; align-items: center; display: flex; align-items: center; padding-left: 18%">
                  <button
                    nbButton
                    ghost
                    shape="round"
                    status="default"
                    class="button-icon"
                    (click)="editNote(structure.checklist_structure[index], false)"
                    nbTooltip="Edit Note"
                    nbTooltipStatus="control"
                    nbTooltipPlacement="bottom"
                    nbPopoverTrigger="hover"
                    [nbTooltipDisabled]="allNotes[rowData.id].length > 0 ? false : true"
                    [nbPopoverContext]="{ id: rowData.id, notes: structure.checklist_structure[index] }"
                    nbPopoverPlacement="bottom"
                    [nbPopover]="notesTemplateRef"
                  >
                    <nb-icon icon="assetEditIcon" pack="custom" style="height: 26px; width: 26px"> </nb-icon>
                    <div style="margin-top: 5px">
                      {{ allNotes[rowData.id].length > 0 ? allNotes[rowData.id].length : null }}
                    </div>
                  </button>
                  <button
                    nbButton
                    ghost
                    shape="round"
                    status="default"
                    class="button-icon"
                    nbTooltip="Documents"
                    nbTooltipStatus="control"
                    nbTooltipPlacement="bottom"
                    nbPopoverTrigger="hover"
                    [nbTooltipDisabled]="allUploadedDocuments[rowData.id].length > 0 ? false : true"
                    [nbPopoverContext]="rowData.id"
                    nbPopoverPlacement="bottom"
                    [nbPopover]="documentTemplateRef"
                  >
                    <nb-icon icon="attachFileIcon" style="height: 26px; width: 26px" pack="custom"> </nb-icon>
                    <div style="margin-top: 5px">
                      {{ allUploadedDocuments[rowData.id].length > 0 ? allUploadedDocuments[rowData.id].length : null }}
                    </div>
                  </button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </nb-card-body>
  </nb-card>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="w-full px-2">
    <div class="popup-close float-right">
      <button
        class="button float-right"
        [nbSpinner]="loading"
        [disabled]="!unsavedItems || updating"
        (click)="updateChecklist()"
        nbButton
        size="large"
        status="primary"
      >
        UPDATE
      </button>
    </div>
  </div>
</div>

<!--  -->

<ng-template #notesTemplateRef let-context>
  <nb-list style="min-width: 400px">
    <nb-list-item *ngFor="let note of allNotes[context.id]" (click)="editNote(context.notes, note)">
      <div class="user-list-item">
        <nb-user
          style="font-size: 30px !important; justify-content: center"
          color="#002c24"
          [name]="note.userName"
          size="medium"
          status="success"
          [showName]="true"
          [showTitle]="false"
          nbTooltip="{{ note.userName }}"
          nbTooltipPlacement="right"
        >
        </nb-user>
        <span>
          {{ note.noteDate | date: "dd/MM/YYYY" }}
        </span>
        <span style="display: inline-flex" *ngIf="note.noteDocuments && note.noteDocuments.length">
          <nb-icon icon="attachFileIcon" pack="custom" style="margin-right: 5px"></nb-icon>
          {{ note.noteDocuments.length }}
        </span>
      </div>
    </nb-list-item>
  </nb-list>
</ng-template>
<ng-template #documentTemplateRef let-id>
  <div class="files-list">
    <div class="single-file" *ngFor="let file of allUploadedDocuments[id]">
      <div class="info w-full px-2" (click)="downloadFile(file)">
        <div class="attachment">
          <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
        </div>
        <div class="name">
          {{ file?.documentName }}
        </div>
      </div>
    </div>
  </div>
</ng-template>
