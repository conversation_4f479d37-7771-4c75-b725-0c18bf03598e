.message-form-bar {
  display: flex;
  border-radius: 5px;
  border: 1px solid var(--color-grey-600);
  padding: 5px;
  background: white;
}

input.with-button {
  border-top-right-radius: 5px !important;
}
input.with-button {
  border-bottom-right-radius: 5px !important;
}

.file-container {
  height: 38px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 146px;

  :hover {
    cursor: pointer;
  }

  p {
    font-size: 16px;
    font-weight: 400;
    color: #c0c4c7;
  }

  input {
    opacity: 0;
    position: absolute;
    z-index: 2;
    width: 60px;
    height: 40px;
    bottom: 22px;
  }

  input:hover {
    cursor: pointer;
  }

  label {
    color: white;
    width: 183px;
    height: 44px;
    border-radius: 21.5px;
    background-color: #db202f;
    padding: 8px 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #38424c;
  }
}

.fileover {
  animation: shake 1s;
  animation-iteration-count: infinite;
  border-width: 3px;
}

.files-list {
  .single-file {
    display: flex;

    .delete {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    display: flex;
    flex-grow: 1;

    .name {
      font-size: 14px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
    }

    .info {
      display: flex;
      padding: 8px;
      border: 2px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
    }
  }
}

.send-button-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
