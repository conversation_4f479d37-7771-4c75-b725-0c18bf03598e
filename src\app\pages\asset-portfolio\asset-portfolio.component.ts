import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { TypeKey } from '@core/models/config';
import { AssetDropdownResponse } from '@core/models/response/asset-dropdown.response';
import { Asset, AssetsResponse } from '@core/models/response/assets.response';
import { AssetService } from '@core/services/asset.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxMaskPipe } from 'ngx-mask';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-asset-portfolio',
  templateUrl: './asset-portfolio.component.html',
  styleUrls: ['./asset-portfolio.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,

    NbButtonModule,
    TableModule,
    SkeletonModule,
    NgxMaskPipe,
  ],
})
export class AssetPortfolioComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  assets: Asset[] = [];
  statusData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  lenderData: any;
  facilityTypeData: any;
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private assetService: AssetService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private dialogService: NbDialogService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 50,
    } as Filters;

    this.getFacilityStatus();
    this.getFacilityType();
    this.getLenderOrg();

    if (this.sharedService.getFormParamValue?.tabId && this.sharedService.getFormParamValue?.changeTab) {
      this.editAsset({
        id: this.sharedService.getFormParamValue.assetKeyDataId,
      });
    }
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    this.assetService.getAssets(this.filterParams).subscribe(
      (data: AssetsResponse) => {
        if (data.success) {
          this.assets = data.payload.assets;
          this.totalRecords = data.payload.rows;
          this.dtTrigger.next(this.assets);
        } else {
          this.toast.danger(data.error.message, 'Oops!');
        }
        this.spinner.hide();
        this.loading = false;
      },
      () => {
        this.spinner.hide();
        this.loading = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  exportUser(): void {
    this.filterParams.export = true;
  }

  ngOnDestroy(): void { }

  editAsset(asset: any): void {
    this.sharedService.setFormParamValue({
      userId: this.sharedService.getUserIdValue.userId,
      assetKeyDataId: asset.id,
      changeTab: this.sharedService.getFormParamValue?.changeTab ? true : false,
      facility: this.sharedService.getFormParamValue?.facility,
      tabId: this.sharedService.getFormParamValue?.tabId ? this.sharedService.getFormParamValue?.tabId : 0,
    });
    this.router.navigate(['/asset/edit']);
  }

  createAsset(): void {
    this.sharedService.setFormParamValue({});
    this.router.navigate(['/asset/view']);
  }

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  private getLenderOrg(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_LenderOrg).subscribe((userData: AssetDropdownResponse) => {
      if (userData.success) {
        this.lenderData = userData.payload;
      }
    });
  }

  private getFacilityType(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_FacilityType).subscribe((userData: AssetDropdownResponse) => {
      if (userData.success) {
        this.facilityTypeData = userData.payload;
      }
    });
  }

  private getFacilityStatus(): void {
    this.assetService.getTypeBy(TypeKey.AssetManagement_FacilityStatus).subscribe((response: any) => {
      if (response.success) {
        this.statusData = response.payload;
      }
    });
  }

  assetUpdates(asset: any): void {
    this.sharedService.setFormParamValue({
      userId: this.sharedService.getUserIdValue.userId,
      assetKeyDataId: asset.id,
      changeTab: false,
    });
    this.router.navigate(['/asset/updates']);
  }

  isLender(): boolean {
    return this.sharedService.isLender();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isOriginatorManager(): boolean {
    return this.sharedService.isOriginatorManager();
  }

  archiveAssetConfirm(asset: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Archive Asset',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Archive Asset',
          yesButtonIcon: 'archive-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.archiveAsset(asset);
        }
      });
  }

  archiveAsset(asset: Asset): void {
    this.assetService
      .archiveAsset({
        assetId: asset.id,
        isDeleted: true,
      })
      .subscribe((response: any) => {
        if (response.success) {
          this.toast.success('Asset archived successfully.', 'Success');
          this.getList();
        } else {
          this.toast.danger(response.error.message, 'Oops!');
        }
      });
  }
}
