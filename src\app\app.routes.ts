import { Routes } from '@angular/router';
import { AuthGuard } from '@core/helpers';
import { AdminGuard } from '@core/helpers/admin.guard';
import { DashboardComponent } from '@pages/dashboard/dashboard.component';
import { DocumentsComponent } from '@pages/documents/documents.component';
import { StaffGuard } from './core/helpers/staff.guard';
import { LoginComponent } from './pages/auth/login/login.component';
import { InvestmentDashboardComponent } from './pages/investment-dashboard/investment-dashboard.component';
import { InvestmentDetailsComponent } from './pages/investment-details/investment-details.component';
import { InvestmentsComponent } from './pages/investments-list/investments.component';
import { InvestorDetailsComponent } from './pages/investor-details/investor-details.component';
import { InvestorPaymentsComponent } from './pages/investor-payments/investor-payments.component';
import { InvestorsComponent } from './pages/investors/investors.component';
import { UsersComponent } from './pages/users/users.component';
import { AuthLayoutComponent } from './shared/layouts/auth-layout/auth-layout.component';
import { LayoutComponent } from './shared/layouts/base-layout/base-layout.component';

export const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      {
        path: 'users',
        component: UsersComponent,
        canActivate: [AdminGuard],
      },
      {
        path: 'investors',
        component: InvestorsComponent,
        canActivate: [StaffGuard],
      },
      {
        path: 'investments',
        component: InvestmentsComponent,
      },
      {
        path: 'investment/view',
        component: InvestmentDetailsComponent,
      },
      {
        path: 'investor/investment-dashboard',
        component: InvestmentDashboardComponent,
      },
      {
        path: 'investor/edit',
        component: InvestorDetailsComponent,
        canActivate: [StaffGuard],
      },
      {
        path: 'investor/new',
        component: InvestorDetailsComponent,
        canActivate: [AdminGuard],
      },
      {
        path: 'documents',
        component: DocumentsComponent,
      },
      {
        path: 'payments',
        component: InvestorPaymentsComponent,
      },
      // Asset Management
      // {
      //   path: 'asset/dashboard',
      //   component: AssetDashboardComponent,
      // },
      // {
      //   path: 'asset/portfolio',
      //   component: AssetPortfolioComponent,
      // },
      // {
      //   path: 'asset/documents',
      //   component: LenderDocumentsComponent,
      // },
      // {
      //   path: 'asset/view',
      //   component: AssetDetailsComponent,
      // },
      // {
      //   path: 'asset/edit',
      //   component: AssetDetailsComponent,
      // },
      // {
      //   path: 'asset/updates',
      //   component: AssetUpdatesComponent,
      // },
      // {
      //   path: 'asset/checklist',
      //   component: AssetChecklistComponent,
      // },
      // {
      //   path: 'asset/checklist-builder',
      //   component: AssetChecklistBuilderComponent,
      // },
      // {
      //   path: 'asset/activities',
      //   component: AssetActivitiesComponent,
      // },
    ],
    canActivate: [AuthGuard],
    // canActivate: [MsalGuard], // TODO: check later
  },

  {
    path: '',
    component: AuthLayoutComponent,
    children: [
      // { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
      {
        path: 'login',
        component: LoginComponent,
      },
    ],
  },
  { path: '**', redirectTo: '' },
];
