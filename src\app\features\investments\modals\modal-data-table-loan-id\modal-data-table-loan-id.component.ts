import { Component, inject, OnInit } from '@angular/core';
import { ModalWithDataTableComponent } from '@components/atoms/modal-with-data-table/modal-with-data-table.component';
import { BaseComponent } from '@core/models/base.component';
import { InvestmentService } from '@core/services/investment.service';
import { Filters } from '@core/services/shared.service';
import { NbDialogRef, NbToastrService } from '@nebular/theme';
import { finalize, takeUntil } from 'rxjs';

@Component({
  selector: 'app-modal-data-table-loan-id',
  imports: [ModalWithDataTableComponent],
  templateUrl: './modal-data-table-loan-id.component.html',
  styleUrl: './modal-data-table-loan-id.component.scss',
})
export class ModalDataTableLoanIdComponent extends BaseComponent implements OnInit {
  title = 'TMO Loan ID';
  payload: Filters = {};
  /**
   * Investment data
   */
  data: any[] = [];

  /**
   * Pagination state
   */
  currentPage = 0;
  pageSize = 10;
  totalRecords = 0;
  loading = false;

  investmentService = inject(InvestmentService);
  toast = inject(NbToastrService);
  dialogRef = inject(NbDialogRef<any>);

  ngOnInit(): void {
    this.initializeDefaultPayload();
    this.loadInvestments();
  }

  private initializeDefaultPayload(): void {
    this.currentPage = 0;
    this.payload = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      investmentId: 0,
    };
  }

  /**
   * Loads investment data from the API
   */
  loadInvestments(): void {
    // Skip if already loading
    if (this.loading) {
      return;
    }

    // Clear existing data and show loading state
    this.data = [];
    this.loading = true;

    this.investmentService
      .getInvestments(this.payload)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.loading = false; // Always clear loading state when finished
        }),
      )
      .subscribe({
        next: (response: any) => this.handleInvestmentsResponse(response),
        error: (error: any) => this.handleInvestmentsError(error),
      });
  }

  /**
   * Handles successful investment data response
   */
  private handleInvestmentsResponse(response: any): void {
    if (!response?.success) {
      this.handleInvestmentsError(new Error('API returned failure status'));
      return;
    }
    this.data = response.payload.investments || [];
    this.totalRecords = response.payload.rows || 0;
    console.log('data:', this.data);
    console.log('totalRecords:', this.totalRecords);
  }

  /**
   * Handles errors from investment data API
   */
  private handleInvestmentsError(error: any): void {
    this.data = [];
    this.toast.danger('Failed to load investments', 'Error');
    console.error('Error loading investments:', error);
  }

  onSave() {
    console.log('saved');
  }

  onClose() {
    console.log('closed');
    this.dialogRef.close(false);
  }
}
