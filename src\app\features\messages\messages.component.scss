.user-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  width: 100%;
}

nb-list-item :hover,
.selected {
  background: var(--color-mist);
  border-radius: 10px;
  cursor: pointer;
}

.list {
  overflow: auto;
  margin: 9px;
  max-height: calc(100vh - 392px);
}

.chat-back {
  background: #f7f7f7;
  border-radius: 20px;
  padding: 0px 15px;
}

.chat-user-list {
  padding: 0px 18px;
}

.chat-back,
.chat-user-list {
  transition: all 0.5s;
}
