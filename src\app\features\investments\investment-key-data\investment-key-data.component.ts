import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TypeKey } from '@core/models/config';
import { InvestmentDetailsResponse, KeyData } from '@core/models/response/investment-details.response';
import { InvestmentService } from '@core/services/investment.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbDatepickerModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbOptionModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxMaskDirective } from 'ngx-mask';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-investment-key-data',
  templateUrl: './investment-key-data.component.html',
  styleUrls: ['./investment-key-data.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbSelectModule,
    NbInputModule,
    NbFormFieldModule,
    NbDatepickerModule,
    NbAlertModule,
    NbOptionModule,
    NbSpinnerModule,
    NbButtonModule,
    NgxMaskDirective,
  ],
})
export class InvestmentKeyDataComponent implements OnInit {
  @Output() changeTab = new EventEmitter<boolean>();
  @Input() investmentDetails: InvestmentDetailsResponse | null = null;

  @Input() getFormParamValue: any;

  financialsForm: UntypedFormGroup;
  financialsData: KeyData | null = null;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';

  pattern = {
    X: {
      pattern: new RegExp('[#+(0-9)]'),
    },
    0: {
      pattern: new RegExp('[(0-9)]'),
    },
  };
  investmentId: any;
  statusData: any;
  investmentStateData: any;
  investmentTypeData: any;
  assetTypeData: any;
  isValidatingLoanId = false;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    protected cd: ChangeDetectorRef,
    public toastr: NbToastrService,
    private sharedService: SharedService,
    private investmentService: InvestmentService,
  ) {
    this.getFormParamValue = {};

    this.financialsForm = this.formBuilder.group({
      investmentId: 0,
      title: ['', Validators.required],
      borrower: ['', Validators.required],
      term: ['', Validators.required],
      startDate: ['', Validators.required],
      totalOpportunity: ['', Validators.required],
      lvr: ['', Validators.required],
      minInvestment: ['', Validators.required],
      investmentReturn: ['', Validators.required],
      assetType: ['', Validators.required],
      investmentType: ['', Validators.required],
      stateId: ['', Validators.required],
      suburb: ['', Validators.required],
      statusId: ['', Validators.required],
      loanId: [''], // TMO Loan ID field - optional for manual investments
    });

    if (this.sharedService.isAdmin()) {
      this.financialsForm.enable();
    } else {
      this.financialsForm.disable();
    }

    // Set up TMO Loan ID validation
    this.setupLoanIdValidation();
  }

  ngOnInit(): void {
    this.investmentId = this.sharedService.getFormParamValue.investmentId;

    // if (this.investmentId) {
    //   this.investmentService.getInvestmentDetail(this.investmentId).subscribe((data: any) => {
    //     if (data.success) {
    //       if (data.payload) {
    //         if (data.payload.startDate) {
    //           data.payload.startDate = new Date(data.payload.startDate);
    //         }
    //         this.financialsData = data.payload;
    //         this.financialsForm.patchValue(data.payload);
    //       }
    //     } else {
    //       this.toastr.danger(data.error.message, 'Error!');
    //       this.loading = false;
    //     }
    //   });
    // }

    this.getInvestmentState();
    this.getAdminInvestmentStatus();
  }

  private getInvestmentState(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.InvestmentState,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.investmentStateData = userData.payload;
        }
      });
  }
  private getAdminInvestmentStatus(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.AdminInvestmentStatus,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.statusData = userData.payload;
        }
      });
  }

  dateChange(event: any): void {
    this.financialsForm.patchValue({
      startDate: event,
    });
  }

  get f() {
    return this.financialsForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.financialsForm.invalid) {
      return;
    }

    this.loading = true;

    this.updateFinancials();
  }

  private updateFinancials(): void {
    const financialsData: KeyData = {
      investmentId: this.investmentId,
      title: this.financialsForm.value.title,
      borrower: this.financialsForm.value.borrower,
      term: this.financialsForm.value.term,
      startDate: this.financialsForm.value.startDate,
      totalOpportunity: this.financialsForm.value.totalOpportunity,
      lvr: this.financialsForm.value.lvr,
      minInvestment: this.financialsForm.value.minInvestment,
      investmentReturn: this.financialsForm.value.investmentReturn,
      assetTypeId: this.financialsForm.value.assetTypeId,
      investmentTypeId: this.financialsForm.value.investmentTypeId,
      stateId: this.financialsForm.value.stateId,
      suburb: this.financialsForm.value.suburb,
      statusId: this.financialsForm.value.statusId,
      assetType: this.financialsForm.value.assetType,
      investmentType: this.financialsForm.value.investmentType,
    };

    this.investmentService.saveKeyData(financialsData).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            this.loading = false;
            this.toastr.success('Saved Successfully', 'Success!');
            if (!this.financialsData) {
              this.changeTab.emit(true);
            }
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  private setupLoanIdValidation(): void {
    // Set up debounced validation for TMO Loan ID
    this.financialsForm
      .get('loanId')
      ?.valueChanges.pipe(
        debounceTime(500), // Wait 500ms after user stops typing
        distinctUntilChanged(), // Only emit when value actually changes
      )
      .subscribe((value: any) => {
        if (value && typeof value === 'string' && value.trim()) {
          this.validateTmoLoanId(value.trim());
        } else {
          // Clear any existing errors when field is empty
          this.financialsForm.get('loanId')?.setErrors(null);
          this.isValidatingLoanId = false;
        }
      });
  }

  private validateTmoLoanId(loanId: string): void {
    if (this.isValidatingLoanId) {
      return; // Prevent multiple simultaneous validations
    }

    this.isValidatingLoanId = true;

    // Call the investment service to validate the TMO Loan ID
    this.investmentService.validateTmoLoanId(loanId, this.financialsForm.get('investmentId')?.value).subscribe(
      (response: any) => {
        this.isValidatingLoanId = false;

        if (response.success) {
          if (response.payload.isValid) {
            // TMO Loan ID is valid and not already linked
            this.financialsForm.get('loanId')?.setErrors(null);
          } else {
            // TMO Loan ID is either invalid or already linked
            this.financialsForm.get('loanId')?.setErrors({
              tmoLoanIdTaken: true,
              message: response.payload.message || 'This TMO Loan ID has already been linked to another investment.',
            });
          }
        } else {
          // API error - treat as invalid
          this.financialsForm.get('loanId')?.setErrors({
            tmoLoanIdInvalid: true,
            message: 'Unable to validate TMO Loan ID. Please try again.',
          });
        }
      },
      (error: any) => {
        this.isValidatingLoanId = false;
        console.error('Error validating TMO Loan ID:', error);

        // Set error state
        this.financialsForm.get('loanId')?.setErrors({
          tmoLoanIdError: true,
          message: 'Unable to validate TMO Loan ID. Please check your connection and try again.',
        });
      },
    );
  }

  backtoList(): void {
    this.router.navigate(['/users']);
  }
}
