@use "themes" as *;

nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

:host ::ng-deep .html-contant {
  p,
  ul,
  ul li {
    line-height: 30px;
  }
}

.file-container {
  width: 100%;
  padding: 0.5rem;
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #bfc6d0 !important;
  border-radius: 10px;
  height: 180px;

  p {
    font-size: 16px;
    line-height: 24px;
    color: #bfc6d0;
    text-align: center;
  }

  input {
    opacity: 0;
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  label {
    color: white;
    width: 183px;
    height: 44px;
    border-radius: 21.5px;
    background-color: #db202f;
    padding: 8px 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #38424c;
  }
}

.fileover {
  animation: shake 1s;
  animation-iteration-count: infinite;
  border-width: 3px;
}

.title {
  margin-top: 30px;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  color: #0a0a0a;
}

.image-block {
  padding: 3px;
}

.outer-circle,
.inner-circle {
  left: 13px !important;
}

.images-1,
.images-2,
.images-3,
.images-4,
.images-5,
.images-6 {
  position: relative;
  .full-icon {
    position: absolute;
    right: 12px;
    top: 12px;
    color: white;
    padding: 6px 7px;
    border-radius: 40px;
    nb-icon {
      border: 1px solid white;
      padding: 2px;
      margin: 3px;
      height: 25px;
      width: 25px;
      background: #00000094;
      border-radius: 24px;
    }
  }
  img {
    object-fit: cover;
    border-radius: 10px;
    width: 100%;
    padding: 3px 3px;
    border: 2px white;
    box-shadow: 0px 0px 1px 1px #e9ecef;
  }
  .image-block {
    position: relative;
  }
}

.images-1 {
  .image-block--0 {
    width: 100%;
    transition: all 0.5s;
  }
  .gallery__img--0 {
    width: 100%;
    height: 1120px;
  }
}

.images-2 {
  .image-block--0 .image-block--1 {
    width: 100%;
    transition: all 0.5s;
  }
  .gallery__img--0,
  .gallery__img--1 {
    width: 100%;
    height: 500px;
  }
}

.images-3 {
  .image-block--0,
  .image-block--1,
  .image-block--2 {
    width: 100%;
    transition: all 0.5s;
  }
  .gallery__img--0,
  .gallery__img--1,
  .gallery__img--2 {
    width: 100%;
    height: 360px;
  }
}

.images-4 {
  .image-block--0,
  .image-block--1,
  .image-block--2 {
    width: 100%;
    transition: all 0.5s;
  }
  .image-block--2,
  .image-block--3 {
    width: 50%;
    float: left;
    transition: all 0.5s;
  }
  .gallery__img--0,
  .gallery__img--1 {
    width: 100%;
    height: 360px;
  }
  .gallery__img--2,
  .gallery__img--3 {
    height: 240px;
  }
}

.images-5 {
  .image-block--0 {
    width: 100%;
    transition: all 0.5s;
  }

  .image-block--1,
  .image-block--2,
  .image-block--3,
  .image-block--4 {
    width: 50%;
    float: left;
    transition: all 0.5s;
  }
  .gallery__img--0 {
    width: 100%;
    height: 360px;
  }
  .gallery__img--1,
  .gallery__img--2,
  .gallery__img--3,
  .gallery__img--4 {
    height: 240px;
  }
}

.images-6 {
  .image-block--0,
  .image-block--1 {
    width: 100%;
    transition: all 0.5s;
  }
  .image-block--2,
  .image-block--3,
  .image-block--4 {
    width: 33.33%;
    float: left;
    transition: all 0.5s;
  }
  .gallery__img--0,
  .gallery__img--1 {
    width: 100%;
    height: 360px;
  }
  .gallery__img--2,
  .gallery__img--3,
  .gallery__img--4 {
    height: 240px;
  }
}

.editor-content {
  ::ng-deep {
    img {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
    }
  }
}
