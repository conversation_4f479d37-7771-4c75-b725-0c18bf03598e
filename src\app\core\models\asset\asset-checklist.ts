// Dynamic Checklist
export interface Originator {
  userId: number;
  contactName: string;
}

export interface ApprovalChecklistRow {
  contentType: string;
  contentOrder: number;
  contentTitle: string;
  loanManagerId?: number;
  reviewerId?: number;
  notApplicableId?: number;
  contentHtml: string;
}

export interface ChecklistTableHeader extends ApprovalChecklistRow {
  taskId: number;
  header: string;
  checklist_structure: ApprovalChecklistRow[];
  assetId: number;
}

export interface ChecklistTableItem extends ApprovalChecklistRow {
  taskId: number;
  checklistId: number;
  assetId: number;
}
