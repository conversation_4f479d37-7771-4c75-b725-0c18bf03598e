import { AfterViewChecked, ChangeDetector<PERSON><PERSON>, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { SharedService } from '@core/services/shared.service';
import { AssetDocumentsComponent } from '@features/lender/asset-document/asset-documents/asset-documents.component';

@Component({
  selector: 'app-lender-documents',
  templateUrl: './lender-documents.component.html',
  styleUrls: ['./lender-documents.component.scss'],
  standalone: true,
  imports: [AssetDocumentsComponent],
})
export class LenderDocumentsComponent implements OnInit, OnDestroy, AfterViewChecked {
  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  constructor(
    private sharedService: SharedService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.sharedService.setFormParamValue({});
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {}

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isLender(): boolean {
    return this.sharedService.isLender();
  }

  isOriginatorManager(): boolean {
    return this.sharedService.isOriginatorManager();
  }
}
