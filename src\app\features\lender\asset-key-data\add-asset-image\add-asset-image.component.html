<div [hidden]="uploadedDocuments">
  <div class="file-container" for="fileDropRef" appDnd (fileDropped)="onFileDropped($event)">
    <input type="hidden" />

    <input type="file" #fileDropRef id="fileDropRef" (change)="fileBrowseHandler($event)" accept="image/*" />

    <p class="m-0" for="fileDropRef">
      <nb-icon icon="file-add"></nb-icon>
      Drop Document here or Click to upload.
    </p>
  </div>

  <div *ngIf="submitted && !this.uploadedDocuments" class="invalid-feedback">
    <div *ngIf="!this.uploadedDocuments">image is required.</div>
  </div>
</div>

<div class="w-8/12 px-2" *ngIf="uploadedDocuments && uploadedDocuments.length > 0">
  <div class="files-list">
    <div class="single-file" *ngFor="let file of uploadedDocuments; let i = index">
      <div class="info">
        <div class="name">
          {{ file?.name }}
        </div>
        <div class="delete" (click)="clearDocuments()">
          <nb-icon class="file-delete" icon="close-circle"></nb-icon>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
    <nb-progress-bar style="width: 100%;" *ngIf="progress" [value]="progress" status="primary">
        Uploading {{progress}}%
    </nb-progress-bar>
</div> -->
