<div class="flex flex-wrap -mx-2">
  <button
    class="bg-velvet-700 hover:bg-velvet-600 text-white"
    *ngIf="!selectedLenderId && !createNew"
    nbButton
    status="default"
    type="button"
    (click)="createNewEntry()"
  >
    ADD NEW ENTRY
    <nb-icon icon="plus-outline"> </nb-icon>
  </button>
</div>
<form *ngIf="selectedLenderId || createNew" class="w-full px-2" [formGroup]="lenderUpdatesForm" (ngSubmit)="onSubmit()">
  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Update Title </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="updateTitle"
        status="{{ submitted && f.updateTitle.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.updateTitle.errors" class="invalid-feedback">
        <div *ngIf="f.updateTitle.errors.required">Update Title is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Update Publish Status </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="updatePublishStatusId"
        formControlName="updatePublishStatusId"
        status="{{ submitted && f.updatePublishStatusId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let status of updatePublishStatusData" [value]="status.id" [disabled]="status.disabled">
          {{ status.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.updatePublishStatusId.errors" class="invalid-feedback">
        <div *ngIf="f.updatePublishStatusId.errors.required">Update Publish Status is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Facility Limit </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        prefix="$"
        mask="separator"
        thousandSeparator=","
        placeholder=""
        maxlength="12"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="facilityLimit"
        status="{{ submitted && f.facilityLimit.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.facilityLimit.errors" class="invalid-feedback">
        <div *ngIf="f.facilityLimit.errors.required">Facility Limit is required.</div>
        <div *ngIf="f.facilityLimit.errors.mask">Invalid Facility Limit format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Drawn Balance </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        prefix="$"
        mask="separator"
        thousandSeparator=","
        placeholder=""
        maxlength="12"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="drawnBalance"
        status="{{ submitted && f.drawnBalance.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.drawnBalance.errors" class="invalid-feedback">
        <div *ngIf="f.drawnBalance.errors.required">Drawn Balance is required.</div>
        <div *ngIf="f.drawnBalance.errors.mask">Invalid Drawn Balance format.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Term (Months) </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="0*"
        placeholder=""
        maxlength="3"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="term"
        status="{{ submitted && f.term.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.term.errors" class="invalid-feedback">
        <div *ngIf="f.term.errors.required">Term is required.</div>
        <div *ngIf="f.term.errors.mask">Invalid Term format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong for="inStore">Financial Close</strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <nb-form-field>
        <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>

        <input
          type="text"
          shape="semi-round"
          nbInput
          fieldSize="large"
          fullWidth
          rInputMask="99/99/9999"
          id="financialClose"
          name="financialClose"
          formControlName="financialClose"
          required
          [nbDatepicker]="dateTimePickerFinancial"
          status="{{ submitted && f.financialClose.errors ? 'danger' : 'basic' }}"
        />
      </nb-form-field>
      <nb-datepicker #dateTimePickerFinancial (dateChange)="dateChange($event)"></nb-datepicker>
      <div *ngIf="submitted && f.financialClose.errors" class="invalid-feedback">
        <div *ngIf="f.financialClose.errors.required">Financial Close Date is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong for="inStore">Maturity Date</strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <nb-form-field>
        <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>

        <input
          type="text"
          shape="semi-round"
          nbInput
          fieldSize="large"
          fullWidth
          rInputMask="99/99/9999"
          id="maturityDate"
          name="maturityDate"
          formControlName="maturityDate"
          required
          [nbDatepicker]="dateTimePickerMaturity"
          status="{{ submitted && f.maturityDate.errors ? 'danger' : 'basic' }}"
        />
      </nb-form-field>
      <nb-datepicker #dateTimePickerMaturity (dateChange)="dateChange($event)"></nb-datepicker>
      <div *ngIf="submitted && f.maturityDate.errors" class="invalid-feedback">
        <div *ngIf="f.maturityDate.errors.required">Maturity Date is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong for="inStore">Repayment Date</strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <nb-form-field>
        <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>

        <input
          type="text"
          shape="semi-round"
          nbInput
          fieldSize="large"
          fullWidth
          rInputMask="99/99/9999"
          id="repaymentDate"
          name="repaymentDate"
          formControlName="repaymentDate"
          required
          [nbDatepicker]="dateTimePickerRepayment"
          status="{{ submitted && f.repaymentDate.errors ? 'danger' : 'basic' }}"
        />
      </nb-form-field>
      <nb-datepicker #dateTimePickerRepayment (dateChange)="dateChange($event)"></nb-datepicker>
      <div *ngIf="submitted && f.repaymentDate.errors" class="invalid-feedback">
        <div *ngIf="f.repaymentDate.errors.required">Repayment Date is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> LVR Limit </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="separator.2"
        separatorLimit="10"
        [validation]="true"
        suffix="%"
        placeholder="00.00%"
        maxlength="6"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="lvrLimit"
        status="{{ submitted && f.lvrLimit.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.lvrLimit.errors" class="invalid-feedback">
        <div *ngIf="f.lvrLimit.errors.required">LVR Limit is required.</div>
        <div *ngIf="f.lvrLimit.errors.mask">Invalid LVR Limit format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> LTC </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="separator.2"
        separatorLimit="10"
        suffix="%"
        [validation]="true"
        placeholder="00.00%"
        maxlength="6"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="ltc"
        status="{{ submitted && f.ltc.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.ltc.errors" class="invalid-feedback">
        <div *ngIf="f.ltc.errors.required">LTC is required.</div>
        <div *ngIf="f.ltc.errors.mask">Invalid LTC format.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> LVR Actual </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="separator.2"
        separatorLimit="10"
        suffix="%"
        [validation]="true"
        placeholder="00.00%"
        maxlength="6"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="lvrActual"
        status="{{ submitted && f.lvrActual.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.lvrActual.errors" class="invalid-feedback">
        <div *ngIf="f.lvrActual.errors.required">LVR Actual is required.</div>
        <div *ngIf="f.lvrActual.errors.mask">Invalid LVR Actual format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Current Loan Status </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

      <nb-select
        fullWidth
        size="large"
        shape="semi-round"
        status="basic"
        name="currentLoanStatusId"
        formControlName="currentLoanStatusId"
        status="{{ submitted && f.currentLoanStatusId.errors ? 'danger' : 'basic' }}"
      >
        <nb-option *ngFor="let status of currentLoanStatusData" [value]="status.id" [disabled]="status.disabled">
          {{ status.name }}
        </nb-option>
      </nb-select>
      <div *ngIf="submitted && f.currentLoanStatusId.errors" class="invalid-feedback">
        <div *ngIf="f.currentLoanStatusId.errors.required">Current Loan Status is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label>
        <strong for="inStore">Reporting Month</strong>
        <strong class="text-lime required"> &nbsp; * </strong></label
      >
      <p-calendar
        formControlName="reportingMonth"
        view="month"
        dateFormat="mm/yy"
        [inputStyle]="{
          'border-radius': '0.75rem',
          'font-size': '0.9375rem',
          'font-weight': 'normal',
          'line-height': '1.5rem',
          padding: '0.6875rem 1rem',
          'background-color': '#ffffff',
          'border-color': '#a6b2ae',
          color: '#576460',
        }"
        inputStyleClass="calendarInput"
        [ngClass]="submitted && f.reportingMonth.errors ? 'calendarModal invalidCalendar' : 'calendarModal'"
        [readonlyInput]="true"
        [required]="true"
        inputId="monthpicker"
      >
      </p-calendar>
      <div *ngIf="submitted && f.reportingMonth.errors" class="invalid-feedback">
        <div *ngIf="f.reportingMonth.errors.required">Reporting Month is required.</div>
      </div>
    </div>
  </div>
  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <div class="my-[15px]">
        <input
          nbInput
          placeholder="Loan Management Update"
          shape="semi-round"
          type="text"
          fieldSize="large"
          formControlName="loanManagementUpdateLabel"
          status="{{ submitted && f.loanManagementUpdateLabel.errors ? 'danger' : 'basic' }}"
          style="width: 100%"
        />
        <div *ngIf="submitted && f.loanManagementUpdateLabel.errors" class="invalid-feedback">
          <div *ngIf="f.loanManagementUpdateLabel.errors.required">Loan Management Update Title is required.</div>
        </div>
      </div>
      <app-ckeditor formControlName="loanManagementUpdate"> </app-ckeditor>
      <div *ngIf="submitted && f.loanManagementUpdate.errors" class="invalid-feedback">
        <div *ngIf="f.loanManagementUpdate.errors.required">Loan Management Update is required.</div>
      </div>
    </div>
    <!--    [(data)]="financialsData.loanManagementUpdate" -->
  </div>

  <div class="w-full px-2 my-[15px]">
    <button
      type="submit"
      class="float-right"
      [nbSpinner]="loading"
      nbButton
      status="primary"
      style="min-width: 135px; margin-left: 12px"
    >
      <span *ngIf="createNew"> ADD NEW </span>
      <span *ngIf="!createNew"> UPDATE </span>
    </button>
    &nbsp;
    <button
      type="button"
      class="float-right"
      *ngIf="selectedLenderId || createNew"
      nbButton
      type="button"
      (click)="cancelEdit()"
    >
      Cancel
    </button>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>
<div class="flex flex-wrap -mx-2">
  <div class="w-full px-2">
    <app-asset-lenders
      *ngIf="!selectedLenderId && !createNew"
      [assetKeyDataId]="assetKeyDataId"
      (lenderSelect)="editLender($event)"
      (latestLender)="getLatestLender($event)"
    >
    </app-asset-lenders>
  </div>
</div>
