// base.component.ts
import { Component, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';

@Component({
  template: '',
})
export abstract class BaseComponent implements OnDestroy {
  /**
   * Subject that emits when the component is destroyed
   * Use this for takeUntil operator to automatically unsubscribe from observables
   */
  protected readonly destroy$ = new Subject<void>();

  /**
   * Array to store subscriptions for manual cleanup
   */
  private subscriptions: (() => void)[] = [];

  /**
   * Add a cleanup function that will be called on component destroy
   * @param cleanupFn Function to execute on destroy
   */
  protected addCleanup(cleanupFn: () => void): void {
    this.subscriptions.push(cleanupFn);
  }

  /**
   * Add multiple cleanup functions
   * @param cleanupFns Array of functions to execute on destroy
   */
  protected addCleanups(...cleanupFns: (() => void)[]): void {
    this.subscriptions.push(...cleanupFns);
  }

  ngOnDestroy(): void {
    // Emit destroy signal for takeUntil operator
    this.destroy$.next();
    this.destroy$.complete();

    // Execute all registered cleanup functions
    this.subscriptions.forEach((cleanup) => {
      try {
        cleanup();
      } catch (error) {
        console.error('Error during component cleanup:', error);
      }
    });

    // Clear subscriptions array
    this.subscriptions = [];
  }
}

// Example usage in a component:

/*
// my-feature.component.ts
import { Component, OnInit } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { BaseComponent } from './base.component';
import { MyService } from './my.service';

@Component({
  selector: 'app-my-feature',
  template: `
    <div>{{ data }}</div>
  `
})
export class MyFeatureComponent extends BaseComponent implements OnInit {
  data: string = '';

  constructor(private myService: MyService) {
    super();
  }

  ngOnInit(): void {
    // Method 1: Using destroy$ with takeUntil (recommended for observables)
    this.myService.getData()
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.data = data;
      });

    // Method 2: Using addCleanup for manual cleanup
    const interval = setInterval(() => {
      console.log('Interval running');
    }, 1000);

    this.addCleanup(() => {
      clearInterval(interval);
      console.log('Interval cleared');
    });

    // Method 3: Adding multiple cleanups at once
    const timeout1 = setTimeout(() => console.log('Timeout 1'), 5000);
    const timeout2 = setTimeout(() => console.log('Timeout 2'), 10000);

    this.addCleanups(
      () => clearTimeout(timeout1),
      () => clearTimeout(timeout2),
      () => console.log('Component cleanup complete')
    );
  }
}
*/
