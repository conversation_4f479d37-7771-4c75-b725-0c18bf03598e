import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { NbToastrService, NbIconModule } from '@nebular/theme';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-add-asset-image',
  templateUrl: './add-asset-image.component.html',
  styleUrls: ['./add-asset-image.component.scss'],
  standalone: true,
  imports: [CommonModule, NbIconModule],
})
export class AddAssetImageComponent implements OnInit, AfterViewChecked {
  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;
  @Input() assetKeyDataId!: number | null;
  @Input() uploadedDocuments: any = null;
  @Output() uploadDocument = new EventEmitter<any>();
  loading = false;
  submitted = false;

  constructor(
    public toastr: NbToastrService,
    private cdr: ChangeDetectorRef,
    private toast: NbToastrService,
  ) {}

  ngOnInit(): void {}

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  clearDocuments(): void {
    this.uploadedDocuments = null;
    this.fileDropRef.nativeElement.value = '';
    this.loading = false;
    this.uploadDocument.emit(null);
    this.cdr.detectChanges();
  }

  onSubmit(): void {
    this.submitted = true;
    this.loading = true;
    this.prepareFilesList(this.uploadedDocuments);
    this.cdr.detectChanges();
  }

  onFileDropped(event: any): void {
    if (event && event.length > 1) {
      this.toast.danger('Multiple files upload not supported, Please drag single file only', 'Upload Error!');
      return;
    }
    this.uploadedDocuments = event;
    this.prepareFilesList(this.uploadedDocuments);
  }

  fileBrowseHandler(event: any): void {
    this.uploadedDocuments = event.target.files;
    this.prepareFilesList(this.uploadedDocuments);
  }

  prepareFilesList(files: any[]): void {
    // const extensions = ['png', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'doc', 'docx', 'odt'];
    const allowedExtensions = /(\.jpg|\.jpeg|\.png)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toast.danger('File type not supported', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = null;
          this.loading = false;
          this.submitted = false;
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toast.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = null;
          this.submitted = false;
          this.loading = false;
          return;
        }
      }
    }
    this.uploadDocument.emit(files);
  }
}
