export interface SaveLenderDataRequest {
  id?: number;
  assetId?: number;
  updateTitle: string;
  updatePublishStatusId: number;
  facilityLimit: number;
  drawnBalance: number;
  term: number;
  financialClose: Date;
  maturityDate: Date;
  repaymentDate: Date;
  lvrLimit: number;
  ltc: number;
  lvrActual: number;
  currentLoanStatusId: number;
  reportingMonth: Date;
  loanManagementUpdate: string;
  loanManagementUpdateLabel: string;
  upcomingReporting: string;
  upcomingReportingLabel: string;
  userId?: number;
}
