@use "themes" as *;

:host {
  .title h5,
  .chart-title {
    text-decoration-line: underline;
    text-underline-offset: 10px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }

  .btn-success {
    background-color: #4db664 !important;
    border-color: #4db664 !important;
    border-radius: 0.25rem;
    color: rgba(255, 255, 255, 1) !important;
    min-width: 63px;
    min-height: 27px;
    font-family: "Roboto";
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
  }

  .btn-light {
    background-color: #e4e9f2 !important;
    border-color: #e4e9f2 !important;
    border-radius: 0.25rem;
    color: #222b45 !important;
    min-width: 63px;
    min-height: 27px;
    font-family: "Roboto";
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
  }
}

.chart-title {
  font-size: 18px;
  line-height: 30px;
}

.text-right {
  text-align: right;
}

.float-right {
  margin-left: 12px;
}

.items-rows {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.items-rows nb-select {
  // margin-right: 10px;
  width: 184px;
}

.items-rows div {
  margin-right: 5px;
}

.items-rows div {
  margin-bottom: 8px;
}

:host ::ng-deep {
  // ::-webkit-scrollbar {
  //     width: 0px !important;
  //     background: transparent !important;
  //     /* make scrollbar transparent */
  // }

  // .p-datatable .p-datatable-tbody>tr>td {
  //     height: 78px !important;
  // }
  .negateMargin {
    margin: -15px 0px !important;
  }
}

.wrap-text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 300px !important;
}

.overdue {
  background-color: #ff5c5c1a !important;
  color: #ff5c5c !important;
}

.completed {
  background-color: #f5f5f5 !important;
}

::ng-deep {
  [dir="ltr"] .nb-form-field-control-with-prefix nb-select.appearance-outline.size-medium .select-button {
    padding-left: 35px !important;
  }

  [dir="ltr"] .nb-form-field-prefix-medium {
    margin-right: -35px !important;
    width: 35px !important;
  }
}

.files-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 5px 0px;
  padding-left: 5px;
  padding-right: 10px;

  .single-file {
    display: flex;

    .delete {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .attachment {
      display: flex;
      margin-left: 0.5rem;
      cursor: pointer;
      align-self: flex-start;
    }

    .name {
      font-size: 12px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
    }

    .size {
      font-size: 12px;
      font-weight: 500;
      color: #a4a4a4;
      margin: 0;
      margin-bottom: 0.25rem;
    }

    .info {
      display: flex;
      padding: 8px;
      // padding-left: 3px;
      // padding-right: 3px;
      border: 1px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
      margin: 4px 3px;
      cursor: pointer;
    }
  }
}
