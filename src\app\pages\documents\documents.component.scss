@use "themes" as *;

:host {
  .title h5,
  .chart-title {
    text-decoration-line: underline;
    text-underline-offset: 5px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }
}

.chart-title {
  font-size: 18px;
  line-height: 30px;
}

.text-right {
  text-align: right;
}

.float-right {
  margin-left: 12px;
}

.items-rows {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}
.items-rows nb-select {
  // margin-right: 10px;
  min-width: 200px;
}

.items-rows div {
  margin-right: 5px;
}

.items-rows div {
  margin-bottom: 8px;
}
.user-status {
  border-radius: 5px;
  padding: 5px 20px;
  color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.user-status.Active {
  background: #4db6ac;
}

.user-status.Disabled,
.user-status.Inactive {
  background: #90a4ae;
}

.user-status.New,
.user-status.Pending {
  background: #ffbc35;
}

.user-status.Archived {
  background: #3e4550;
}
