import { UserRole } from '@core/helpers';

export interface GoogleUserResponse {
  id: string;
  idToken: string;
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  photoUrl: string;
  provider: string;
}

export interface ExternalLoginResponse {
  isSuccess: boolean;
  isLocked: boolean;
  requireVerify: boolean;
  token: string;
  renewToken: string;
  expDate: string;
  orgList: OrgList[];
  permissions: any[];
  orgId: number;
  otp: number;
  redirectToHome: boolean;
  otpMessage: any;
  redirectResetPassword: boolean;
  isLoggedIn: boolean;
  isAdmin: boolean;

  role?: UserRole;
}

export interface OrgList {
  departments: any[];
  roles: any[];
  orgId: number;
  orgKey: string;
  orgName: string;
  orgUrl: string;
}

export interface UserParamsResponse {
  name: string;
  investmentId: number;
  investorId: number;
  chatUserId: number;
  userId: number;
}
