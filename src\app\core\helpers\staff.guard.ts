import { Injectable, inject } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot, CanActivateFn } from '@angular/router';
import { AuthenticationService } from '@core/services/authentication.service';
import { SharedService } from '@core/services/shared.service';

@Injectable({ providedIn: 'root' })
export class StaffGuardService {
  constructor(
    private router: Router,
    private sharedService: SharedService,
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (this.sharedService.isAdmin() || this.sharedService.isManager()) {
      return true;
    }

    // not logged in so redirect to login page with the return url
    this.router.navigate(['/dashboard']);
    return false;
  }
}

// New functional guard approach for Angular 16
export const StaffGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean => {
  return inject(StaffGuardService).canActivate(route, state);
};
