import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetector<PERSON>ef, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { AuthenticationService } from '@core/services/authentication.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { UserManagementService } from '@core/services/user-management.service';
import { AddUserComponent } from '@features/user-management/add-user/add-user.component';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    NbIconModule,
    NbCardModule,
    NbButtonModule,
    SkeletonModule,
    TableModule,
  ],
})
export class UsersComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  users: any[] = [];
  statusData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private authenticationService: AuthenticationService,
    private sharedService: SharedService,
    private userManagementService: UserManagementService,
    private dialogService: NbDialogService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
      userTypeId: 2, // Staff users only
    } as Filters;

    await this.getStatus();
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    this.userManagementService.getUsers(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.users = (data as any).payload.users;
        this.totalRecords = (data as any).payload.rows;
        this.dtTrigger.next(this.users);
        this.spinner.hide();
        this.loading = false;
      }
    });

    this.userManagementService.getRoleForAdmin().subscribe((roleData: any) => {
      if (roleData.success) {
        this.roleData = roleData.payload;
        this.roleData = this.roleData.filter((x: any) => x.id !== 3);
      }
    });
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  exportUser(): void {
    this.filterParams.export = true;
  }

  public async getStatus(): Promise<void> {
    this.statusData = (await this.userManagementService.getUserStatusAdmin().toPromise()).payload;
  }

  ngOnDestroy(): void {}

  editUser(user: any): void {
    this.dialogService
      .open(AddUserComponent, {
        context: {
          getFormParamValue: user,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.toast.success('User Updated Successfully.', 'Success!');
          this.getList();
        }
      });
  }

  addNewUser(): void {
    this.dialogService
      .open(AddUserComponent, {
        context: {
          getFormParamValue: {},
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.toast.success('User Added Successfully.', 'Success!');
          this.getList();
        }
      });
  }

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  deleteUserConfirm(user: any): void {
    // if (user.accreditationCount === 0 && user.dealCount === 0) {
    this.confirmArchive(user);
    // } else {
    // this.confirmDisable(user);
    // }
  }

  confirmArchive(user: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Archive User',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Archive User',
          yesButtonIcon: 'archive-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.deleteUser(user);
        }
      });
  }

  confirmDisable(user: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Disable User',
          message: 'Are you sure you want to disable this user?',
          yesButton: 'Disable User',
          yesButtonIcon: 'lock-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.updateStatus(user, 3); // Disable user
        }
      });
  }

  activeUser(user: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Activate User',
          message: 'Are you sure you want to activate this user?',
          yesButton: 'Activate User',
          yesButtonIcon: 'unlock-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.updateStatus(user, 1); // Active user
        }
      });
  }

  updateStatus(user: any, statusId: number): void {
    this.userManagementService
      .updateStatus({
        // orgId: user.orgId,
        userStatusList: [
          {
            userId: user.userId,
            statusId,
          },
        ],
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.toast.success('Status Updated successfully.', 'Success!');
          this.getList();
        } else {
          this.toast.danger(data.error.message, 'Error!');
        }
      });
  }

  deleteUser(user: any): void {
    this.userManagementService
      .archiveUser({
        userId: user.userId,
        orgId: user.orgId,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.toast.success('User Archive Successfully.', 'Success!');
          this.getList();
        } else {
          this.toast.danger(data.error.message, 'Error!');
        }
      });
  }

  copyLink(user: any): void {
    this.userManagementService
      .copyLink({
        userId: user.userId,
        host: window.location.host,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.copyToClipboard(data.payload.link);
          this.toast.success(null, 'Link copied to clipboard');
        } else {
          this.toast.danger(data.error.message, 'Error!');
        }
      });
  }

  copyToClipboard(val: string): void {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
  }

  hideActions(): boolean {
    return this.eventFilters && this.eventFilters.statusId && this.eventFilters.statusId.value === 4;
  }
}
