<div class="flex flex-wrap -mx-2 my-[15px]">
  <div class="md:w-full px-2" style="margin: auto">
    <div class="title">
      <h5>Activities</h5>
    </div>
  </div>
</div>
<div class="flex flex-wrap -mx-2">
  <div class="lg:w-10/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-form-field>
        <nb-icon nbPrefix icon="calendar-outline" pack="eva"></nb-icon>
        <nb-select
          class="hide-dropdown-icon"
          fullWidth
          placeholder="Select Period"
          name="period"
          id="value"
          (selectedChange)="dt.filter($event, 'dueDate', 'equals')"
          [(ngModel)]="selectedDate"
        >
          <nb-option *ngFor="let dateFilter of dateFilterData" [value]="dateFilter.value">
            {{ dateFilter.name }}
          </nb-option>
        </nb-select>
      </nb-form-field>
    </div>
    <div>
      <nb-select
        fullWidth
        placeholder="Facility Name"
        name="facilityName"
        id="value"
        (selectedChange)="dt.filter($event, 'facility', 'equals')"
        [(ngModel)]="selectedFacility"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let asset of facilityList" [value]="asset.facilityName">
          {{ asset.facilityName }}
        </nb-option>
      </nb-select>
    </div>
    <div>
      <nb-select
        fullWidth
        placeholder="Assigned to"
        name="assignedTo"
        id="value"
        (selectedChange)="dt.filter($event, 'assignedUserId', 'equals')"
        [(ngModel)]="selectedUser"
      >
        <nb-option value="All">All</nb-option>
        <nb-option *ngFor="let user of originatorUsers" [value]="user">
          {{ user.contactName }}
        </nb-option>
      </nb-select>
    </div>
    <div>
      <nb-select
        fullWidth
        placeholder="Status"
        id="value"
        [(ngModel)]="selectedStatus"
        (selectedChange)="dt.filter($event, 'taskStatus', 'equals')"
      >
        <nb-option *ngFor="let status of statusOptions" [value]="status">
          {{ status.name }}
        </nb-option>
      </nb-select>
    </div>
    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input
          type="text"
          fullWidth
          placeholder="Search"
          (input)="filterGlobal($event)"
          nbInput
          [(ngModel)]="searchTerm"
        />
      </nb-form-field>
    </div>
  </div>
  <div class="lg:w-2/12 px-2 my-[15px]">
    <button class="float-right" nbButton status="primary" (click)="editTask(null)">
      <nb-icon icon="plus-outline"></nb-icon> ADD NEW TASK
    </button>
  </div>
</div>

<nb-card>
  <nb-card-body>
    <p-table
      #dt
      [filterDelay]="700"
      [value]="activities"
      [lazy]="true"
      (onLazyLoad)="nextPage($event)"
      [paginator]="true"
      [rows]="50"
      [totalRecords]="totalRecords"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
      [rowsPerPageOptions]="[10, 25, 50]"
      [scrollable]="true"
      scrollWidth="flex"
      scrollHeight="flex"
      [style]="{ width: '100%' }"
      [globalFilterFields]="['id', 'taskTitle', 'facilityName', 'assignedTo']"
      sortField="dueDate"
      [sortOrder]="1"
      [loading]="loading"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="max-width: 100px" [pSortableColumn]="'id'">
            <div>
              <div>ID</div>
              <p-sortIcon [field]="'id'"></p-sortIcon>
            </div>
          </th>
          <th style="min-width: 400px" [pSortableColumn]="'taskTitle'">
            <div>
              <div>Title & Description</div>
              <p-sortIcon [field]="'taskTitle'"> </p-sortIcon>
            </div>
          </th>
          <th style="max-width: 300px" [pSortableColumn]="'facilityName'">
            <div>
              <div>Facility Name</div>
              <p-sortIcon [field]="'facilityName'"> </p-sortIcon>
            </div>
          </th>
          <th style="max-width: 200px" [pSortableColumn]="'assignedTo'">
            <div>
              <div>Assigned to</div>
              <p-sortIcon [field]="'assignedTo'"> </p-sortIcon>
            </div>
          </th>
          <th style="max-width: 200px" [pSortableColumn]="'dueDate'">
            <div>
              <div>Due Date</div>
              <p-sortIcon [field]="'dueDate'"> </p-sortIcon>
            </div>
          </th>
          <th style="max-width: 100px">
            <div>Action</div>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-index="rowIndex">
        <tr [ngClass]="getRowClass(rowData)">
          <td style="max-width: 100px">{{ rowData.id }}</td>
          <td style="min-width: 400px">
            <div [ngSwitch]="rowData.taskType !== 3">
              <div *ngSwitchCase="false">
                <div class="flex flex-wrap -mx-2">
                  <div class="w-full px-2">
                    <b>{{ rowData.taskTitle }}</b>
                  </div>
                  <div
                    class="w-full px-2 negateMargin"
                    [innerHTML]="sanitizer.bypassSecurityTrustHtml(rowData.taskContent)"
                  ></div>
                  <div class="w-full px-2">
                    <div class="files-list">
                      <div class="single-file" *ngFor="let file of rowData.noteDocuments">
                        <div class="info w-full px-2" (click)="downloadFile(file)">
                          <div class="attachment">
                            <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
                          </div>
                          <div class="name">
                            {{ file?.documentName }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="true">
                <div class="w-full px-2">
                  <div class="flex flex-wrap -mx-2" (click)="editTask(rowData)">
                    <div style="margin-top: 8px">
                      <nb-icon icon="covenantTabIcon" pack="custom" style="cursor: pointer; width: 40px; height: 40px">
                      </nb-icon>
                    </div>
                    <div style="margin-top: 15px">
                      <b style="color: #002c24; cursor: pointer">{{ rowData.taskTitle }}</b>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td style="max-width: 300px">
            <div class="wrap-text-ellipsis">
              {{ rowData.facilityName }}
            </div>
          </td>
          <td style="max-width: 200px">
            <div class="wrap-text-ellipsis">
              {{ rowData.contactName }}
            </div>
          </td>
          <td style="max-width: 200px">{{ rowData.dueDate | date: "dd/MM/YYYY" }}</td>
          <td style="max-width: 100px">
            <div [ngSwitch]="rowData.completedBy > 0">
              <div *ngSwitchCase="false" style="height: 40px; width: 40px; padding: 6px 0px">
                <nb-icon
                  [ngStyle]="{ visibility: rowData.taskType !== 3 ? 'hidden' : 'visible' }"
                  icon="checkBoxIcon"
                  pack="custom"
                  (click)="toggleTaskCompletion(activities[index])"
                  style="cursor: pointer; width: 32px; height: 32px"
                >
                </nb-icon>
              </div>
              <div *ngSwitchCase="true" style="height: 40px; width: 40px; padding: 11px 0px">
                <nb-user
                  [ngStyle]="{ visibility: rowData.taskType !== 3 ? 'hidden' : 'visible' }"
                  style="cursor: pointer"
                  color="#002c24"
                  [name]="rowData.completedByName"
                  status="success"
                  [showName]="false"
                  [showTitle]="false"
                  nbTooltip="{{ rowData.completedByName }}"
                  nbTooltipPlacement="left"
                  size="small"
                  (click)="resetTaskCompletion(rowData)"
                >
                </nb-user>
              </div>
            </div>
            <button
              *ngIf="isAdmin() || userId === rowData.assignedTo || userId === rowData.createdBy"
              nbButton
              ghost
              shape="round"
              status="default"
              class="button-icon"
              (click)="editTask(rowData)"
              nbTooltip="Edit Task"
              nbTooltipStatus="control"
              nbTooltipPlacement="bottom"
            >
              <nb-icon icon="covenantEditPencilIcon" pack="custom"></nb-icon>
            </button>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage" let-columns>
        <tr>
          <td style="text-align: center; display: block" *ngIf="!eventFilters" [attr.colspan]="10">
            No activities found
          </td>
          <td style="text-align: center; display: block" *ngIf="eventFilters" [attr.colspan]="10">
            Sorry, your search did not return any matching results. Please try again
          </td>
        </tr>
      </ng-template>
    </p-table>
  </nb-card-body>
</nb-card>
