<form [formGroup]="investmentForm" (ngSubmit)="onSubmit()" autocomplete="off">
  <div class="sm:w-full px-2">
    <div class="flex flex-wrap -mx-2" *ngIf="error">
      <div class="my-[15px]">
        <nb-alert accent="danger">{{ error }}</nb-alert>
      </div>
    </div>

    <div formArrayName="investments">
      <div *ngFor="let investment of investments.controls; let i = index">
        <div [formGroupName]="i">
          <div
            class="flex flex-wrap -mx-2"
            *ngIf="i !== 0"
            style="border-top: 1px solid #e9ecef; margin: 14px 0px"
          ></div>

          <div class="flex flex-wrap -mx-2 array-row">
            <label class="sm:w-8/12 px-2 w-8/12 px-2">
              <strong class="{{ 'investment' + i }}"
                >{{ i + 1 }}. Investment <span *ngIf="getValue(i, 'id')">(ID {{ getValue(i, "id") }}) </span>:
              </strong>
            </label>

            <div class="sm:w-4/12 px-2 w-8/12 px-2">
              <button
                type="button"
                class="float-right"
                shape="semi-round"
                ghost
                nbButton
                (click)="removeInvestments(investment, i)"
              >
                <nb-icon icon="trash-2-outline"></nb-icon>
              </button>
            </div>

            <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
              <label>
                <strong for="inStore">Investment Title</strong>
                <strong class="text-lime required"> &nbsp; * </strong></label
              >

              <nb-select
                fullWidth
                size="large"
                shape="semi-round"
                status="basic"
                name="investmentId"
                formControlName="investmentId"
              >
                <nb-option
                  *ngFor="let entityType of investmentLookup"
                  [value]="entityType.id"
                  [disabled]="entityType.disabled"
                >
                  {{ entityType.title }}
                </nb-option>
              </nb-select>
              <div
                *ngIf="getInvestmentsValidity(i, 'investmentId') === 'danger'"
                class="invalid-feedback caption status-danger"
              >
                Investment Title is Required.
              </div>
            </div>

            <div class="sm:w-6/12 px-2 my-[15px] w-full px-2">
              <label>
                <strong for="inStore">Investment Amount</strong>
                <strong class="text-lime required"> &nbsp; * </strong></label
              >
              <input
                type="text"
                shape="semi-round"
                prefix="$"
                mask="separator"
                thousandSeparator=","
                nbInput
                fieldSize="large"
                fullWidth
                id="investmentAmount{{ i }}"
                name="investmentAmount"
                formControlName="investmentAmount"
                required
                maxlength="12"
                [status]="getInvestmentsValidity(i, 'investmentAmount')"
              />
              <div
                *ngIf="getInvestmentsValidity(i, 'investmentAmount') === 'danger'"
                class="invalid-feedback caption status-danger"
              >
                Investment Amount is Required.
              </div>
            </div>

            <div class="sm:w-6/12 px-2 my-[15px] w-full px-2">
              <label> <strong for="inStore">Deposit Reference</strong></label>
              <input
                type="text"
                shape="semi-round"
                nbInput
                fieldSize="large"
                fullWidth
                id="depositReference{{ i }}"
                name="depositReference"
                formControlName="depositReference"
                [status]="getInvestmentsValidity(i, 'depositReference')"
              />
              <!-- <div *ngIf="getInvestmentsValidity(i, 'depositReference') == 'danger'"
                                class="invalid-feedback caption status-danger">
                                Deposit Reference is Required. </div> -->
            </div>

            <div class="sm:w-6/12 px-2 my-[15px] w-full px-2">
              <label>
                <strong for="inStore">Investment Date</strong>
                <strong class="text-lime required"> &nbsp; * </strong></label
              >
              <nb-form-field>
                <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>

                <input
                  type="text"
                  shape="semi-round"
                  nbInput
                  fieldSize="large"
                  fullWidth
                  rInputMask="99/99/9999"
                  id="investmentDate{{ i }}"
                  name="investmentDate"
                  formControlName="investmentDate"
                  required
                  [status]="getInvestmentsValidity(i, 'investmentDate')"
                  [nbDatepicker]="dateTimePicker"
                />
              </nb-form-field>
              <nb-datepicker #dateTimePicker (dateChange)="dateChange($event)"></nb-datepicker>

              <div
                *ngIf="getInvestmentsValidity(i, 'investmentDate') === 'danger'"
                class="invalid-feedback caption status-danger"
              >
                Investment Date is Required.
              </div>
            </div>

            <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
              <label><strong> Status </strong> <strong class="text-lime required"> &nbsp; * </strong></label>

              <nb-select
                fullWidth
                size="large"
                shape="semi-round"
                id="investmentStatusId{{ i }}"
                status="basic"
                name="investmentStatusId"
                formControlName="investmentStatusId"
              >
                <nb-option
                  *ngFor="let entityType of entityTypeData"
                  [value]="entityType.id"
                  [disabled]="entityType.disabled"
                >
                  {{ entityType.name }}
                </nb-option>
              </nb-select>
              <div
                *ngIf="getInvestmentsValidity(i, 'investmentStatusId') === 'danger'"
                class="invalid-feedback caption status-danger"
              >
                Status is Required.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-wrap -mx-2">
      <div class="w-6/12 px-2 my-[15px]">
        <button
          *ngIf="isAdmin()"
          class="bg-velvet-700 hover:bg-velvet-600 text-white"
          nbButton
          status="default"
          type="button"
          (click)="addInvestments()"
        >
          ADD NEW ENTRY
          <nb-icon icon="plus-outline"> </nb-icon>
        </button>
      </div>
      <div class="w-6/12 px-2 my-[15px]">
        <button
          [disabled]="!isAdmin()"
          *ngIf="investments.controls.length > 0"
          class="float-right"
          [nbSpinner]="loading"
          nbButton
          status="primary"
        >
          UPDATE
        </button>
      </div>
    </div>
  </div>
</form>
