import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// components
import * as theme from '@nebular/theme';
import { NbDateFnsDateModule } from '@nebular/date-fns';
import { NgxMaskDirective, NgxMaskPipe } from 'ngx-mask';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    theme.NbThemeModule.forRoot({ name: 'default' }),
    theme.NbLayoutModule,
    theme.NbSidebarModule.forRoot(),
    theme.NbMenuModule.forRoot(),
    theme.NbCardModule,
    theme.NbInputModule,
    theme.NbCheckboxModule,
    theme.NbButtonModule,
    theme.NbUserModule,
    theme.NbActionsModule,
    theme.NbContextMenuModule,
    theme.NbAlertModule,
    theme.NbSpinnerModule,
    theme.NbIconModule,
    theme.NbSelectModule,
    theme.NbToastrModule.forRoot(),
    theme.NbFormFieldModule,
    theme.NbPopoverModule,
    theme.NbTooltipModule,
    theme.NbDialogModule.forRoot(),
    theme.NbDatepickerModule.forRoot(),
    theme.NbToggleModule,
    theme.NbStepperModule,
    theme.NbRadioModule,
    NbDateFnsDateModule.forChild({ format: 'dd/MM/yyyy' }),
    NgxMaskDirective,
    NgxMaskPipe,
  ],
  declarations: [],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DynamicFormBuilderModule {}
