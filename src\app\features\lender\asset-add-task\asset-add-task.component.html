<form class="" [formGroup]="form">
  <nb-card>
    <nb-card-header>
      <div class="flex flex-wrap -mx-2">
        <h5 class="w-6/12 px-2">
          <div class="title">
            {{ edit ? "Edit Task" : "Add New Task" }}
          </div>
        </h5>
        <div class="w-6/12 px-2">
          <div class="popup-close float-right">
            <button ghost nbButton (click)="close()">
              <nb-icon icon="close"></nb-icon>
            </button>
          </div>
        </div>
      </div>
    </nb-card-header>
    <nb-card-body>
      <div
        [ngStyle]="{
          height: facilityList && facilityList.length > 0 ? '60vh' : '50vh',
        }"
        style="width: 800px; margin: 22px 50px"
      >
        <div class="w-full px-2 my-[15px]">
          <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
        </div>
        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong> Task Title </strong><strong class="text-lime required"> &nbsp; * </strong></label>
            <input
              nbInput
              fullWidth
              maxlength="200"
              type="text"
              fieldSize="large"
              formControlName="taskTitle"
              status="{{ submitted && f.taskTitle.errors ? 'danger' : 'basic' }}"
            />
            <div *ngIf="submitted && f.taskTitle.errors" class="invalid-feedback">
              <div *ngIf="f.taskTitle.errors.required">Title is required.</div>
            </div>
          </div>
        </div>
        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label>
              <strong>Due Date</strong>
              <strong class="text-lime required"> &nbsp; * </strong></label
            >
            <nb-form-field>
              <input
                type="text"
                nbInput
                fieldSize="large"
                fullWidth
                rInputMask="99/99/9999"
                id="dueDate"
                name="dueDate"
                formControlName="dueDate"
                required
                [nbDatepicker]="dateTimePicker"
                status="{{ submitted && f.dueDate.errors ? 'danger' : 'basic' }}"
              />
              <nb-icon nbSuffix icon="calendar-outline" pack="eva"></nb-icon>
            </nb-form-field>
            <nb-datepicker #dateTimePicker (dateChange)="dateChange($event)"></nb-datepicker>
            <div *ngIf="submitted && f.dueDate.errors" class="invalid-feedback">
              <div *ngIf="f.dueDate.errors.required">Due date is required.</div>
            </div>
          </div>
          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label>
              <strong> Assigned to </strong>
              <strong class="text-lime required"> &nbsp; * </strong>
            </label>
            <div class="scrollable-container">
              <nb-select
                fullWidth
                size="large"
                placeholder="Please Select"
                style="overflow: visible"
                status="{{ submitted && f.assignedTo.errors ? 'danger' : 'basic' }}"
                name="assignedTo"
                formControlName="assignedTo"
              >
                <nb-option *ngFor="let user of originatorUsers" [value]="user.userId">
                  {{ user.contactName }}
                </nb-option>
              </nb-select>
            </div>
            <div *ngIf="submitted && f.assignedTo.errors" class="invalid-feedback">
              <div *ngIf="f.assignedTo.errors">Assigned to user is required.</div>
            </div>
          </div>
        </div>
        <div class="flex flex-wrap -mx-2" *ngIf="facilityList && facilityList.length > 0">
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong>Facility Name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <nb-form-field>
              <input
                [(ngModel)]="selectedFacility"
                [disabled]="edit"
                (ngModelChange)="onFacilityChange($event)"
                nbInput
                type="text"
                placeholder="Search"
                fullWidth
                [nbAutocomplete]="autoNgModel"
                [ngModelOptions]="{ standalone: true }"
                status="{{ submitted && f.assetId.errors ? 'danger' : 'basic' }}"
              />
              <nb-autocomplete #autoNgModel (selectedChange)="onFacilitySelection($event)">
                <nb-option [disabled]="edit" *ngFor="let option of facilityOptions" [value]="option.facilityName">
                  {{ option.facilityName }}
                </nb-option>
              </nb-autocomplete>
              <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
            </nb-form-field>
            <div *ngIf="submitted && f.assetId.errors" class="invalid-feedback">
              <div *ngIf="f.assetId.errors.required">Facility is required.</div>
            </div>
          </div>
        </div>
        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong>Description </strong></label>
            <!-- <textarea nbInput maxlength="1000" fullWidth placeholder="Task Description"
                            formControlName="taskContent">
                            </textarea> -->
            <app-ckeditor formControlName="taskContent"> </app-ckeditor>
          </div>
        </div>
        <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
          <nb-progress-bar *ngIf="progress" style="width: 100%" [value]="progress" status="primary">
            Uploading {{ progress }}%
          </nb-progress-bar>
        </div>
        <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]" *ngIf="this.type === 3">
          <p style="font-weight: bold; font-size: 16px; margin-bottom: 0px">Attach Files</p>
          <div class="file-container" appDnd (fileDropped)="onFileDropped($event)">
            <input type="hidden" />
            <input
              type="file"
              multiple
              #fileDropRef
              id="fileDropRef"
              (change)="fileBrowseHandler($event)"
              accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
            />
            <p class="m-0" for="fileDropRef">
              <nb-icon icon="file-add"></nb-icon>
              Drop Document here or Click to upload.
            </p>
          </div>
        </div>
        <div class="w-8/12 px-2" *ngIf="pendingDocuments && pendingDocuments.length > 0 && this.type === 3">
          <div class="files-list">
            <div class="single-file" *ngFor="let file of pendingDocuments; let i = index">
              <div class="info">
                <div class="attachment">
                  <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
                </div>
                <div class="name">
                  {{ file?.name }}
                </div>
                <div class="delete" (click)="clearDocuments(file, false)">
                  <nb-icon class="file-delete" icon="close-circle"></nb-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-8/12 px-2" *ngIf="uploadedDocuments && uploadedDocuments.length > 0 && this.type === 3">
          <div class="files-list">
            <div class="single-file" *ngFor="let file of uploadedDocuments; let i = index">
              <div class="info">
                <div class="attachment">
                  <nb-icon icon="attachFileIcon" pack="custom"></nb-icon>
                </div>
                <div class="name" (click)="downloadFile(file)">
                  {{ file?.documentName }}
                </div>
                <div class="delete" (click)="clearDocuments(file, true)">
                  <nb-icon class="file-delete" icon="close-circle"></nb-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
        <br />
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="flex flex-wrap -mx-2" style="margin-right: 9px; justify-content: end">
        <div *ngIf="edit">
          <button
            [nbSpinner]="archiveLoading"
            nbButton
            status="default"
            class="new-item-button"
            (click)="archiveTaskConfirm()"
            nbTooltip="Archive"
            nbTooltipStatus="control"
            nbTooltipPlacement="bottom"
            style="min-width: 135px"
          >
            <nb-icon icon="archiveIcon" pack="custom"></nb-icon>
            ARCHIVE TASK
          </button>
        </div>
        <button
          [nbSpinner]="saveLoading"
          nbButton
          status="primary"
          style="margin-left: 10px; min-width: 135px"
          (click)="onSubmit()"
          nbTooltip="Save"
          nbTooltipStatus="control"
          nbTooltipPlacement="bottom"
        >
          SAVE
        </button>
      </div>
    </nb-card-footer>
  </nb-card>
</form>
