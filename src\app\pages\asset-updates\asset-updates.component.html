<div class="flex flex-wrap -mx-2">
  <div class="md:w-9/12 px-2" style="margin: auto">
    <div class="title">
      <h5>{{ updateTitle }}</h5>
    </div>
  </div>
  <div class="md:w-3/12 px-2 text-right my-[15px]">
    <!-- <button class="float-right" nbButton status="primary" (click)="createWorkspace()">
            Create New Workspace
        </button> -->

    <nb-select
      placeholder=""
      fullWidth
      size="large"
      shape="semi-round"
      status="basic"
      name="lenderId"
      [(ngModel)]="lenderId"
      (selectedChange)="loadDashboard()"
    >
      <nb-option
        *ngFor="let lender of lenders"
        [value]="lender.id"
        [hidden]="!(lender.updatePublishStatusId === UpdatePublishStatus.Published)"
      >
        {{ lender.updateTitle }}
      </nb-option>
    </nb-select>
  </div>
</div>

<div class="flex flex-wrap -mx-2" *ngIf="assetDetails">
  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div *ngIf="assetDetails">{{ assetDetails.facilityName }}</div>
            </div>
          </div>
        </div>

        <br />

        <div class="image-container">
          <ng-container *ngIf="assetDetails">
            <img *ngIf="assetDetails.imageUrl" [src]="assetDetails.imageUrl" />

            <nb-icon class="img" icon="image-outline" *ngIf="!assetDetails.imageUrl"> </nb-icon>
          </ng-container>

          <div class="invested" *ngIf="assetDetails && assetDetails.drawnBalance > 0">
            {{ assetDetails.facilityType | uppercase }} -
            {{ assetDetails.drawnBalance | currency: "USD" : "symbol" : "1.0" }}
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Loan Summary</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <nb-card-body [nbSpinner]="false" class="nb-scroll-body">
        <div>
          <nb-list>
            <nb-list-item>
              <div>Loan Status</div>
              <div>
                <strong *ngIf="assetDetails.loanStatus === 'Watchlist'" class="text-danger">
                  {{ assetDetails.loanStatus }}
                </strong>
              </div>
              <div>
                <strong *ngIf="assetDetails.loanStatus === 'Current'" class="text-blue-600">
                  {{ assetDetails.loanStatus }}
                </strong>
              </div>
              <div>
                <strong *ngIf="assetDetails.loanStatus === 'Exited'" class="text-basic">
                  {{ assetDetails.loanStatus }}
                </strong>
              </div>
            </nb-list-item>
            <nb-list-item>
              <div>Facility Limit</div>
              <div>{{ assetDetails.facilityLimit | currency: "USD" : "symbol" : "1.0" }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Drawn Balance</div>
              <div>{{ assetDetails.drawnBalance | currency: "USD" : "symbol" : "1.0" }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Financial Close</div>
              <div>{{ assetDetails.financialClose | date: "dd/MM/YYYY" }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Repayment Date</div>
              <div>{{ assetDetails.repaymentDate | date: "dd/MM/YYYY" }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Months to Maturity</div>
              <div>{{ assetDetails.term }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>LVR Limit</div>
              <div>{{ assetDetails.lvrLimit | mask: "separator.2" }}%</div>
            </nb-list-item>
            <nb-list-item>
              <div>LVR Actual</div>
              <div>{{ assetDetails.lvrActual | mask: "separator.2" }}%</div>
            </nb-list-item>
            <nb-list-item>
              <div>Loan Manager</div>
              <div>{{ assetDetails.portfolioManager }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Originator</div>
              <div>{{ assetDetails.originator }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Project Summary</div>
              <div>{{ assetDetails.projectSummary }}</div>
            </nb-list-item>
            <nb-list-item>
              <div>Security Address</div>
              <div>{{ assetDetails.securityAddress }}</div>
            </nb-list-item>
          </nb-list>
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Manager Reports</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <nb-card-body [nbSpinner]="!managerReports" class="nb-scroll-body">
        <ng-container *ngIf="managerReports && managerReports.length === 0">
          <div class="no-data">
            <p class="no-data-text">No Manager Documents yet.</p>
          </div>
        </ng-container>

        <div>
          <div *ngFor="let logs of managerReports">
            <div class="logs">
              <div class="display-flex" style="align-items: center">
                <nb-icon class="file-icon" icon="file-text"></nb-icon>
                <div class="text-blue-600 cursor-pointer file-name" (click)="downloadFile(logs)">
                  {{ logs.description }}
                </div>
              </div>
              <div>{{ logs.date | date: "dd/MM/YYYY" }}</div>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-8/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header *ngIf="assetDetails.loanManagementUpdateLabel">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>{{ assetDetails.loanManagementUpdateLabel }}</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <ng-container *ngIf="!assetDetails.loanManagementUpdate">
        <div class="no-data">
          <p class="no-data-text">No loan management update yet.</p>
        </div>
      </ng-container>

      <nb-card-body *ngIf="assetDetails.loanManagementUpdate" class="nb-scroll-body">
        <div
          class="html-contant w-full px-2 editor-content"
          [innerHTML]="sanitizer.bypassSecurityTrustHtml(assetDetails.loanManagementUpdate)"
        ></div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Supporting Documentation</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <nb-card-body [nbSpinner]="!supportingDocuments" class="nb-scroll-body">
        <ng-container *ngIf="supportingDocuments && supportingDocuments.length === 0">
          <div class="no-data">
            <p class="no-data-text">No Supporting Documents yet.</p>
          </div>
        </ng-container>
        <div>
          <div *ngFor="let logs of supportingDocuments">
            <div class="logs">
              <div class="display-flex" style="align-items: center">
                <nb-icon class="file-icon" icon="file-text"></nb-icon>
                <div class="text-blue-600 cursor-pointer file-name" (click)="downloadFile(logs)">
                  {{ logs.description }}
                </div>
              </div>
              <div>{{ logs.date | date: "dd/MM/YYYY" }}</div>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <!-- <div class="md:w-full px-2 sm:w-full px-2 w-full px-2">
        <nb-card>

            <nb-card-body [nbSpinner]="!(assetKeyDataId)">

                <app-asset-chat *ngIf="assetKeyDataId" [showFilter]="false" [assetId]="assetKeyDataId">
                </app-asset-chat>

            </nb-card-body>
        </nb-card>
    </div> -->

  <div class="md:w-8/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Messages</div>
            </div>
          </div>
        </div>
      </nb-card-header>
      <nb-card-body [nbSpinner]="false" class="nb-scroll-body">
        <div *ngFor="let logs of recentMessages">
          <div class="logs">
            <div class="clickable">
              <!-- <i pBadge *ngIf="logs.isRead !== 'True'" severity="danger"></i> -->
              <div class="recent-msg">
                <nb-user
                  color="#002c24"
                  size="medium"
                  [name]="logs.userName"
                  [showName]="false"
                  [showTitle]="false"
                  status="success"
                >
                </nb-user>
                <div>
                  <div>
                    <strong class="text-bold-500">{{ logs.userName }} </strong>
                    <span *ngIf="logs.facilityName">
                      | <span class="text-blue-600"> {{ logs.facilityName }} </span>
                    </span>
                  </div>
                  <div>
                    <span class="user-title">{{ logs.message }} </span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <i>{{ logs.dateCreated | date: "dd/MM/YYYY" }}</i>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-header>
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Covenant Reporting</div>
            </div>
          </div>
        </div>
      </nb-card-header>

      <nb-card-body [nbSpinner]="!covenantReportingCompleted || !covenantReportingDue" class="nb-scroll-body">
        <ng-container
          *ngIf="
            covenantReportingCompleted &&
            covenantReportingCompleted.length === 0 &&
            covenantReportingDue &&
            covenantReportingDue.length === 0
          "
        >
          <div class="no-data">
            <p class="no-data-text">No Covenant Reporting yet.</p>
          </div>
        </ng-container>
        <div>
          <nb-list
            *ngIf="
              (covenantReportingCompleted && covenantReportingCompleted.length !== 0) ||
              (covenantReportingDue && covenantReportingDue.length !== 0)
            "
          >
            <nb-list-item style="flex-direction: column">
              <div class="covenant-title">Completed This Month</div>
              <div>
                <ul>
                  <li *ngFor="let task of covenantReportingCompleted">{{ task.taskTitle }}</li>
                </ul>
              </div>
            </nb-list-item>
            <nb-list-item style="flex-direction: column">
              <div class="covenant-title">Due Next Month</div>
              <div>
                <ul>
                  <li *ngFor="let task of covenantReportingDue">{{ task.taskTitle }}</li>
                </ul>
              </div>
            </nb-list-item>
          </nb-list>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
</div>
