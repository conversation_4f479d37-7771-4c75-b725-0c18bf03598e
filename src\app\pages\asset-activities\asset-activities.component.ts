import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetector<PERSON>ef, Component, On<PERSON><PERSON>roy, OnInit, Optional, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { AssetTaskType } from '@core/models/config';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { AssetAddTaskComponent } from '@features/lender/asset-add-task/asset-add-task.component';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogRef,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
  NbTooltipModule,
  NbUserModule,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { Table, TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-asset-activities',
  templateUrl: './asset-activities.component.html',
  styleUrls: ['./asset-activities.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbUserModule,
    NbInputModule,
    NbTooltipModule,
    TableModule,
    NbButtonModule,
  ],
})
export class AssetActivitiesComponent implements OnInit, AfterViewChecked, OnDestroy {
  @ViewChild('dt') dt!: Table;
  @ViewChild('admin', { static: false }) admin: any;
  dtTrigger: Subject<any> = new Subject<any>();
  loading = false;
  userId: any;

  activities!: any[];
  selectedActivity: any = {};
  totalRecords = 0;
  filterParams: Filters = {};
  eventFilters: any;
  eventStore: any;

  facilityList: any[] = [];
  statusOptions: any[] = [
    { id: null, name: 'All' },
    { id: 'BEFORE_1_BOT', name: 'Overdue' },
    { id: '1', name: 'Completed' },
  ];
  dateFilterData: any[] = [];
  originatorUsers: any[] = [];
  resetTaskObject: any = {};
  selectedUser = '';
  selectedStatus: any;
  selectedFacility = '';
  selectedDate = '';
  searchTerm = '';

  constructor(
    public sanitizer: DomSanitizer,
    @Optional() private dialogRef: NbDialogRef<any>,
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private sharedService: SharedService,
    private assetService: AssetService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private dialogService: NbDialogService,
    private documentService: DocumentService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.dateFilterData = this.sharedService.getDatefilterRowsActivities();
    this.userId = this.sharedService.getUserIdValue.userId;
    this.selectedStatus = this.statusOptions[0];
    this.filterParams = {
      pageNumber: 1,
      pageSize: 100,
      sortField: 'dueDate',
      sortOrder: 'asc',
      assignedUserId: this.sharedService.getUserIdValue.userId,
      taskStatus: this.selectedStatus,
    } as Filters;

    if (this.sharedService.isAdmin() || this.sharedService.isOriginatorManager()) {
      this.getAssetList();
      this.getOriginatorList();
    }
  }

  private getAssetList(): void {
    this.loading = true;
    this.assetService.getAssetList().subscribe(
      (data: any) => {
        if (data.success) {
          this.facilityList = data.payload.assets.sort((a: any, b: any) =>
            a.facilityName.localeCompare(b.facilityName),
          );
          this.getActivities();
        } else {
          this.loading = false;
          this.toast.danger(data.error.message, 'Oops!');
        }
      },
      () => {
        this.loading = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  getActivities(): void {
    this.assetService.getActivitiesTasks(this.filterParams).subscribe(
      (res: any) => {
        if (res.success) {
          const tasks = (res as any).payload.tasks;
          this.activities = tasks.reduce((agg: any, item: any) => {
            const o = { ...item };
            if (!item.taskContent || item.taskContent == 'null') {
              o['taskContent'] = '';
            }
            if (item.completedBy) {
              const userObj = this.originatorUsers.find((i: any) => i.userId == item.completedBy);
              o['completedByName'] = userObj ? userObj.contactName : 'No User';
              o['completedByDate'] = new Date(item.completedByDate);
            }
            agg.push(o);
            return agg;
          }, []);
          this.totalRecords = (res as any).payload.rows;
          this.dtTrigger.next(this.activities);
          this.loading = false;
        } else {
          this.loading = false;
          this.toast.danger(res.error.message, 'Oops!');
        }
      },
      () => {
        this.loading = false;
        this.toast.danger('Something went wrong', 'Oops!');
      },
    );
  }

  private getOriginatorList(): void {
    this.assetService.getLenderOriginatorUsers().subscribe((data: any) => {
      if (data.success) {
        this.originatorUsers = (data as any).payload.userResult
          .filter((user: any) => user.roleId == 4 || user.roleId == 1)
          .sort((a: any, b: any) => a.contactName.localeCompare(b.contactName));
        this.selectedUser = this.originatorUsers.find((i: any) => i.userId == this.sharedService.getUserIdValue.userId);
      }
    });
  }

  editTask(task: any) {
    if (task && task.taskType == AssetTaskType.Covenant) {
      const facility = this.facilityList.find((el) => el.id == task.assetId);
      this.sharedService.setFormParamValue({
        userId: this.userId,
        assetKeyDataId: task.assetId,
        facility: facility,
        changeTab: true,
        tabId: 5, // covenant tabId = 6 (5 + 1)
      });
      this.router.navigate(['/asset/portfolio']);
    } else {
      this.sharedService.setFormParamValue({
        userId: this.userId,
        assetKeyDataId: task ? task.assetId : 0,
        changeTab: false,
      });
      this.dialogService
        .open(AssetAddTaskComponent, {
          context: {
            data: task ? task : null,
            edit: task ? true : false,
            type: AssetTaskType.NoteTask,
            facilityList: this.facilityList,
          },
          autoFocus: false,
        })
        .onClose.subscribe((res: any) => {
          if (res) {
            this.nextPage(
              {
                filters: {},
                globalFilter: '',
                sortField: 'dueDate',
                sortOrder: 1,
                first: 0,
                rows: 50,
              },
              true,
            );
          }
        });
    }
  }

  toggleTaskCompletion(data: any) {
    if (this.isOriginatorManager() || data.assignedTo == this.userId || data.createdBy == this.userId) {
      // || this.isAdmin()
      this.dialogService
        .open(ConfirmPopupComponent, {
          context: {
            title: 'Complete Task',
            message: 'Are you sure you want to proceed?',
            yesButton: 'Complete Task',
          },
          autoFocus: false,
          hasBackdrop: true,
          closeOnEsc: false,
          closeOnBackdropClick: false,
          hasScroll: false,
        })
        .onClose.subscribe((res: any) => {
          if (res) {
            // if (this.isOriginatorManager()) {
            data['Edited'] = true;
            data['completedBy'] = this.userId;
            data['completedByDate'] = new Date(Date.now());
            // }
            // else if (this.isAdmin() && this.resetTaskObject[data['completedBy']]) {
            //   data['completedByDate'] = this.resetTaskObject[data['completedBy']]['completedByDate']
            //   data['completedBy'] = this.resetTaskObject[data['completedBy']]['completedBy']
            //   data['Edited'] = this.resetTaskObject[data['completedBy']]['Edited'];
            //   this.resetTaskObject[data['completedBy']] = null;
            // }

            this.submitTaskChanges();
          }
        });
    }
  }

  resetTaskCompletion(data: any) {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Reset Task',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Reset Completed Task',
        },
        autoFocus: false,
        hasBackdrop: true,
        closeOnEsc: false,
        closeOnBackdropClick: false,
        hasScroll: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          if (this.isAdmin()) {
            this.resetTaskObject[data['completedBy']] = {
              ['completedByDate']: data['completedByDate'],
              ['completedBy']: data['completedBy'],
              ['Edited']: false,
            };
            data['completedBy'] = null;
            data['completedByDate'] = null;
            data['Edited'] = true;
          }

          this.submitTaskChanges();
        }
      });
  }

  submitTaskChanges() {
    const updatedTask = this.activities.filter((row) => Boolean(row.Edited));
    if (updatedTask.length) {
      this.assetService.saveTask(updatedTask).subscribe((res: any) => {
        this.getActivities();
        if (res.success) {
          this.dialogService
            .open(ConfirmPopupComponent, {
              context: {
                yesButton: 'CLOSE',
                yesSuccessPopUp: '',
                message: 'Task successfully updated',
                icon: 'checkmark-circle-2-outline',
              },
              autoFocus: false,
              hasBackdrop: true,
              closeOnEsc: false,
              closeOnBackdropClick: false,
              hasScroll: false,
            })
            .onClose.subscribe((res: any) => {
              this.toast.success('Task Updated', 'Success!');
            });
        }
      });
    } else {
      this.toast.warning('No task changes to update.', 'Error!');
    }
  }

  getRowClass(task: any) {
    const match = new Date(task.dueDate).getTime(); // convert date to number
    const today = new Date().setHours(0, 0, 0, 0); // get present day as number
    if (today > match && !task.completedBy && task.completedBy == 0) {
      return 'overdue';
    } else if (task.completedBy && task.completedBy > 0) {
      return 'completed';
    } else {
      return '';
    }
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  close(): void {
    this.dialogRef.close(false);
  }

  filterGlobal(event: any): void {
    this.dt?.filterGlobal(event.target.value, 'contains');
  }
  nextPage(event: TableLazyLoadEvent, reload = false): void {
    this.spinner.show();
    if (!reload) {
      this.eventStore = event;
      this.eventFilters = event.filters;
    } else {
      // after performing edit/archive task
      this.resetTable();
    }

    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams, true);
    this.filterParams.export = false;
    this.getActivities();
  }

  resetTable() {
    this.dt.filters = {};
    this.selectedFacility = '';
    this.selectedDate = '';
    this.searchTerm = '';
    this.selectedStatus = this.statusOptions[0];
    this.selectedUser = this.originatorUsers.find((i: any) => i.userId == this.sharedService.getUserIdValue.userId);
    this.filterParams = {
      pageNumber: 1,
      pageSize: 50,
      sortField: 'dueDate',
      sortOrder: 'asc',
      assignedUserId: this.sharedService.getUserIdValue.userId,
      taskStatus: this.selectedStatus,
    } as Filters;
  }

  ngOnDestroy(): void {}

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isOriginatorManager(): boolean {
    return this.sharedService.isOriginatorManager();
  }
  async downloadFile(file: any): Promise<void> {
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }
}
