import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DocumentType } from '@core/models/config';
import { firstValueFrom } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ExportService } from './export.service';
import { Filters } from './shared.service';

export interface DocumentPayload {
  investmentId: number;
  isInvestment: boolean;
  documentType: DocumentType;
  allDocTypes: boolean;
  sortField?: string;
  sortOrder?: string;
}
@Injectable({
  providedIn: 'root',
})
export class InvestmentService {
  constructor(
    private http: HttpClient,
    private exportService: ExportService,
  ) {}
  async getImage(url: string): Promise<Blob | undefined> {
    // return this.http.get(url as string).toPromise();
    return await firstValueFrom(this.http.get(url, { responseType: 'blob' }));
  }

  saveInvestment(data: any): any {
    return this.http.post(`${environment.apiURL}/api/Investment/save-investment`, data);
  }

  saveOverview(data: any): any {
    return this.http.post(`${environment.apiURL}/api/Investment/save-overview`, data);
  }

  getDocuments(documentPayload: DocumentPayload): any {
    return this.http.post(`${environment.apiURL}/api/Investment/get-documents`, documentPayload);
  }

  getInvestmentDetail(investmentId: any, investorId?: number): any {
    if (investorId) {
      return this.http.get(`${environment.apiURL}/api/Investment/${investmentId}/detail/${investorId}`);
    } else {
      return this.http.get(`${environment.apiURL}/api/Investment/${investmentId}/detail`);
    }
  }

  getFinancial(investmentId: any): any {
    return this.http.get(`${environment.apiURL}/api/Investment/${investmentId}/get-financial`);
  }

  saveFinancial(userFilters: any): any {
    return this.http.post(`${environment.apiURL}/api/Investment/save-financial`, userFilters);
  }

  saveKeyData(userFilters: any): any {
    return this.http.post(`${environment.apiURL}/api/Investment/save-key-data`, userFilters);
  }

  getInvestments(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investment/get-investments`, userFilters);
  }

  getEntityType(data: any): any {
    return this.http.post(`${environment.apiURL}/api/Investment/get-type-by`, data);
  }

  getInvestmentLookup(data: any): any {
    return this.http.post(`${environment.apiURL}/api/Investment/lookup`, data);
  }

  async getInvestmentsExport(filters: any): Promise<void> {
    this.exportService.export(
      filters,
      `${environment.apiURL}/api/Investment/get-investments`,
      `Investments_${new Date().getTime()}.xlsx`,
    );
  }

  validateTmoLoanId(loanId: string, currentInvestmentId?: number): any {
    const payload = {
      loanId: loanId,
      investmentId: currentInvestmentId || 0,
    };
    return this.http.post(`${environment.apiURL}/api/Investment/validate-tmo-loan-id`, payload);
  }
}
