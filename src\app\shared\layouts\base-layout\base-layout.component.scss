@use "@nebular/theme/styles/global/breakpoints";
@use "@nebular/theme/styles/core/mixins";
@use "themes" as *;

nb-sidebar .main-container {
  transition: width 0.5s;
  transition-delay: 00.1s;
  // margin: -20px;
}

@include nb-install-component() {
  nb-actions {
    float: right;
  }

  nb-action {
    height: auto;
    display: flex;
    align-content: center;
  }

  .header-container {
    display: block;
    align-items: center;
    width: -webkit-fill-available;
    width: -moz-available;
    .sidebar-toggle {
      @include nb-ltr(margin-right, 1.25rem);
      @include nb-rtl(margin-left, 1.25rem);
      text-decoration: none;
      color: nb-theme(text-hint-color);
      nb-icon {
        font-size: 1.75rem;
      }
    }

    .logo {
      padding: 0 1.25rem;
      font-size: 1.75rem;
      @include nb-ltr(border-left, 1px solid nb-theme(divider-color));
      @include nb-rtl(border-right, 1px solid nb-theme(divider-color));
      white-space: nowrap;
      text-decoration: none;
    }
  }

  nb-list-item :hover,
  .selected {
    background: #f8f8f8;
    border-radius: 10px;
    cursor: pointer;
  }

  .user-popup {
    display: flex;
    align-items: center;
    min-width: 280px;
    justify-content: space-between;
    padding: 8px 20px;
  }
  .direction-switcher {
    @include nb-ltr(margin-left, 2rem);
    @include nb-rtl(margin-right, 2rem);
  }

  // @include media-breakpoint-down(sm) {
  //     .control-item {
  //         display: none;
  //     }
  //     .user-action {
  //         border: none;
  //         padding: 0;
  //     }
  // }    // @include bootstrap.media-breakpoint-down(sm) {
  //     nb-select,
  //     .direction-switcher {
  //         display: none;
  //     }
  // }
  @media (min-width: 768px) {
    nb-select,
    .direction-switcher {
      display: none;
    }
  }
}
