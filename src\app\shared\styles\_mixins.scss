// Sydney Wyde Brand Mixins
// ========================
// Use this file to access Sydney Wyde brand mixins in your components

@use "./variables" as *;

// SCSS Functions
// ==============

// Function to get color with opacity
@function sw-color-opacity($color, $opacity) {
    @if $color == $sw-velvet-green {
        @return rgba($sw-velvet-green-rgb..., $opacity);
    } @else if $color == $sw-lime {
        @return rgba($sw-lime-rgb..., $opacity);
    } @else if $color == $sw-mist {
        @return rgba($sw-mist-rgb..., $opacity);
    }
    @return rgba($color, $opacity);
}

// Function to get spacing value
@function sw-space($size) {
    @return map-get($sw-spacing, $size);
}

// Function to get color value
@function sw-color($name) {
    @return map-get($sw-colors, $name);
}

// Function to get text color value
@function sw-text-color($name) {
    @return map-get($sw-text-colors, $name);
}

// SCSS Mixins
// ===========

// Mixin for button styles
@mixin sw-button-style($bg-color, $text-color, $border-color: $bg-color) {
    background-color: $bg-color;
    color: $text-color;
    border: 1px solid $border-color;
    padding: $sw-space-sm $sw-space-md;
    border-radius: $sw-radius-md;
    font-family: $sw-font-heading;
    font-weight: $sw-font-weight-medium;
    text-decoration: none;
    cursor: pointer;
    transition: all $sw-transition-fast;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    line-height: 1.5;
    min-height: 2.5rem;
}

// Mixin for primary button with hover states
@mixin sw-button-primary {
    @include sw-button-style($sw-primary, $sw-text-inverse, $sw-primary);

    &:hover,
    &:focus {
        background-color: $sw-primary-dark;
        border-color: $sw-primary-dark;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px sw-color-opacity($sw-velvet-green, 0.15);
    }
}

// Mixin for secondary button with hover states
@mixin sw-button-secondary {
    @include sw-button-style(transparent, $sw-primary, $sw-primary);

    &:hover,
    &:focus {
        background-color: sw-color-opacity($sw-primary, 0.1);
        border-color: $sw-primary-dark;
        color: $sw-primary-dark;
    }
}

// Mixin for accent button with hover states
@mixin sw-button-accent {
    @include sw-button-style($sw-accent, $sw-primary, $sw-accent);

    &:hover,
    &:focus {
        filter: brightness(0.9);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px sw-color-opacity($sw-lime, 0.15);
    }
}

// Mixin for card styles
@mixin sw-card($bg-color: $sw-white, $shadow: true) {
    background-color: $bg-color;
    border-radius: $sw-radius-lg;
    @if $shadow {
        box-shadow: 0 2px 4px sw-color-opacity($sw-velvet-green, 0.1);
    }
    overflow: hidden;
    transition: all $sw-transition-normal;

    &:hover {
        @if $shadow {
            box-shadow: 0 4px 8px sw-color-opacity($sw-velvet-green, 0.15);
            transform: translateY(-2px);
        }
    }
}

// Mixin for form control styles
@mixin sw-form-control {
    display: block;
    width: 100%;
    padding: $sw-space-sm $sw-space-md;
    font-family: $sw-font-body;
    font-size: 0.875rem;
    line-height: 1.5;
    color: $sw-text-primary;
    background-color: $sw-white;
    border: 1px solid $sw-light-grey;
    border-radius: $sw-radius-md;
    transition: all $sw-transition-fast;

    &:focus {
        outline: none;
        border-color: $sw-primary;
        box-shadow: 0 0 0 3px sw-color-opacity($sw-primary, 0.2);
    }

    &:disabled {
        background-color: $sw-mist;
        opacity: 0.6;
        cursor: not-allowed;
    }
}

// Responsive breakpoint mixin
@mixin sw-breakpoint($breakpoint) {
    $bp-value: map-get($sw-breakpoints, $breakpoint);
    @if $bp-value {
        @media (min-width: $bp-value) {
            @content;
        }
    }
}

// Mixin for text styles
@mixin sw-text-style($family, $weight, $color, $line-height: 1.5) {
    font-family: $family;
    font-weight: $weight;
    color: $color;
    line-height: $line-height;
}

// Specific text style mixins for brand
@mixin sw-heading-feature {
    @include sw-text-style($sw-font-feature, $sw-font-weight-regular, $sw-text-primary, 1.2);
    margin-bottom: $sw-space-md;
}

@mixin sw-heading {
    @include sw-text-style($sw-font-heading, $sw-font-weight-medium, $sw-text-primary, 1.25);
    margin-bottom: $sw-space-sm;
}

@mixin sw-heading-semibold {
    @include sw-text-style($sw-font-heading, $sw-font-weight-semibold, $sw-text-primary, 1.25);
    margin-bottom: $sw-space-sm;
}

@mixin sw-text-body {
    @include sw-text-style($sw-font-body, $sw-font-weight-regular, $sw-text-secondary, 1.6);
    margin-bottom: $sw-space-sm;
}

@mixin sw-text-light {
    @include sw-text-style($sw-font-body, $sw-font-weight-light, $sw-text-secondary, 1.6);
}

// Mixin for centering content
@mixin sw-center-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

// Mixin for absolute centering
@mixin sw-absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

// Mixin for truncating text
@mixin sw-text-truncate {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

// Mixin for visually hidden elements (accessibility)
@mixin sw-visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

// Mixin for focus styles
@mixin sw-focus-style {
    outline: 2px solid $sw-accent;
    outline-offset: 2px;
}

// Mixin for reset list styles
@mixin sw-list-reset {
    margin: 0;
    padding: 0;
    list-style: none;
}

// Mixin for aspect ratio
@mixin sw-aspect-ratio($width: 16, $height: 9) {
    position: relative;
    overflow: hidden;

    &::before {
        content: "";
        display: block;
        width: 100%;
        padding-bottom: percentage($height / $width);
    }

    > * {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

// Mixin for grid layout
@mixin sw-grid($columns: 12, $gap: $sw-space-md) {
    display: grid;
    grid-template-columns: repeat($columns, 1fr);
    gap: $gap;
}

// Mixin for flexbox grid
@mixin sw-flex-grid($columns: 3, $gap: $sw-space-md) {
    display: flex;
    flex-wrap: wrap;
    margin: calc(#{$gap} / -2);

    > * {
        flex: 0 0 calc(100% / #{$columns} - #{$gap});
        margin: calc(#{$gap} / 2);
    }
}

// Mixin for container with max width
@mixin sw-container($max-width: 1200px, $padding: $sw-space-md) {
    max-width: $max-width;
    margin: 0 auto;
    padding-left: $padding;
    padding-right: $padding;
}

// Mixin for backdrop/overlay
@mixin sw-backdrop($bg-color: sw-color-opacity($sw-velvet-green, 0.5), $z-index: $sw-z-modal-backdrop) {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: $bg-color;
    z-index: $z-index;
}

// Mixin for smooth animations
@mixin sw-animate($property: all, $duration: $sw-transition-normal, $timing: ease-in-out) {
    transition: $property $duration $timing;
}

// Mixin for hover effects
@mixin sw-hover-lift {
    transition: transform $sw-transition-fast ease-out;

    &:hover {
        transform: translateY(-2px);
    }
}
