import { HttpResponse } from './http.response';

export interface AssetsResponse extends HttpResponse {
  payload: AssetsPayload;
}

export interface AssetsPayload {
  assets: Asset[];
  rows: number;
}

export interface Asset {
  id: number;
  facilityName: string;
  lenderReference: string;
  securityAddress: string;
  projectSummary: string;
  borrower: string;
  facilityType: string;
  facilityTypeId: number;
  facilityLimit: number;
  financialClose: Date;
  term: number;
  maturityDate: Date;
  lvrLimit: number;
  ltc: number;
  count: number;
}
