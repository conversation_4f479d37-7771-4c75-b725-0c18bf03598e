<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2">
      <h5 class="w-6/12 px-2">
        <div class="title">{{ title }}</div>
      </h5>

      <div class="w-6/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="onClose()">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="popup-body">
      <div class="w-full px-2 my-[15px]">
        <!-- <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert> -->
      </div>
      <p-table
        [value]="data"
        [paginator]="true"
        [rows]="10"
        [responsive]="true"
        [scrollable]="true"
        [scrollHeight]="'500px'"
        selectionMode="single"
        [(selection)]="selectedItem"
        [rowsPerPageOptions]="[5, 10, 20]"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
      >
        <ng-template pTemplate="header">
          <tr>
            <th *ngFor="let col of columns">
              {{ col.header }}
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-data="data">
          <tr [pSelectableRow]="data">
            <td>{{ data?.id }}</td>
            <td>{{ data?.title }}</td>
            <td>{{ data?.borrower }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="w-full px-2">
      <button
        class="float-right"
        [nbSpinner]="loading"
        nbButton
        status="primary"
        style="min-width: 135px"
        (click)="onSave(selectedItem)"
      >
        SELECT
      </button>
    </div>
  </nb-card-footer>
</nb-card>
