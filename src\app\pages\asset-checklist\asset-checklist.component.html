<div class="flex flex-wrap -mx-2 my-[15px]">
  <div class="md:w-9/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Checklists</h5>
    </div>
  </div>
  <div class="md:w-3/12 px-2 text-right" style="margin: auto">
    <button class="float-right" nbButton status="primary" (click)="createChecklist()">
      <nb-icon icon="plus-outline"></nb-icon> CREATE NEW CHECKLIST
    </button>
  </div>
</div>
<div *ngIf="checklists">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [value]="checklists"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="true"
        [rows]="50"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        sortField="dateCreated"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 500px" [pSortableColumn]="'checklistName'">
              <div>
                <div>Checklist Title</div>
                <p-sortIcon [field]="'checklistName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 110px" [pSortableColumn]="'dateCreated'">
              <div>
                <div>Created</div>
                <p-sortIcon [field]="'dateCreated'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 110px" [pSortableColumn]="'createdByName'">
              <div>
                <div>Created by</div>
                <p-sortIcon [field]="'createdByName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 110px" [pSortableColumn]="'status'">
              <div>
                <div>Status</div>
                <p-sortIcon [field]="'status'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 95px">
              <div>
                <div style="margin-left: 9px">Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-checklist>
          <tr>
            <td style="min-width: 500px">{{ checklist.checklistName }}</td>
            <td style="min-width: 110px">{{ checklist.dateCreated | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 110px">{{ checklist.createdByName }}</td>
            <td style="min-width: 110px">
              <button
                type="button"
                style="min-width: 86px; pointer-events: none"
                [ngClass]="[checklist.status === 1 ? 'btn btn-success btn-sm' : 'btn btn-light btn-sm']"
                shape="rectangle"
              >
                {{ checklist.status === 1 ? "Active" : checklist.status === 2 ? "Archived" : "Draft" }}
              </button>
            </td>
            <td style="min-width: 95px">
              <button
                nbButton
                ghost
                shape="round"
                status="default"
                class="button-icon"
                (click)="editChecklist(checklist)"
                nbTooltip="Edit"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="editIcon" pack="custom"></nb-icon>
              </button>
              <button
                nbButton
                ghost
                shape="round"
                status="default"
                class="button-icon"
                (click)="archiveChecklistConfirm(checklist)"
                nbTooltip="Archive"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="archiveIcon" pack="custom"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" *ngIf="!eventFilters" [attr.colspan]="10">
              No checklists found
            </td>
            <td style="text-align: center; display: block" *ngIf="eventFilters" [attr.colspan]="10">
              Sorry, your search did not return any matching results. Please try again
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
