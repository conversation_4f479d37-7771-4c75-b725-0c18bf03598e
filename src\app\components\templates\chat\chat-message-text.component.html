<div class="text">
  <div class="type-icon" *ngIf="metaData.isInternalNote" style="position: absolute; right: 30px; top: 13px">
    <div>
      <nb-icon icon="notesIcon" pack="custom"> </nb-icon>
    </div>
  </div>
  <div style="flex: 1">
    <div style="font-size: 14px">
      {{ date | date: dateFormat }}
      <span *ngIf="metaData.facilityName">
        | <span class="text-blue-600"> {{ metaData.facilityName }} </span>
      </span>
    </div>
    <div class="chat-message" *ngIf="!message && metaData && metaData.attachments.length > 0">
      <b>{{ metaData.userName }}</b
      >Attached
      <ng-container *ngFor="let file of metaData.attachments">
        <a class="clickable" (click)="downloadFile(file)"> {{ file.fileName }} </a>
      </ng-container>
    </div>

    <div class="chat-message" *ngIf="message && metaData && metaData.attachments.length === 0">
      {{ message }}
    </div>

    <div class="chat-message" *ngIf="message && metaData && metaData.attachments.length > 0">
      {{ message }}
      <ng-container *ngFor="let file of metaData.attachments">
        <a class="!text-blue-600 hover:!no-underline cursor-pointer" (click)="downloadFile(file)">
          {{ file.fileName }}
        </a>
      </ng-container>
    </div>
  </div>
</div>
