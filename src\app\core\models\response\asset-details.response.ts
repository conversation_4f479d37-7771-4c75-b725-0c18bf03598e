import { HttpResponse } from './http.response';

export interface AssetDetailsResponse extends HttpResponse {
  payload: AssetDetails;
}

export interface AssetDetails {
  assetId: number;
  facilityName: string;
  facilityType: string;
  facilityTypeId: number;
  portfolioManager: string;
  originator: string;
  projectSummary: string;
  securityAddress: string;
  imageUrl: string;
  lenderId: number;
  drawnBalance: number;
  currentLoanStatusId: number;
  loanStatus: string;
  facilityLimit: number;
  financialClose: string;
  repaymentDate: string;
  maturityDate: string;
  reportingMonth: string;
  term: number;
  lvrLimit: number;
  lvrActual: number;
  loanManagementUpdate: string;
  loanManagementUpdateLabel: string;
  upcomingReporting: string;
  upcomingReportingLabel: string;
  assetDocuments: AssetDocument[];
  assetRecentMessages: AssetRecentMessage[];
}

export interface AssetDocument {
  id: number;
  description: string;
  facility: string;
  type: string;
  category: string;
  date: string;
  fileSize: string;
  documentKey: string;
  uploadedBy: number;
  count: number;
}

export interface AssetRecentMessage {
  chatId: number;
  message: string;
  messageType: number;
  userName: string;
  isAdmin: boolean;
  isInternalNote: boolean;
  attachmentId: number;
  dateCreated: string;
  attachments: Attachment[];
  isSender: boolean;
}

export interface Attachment {
  id: number;
  documentKey: string;
  fileName: string;
  url: string;
  attachedBy: number;
}
