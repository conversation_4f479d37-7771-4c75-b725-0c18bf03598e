import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { NbDialogRef, NbCardModule, NbIconModule, NbButtonModule } from '@nebular/theme';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';

@Component({
  selector: 'app-asset-checklist-preview',
  templateUrl: './asset-checklist-preview.component.html',
  styleUrls: ['./asset-checklist-preview.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbIconModule, TableModule, NbButtonModule],
})
export class AssetChecklistPreviewComponent implements OnInit {
  @Input() tableStructure?: any;
  @Input() title?: string;
  @Input() yesButton?: string;
  constructor(
    private dialogRef: NbDialogRef<any>,
    public sanitizer: DomSanitizer,
  ) {}

  ngOnInit(): void {}

  close(response: boolean): void {
    this.dialogRef.close(response);
  }
}
