import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogRef, NbCardModule, NbButtonModule, NbIconModule } from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-dirty-check',
  templateUrl: './dirty-check.component.html',
  styleUrls: ['./dirty-check.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbButtonModule, NbIconModule],
})
export class DirtyCheckComponent implements OnInit {
  @Input() documentKey: string;

  constructor(
    protected dialogRef: NbDialogRef<any>,
    public spinner: NgxSpinnerService,
  ) {
    this.documentKey = '';
  }

  async ngOnInit(): Promise<void> {}

  close(): void {
    this.dialogRef.close(false);
  }

  delete(): void {
    this.dialogRef.close(true);
  }
}
