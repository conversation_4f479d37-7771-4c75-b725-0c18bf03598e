import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { DashboardService } from '@core/services/dashboard.service';
import { SharedService } from '@core/services/shared.service';
import { NbAccessChecker } from '@nebular/security';
import { NbMenuItem, NbMenuModule, NbMenuService, NbSidebarService, NbToastrService } from '@nebular/theme';
import { Subject } from 'rxjs';
import { filter, map, take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  standalone: true,
  imports: [CommonModule, NbMenuModule],
})
export class SidebarComponent implements OnInit, OnDestroy {
  steps: any;
  sidebarResponsiveState: any;
  destroyed$ = new Subject<boolean>();

  constructor(
    private sharedService: SharedService,
    private sidebarService: NbSidebarService,
    private dashboardService: DashboardService,
    private nbMenuService: NbMenuService,
    private toast: NbToastrService,
    public accessChecker: NbAccessChecker,
    private router: Router,
  ) {}

  menuAdmin: NbMenuItem[] = [];

  async ngOnInit(): Promise<void> {
    await this.loadNavigation();

    // Listen to router events to update active menu items
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroyed$),
      )
      .subscribe(() => {
        this.updateActiveMenuItems();
      });

    this.nbMenuService
      .onItemSelect()
      .pipe(
        filter(({ tag }) => tag === 'menuAdmin'),
        map(({ item: { title } }) => title),
      )
      .subscribe((title) => {
        if (this.sidebarResponsiveState === 'mobile') {
          this.sidebarService.collapse();
        }
      });

    this.nbMenuService
      .onItemClick()
      .pipe(
        filter(({ tag }) => tag === 'menuAdmin'),
        map(({ item: { title } }) => title),
        takeUntil(this.destroyed$),
      )
      .subscribe((title) => {
        if (title === 'Compliance') {
          this.getCompliance();
        }
      });

    this.sidebarService.getSidebarResponsiveState().subscribe((value: any) => {
      this.sidebarResponsiveState = value;
    });

    // Initialize active menu items based on current URL
    this.updateActiveMenuItems();
  }

  private async loadNavigation(): Promise<void> {
    // let hideInvestorDashboard = true;
    let hideInvestments = true;
    let hideInvestors = true;
    let hideDocuments = true;
    let hidePayments = true;
    // let hideCompliances = true;
    let hideUserManagement = true;
    // let hideLenderDashboard = true;
    // let hideAssetManagement = true;
    // let hideLoadDocument = true;
    // let hideChecklist = true;
    // let hideActivities = true;

    // this.accessChecker.isGranted('SydneyWide', 'InvestorDashboard')
    //   .pipe(take(1))
    //   .subscribe((granted: boolean) => {
    //     hideInvestorDashboard = !granted;
    //   });

    this.accessChecker
      .isGranted('YieldStack', 'ViewInvestmentLists')
      .pipe(take(1))
      .subscribe((granted: boolean) => {
        hideInvestments = !granted;
      });

    this.accessChecker
      .isGranted('YieldStack', 'ViewInvestorManagementList')
      .pipe(take(1))
      .subscribe((granted: boolean) => {
        hideInvestors = !granted;
      });

    this.accessChecker
      .isGranted('YieldStack', 'ViewDocuments')
      .pipe(take(1))
      .subscribe((granted: boolean) => {
        hideDocuments = !granted;
      });

    this.accessChecker
      .isGranted('YieldStack', 'ViewPayments')
      .pipe(take(1))
      .subscribe((granted: boolean) => {
        hidePayments = !granted;
      });

    this.accessChecker
      .isGranted('YieldStack', 'ViewUserManagementList')
      .pipe(take(1))
      .subscribe((granted: boolean) => {
        hideUserManagement = !granted;
      });

    // this.accessChecker
    //   .isGranted('YieldStack', 'LenderDashboard')
    //   .pipe(take(1))
    //   .subscribe((granted: boolean) => {
    //     hideLenderDashboard = !granted;
    //   });

    // this.accessChecker
    //   .isGranted('YieldStack', 'ViewAssetManagementList')
    //   .pipe(take(1))
    //   .subscribe((granted: boolean) => {
    //     hideAssetManagement = !granted;
    //   });

    // this.accessChecker
    //   .isGranted('YieldStack', 'ViewLoanDocuments')
    //   .pipe(take(1))
    //   .subscribe((granted: boolean) => {
    //     hideLoadDocument = !granted;
    //   });

    // this.accessChecker
    //   .isGranted('YieldStack', 'ViewChecklist')
    //   .pipe(take(1))
    //   .subscribe((granted: boolean) => {
    //     hideChecklist = !granted;
    //   });

    // this.accessChecker
    //   .isGranted('YieldStack', 'ViewActivities')
    //   .pipe(take(1))
    //   .subscribe((granted: boolean) => {
    //     hideActivities = !granted;
    //   });

    this.menuAdmin = [
      {
        title: 'Investment Management',
        hidden: !this.sharedService.isAdmin(),
        group: true,
      },
      {
        title: 'Dashboard',
        link: '/dashboard',
        icon: { pack: 'custom', icon: 'dashboardIcon' },
        // hidden: hideInvestorDashboard
        hidden: this.sharedService.isLender() || this.sharedService.isOriginatorManager(),
      },
      {
        title: 'Investments',
        link: '/investments',
        icon: { pack: 'custom', icon: 'investmentIcon' },
        hidden: hideInvestments,
      },
      {
        title: 'Investors',
        link: '/investors',
        icon: { pack: 'custom', icon: 'clientIcon' },
        hidden: hideInvestors,
      },
      {
        title: 'Documents',
        link: '/documents',
        icon: { pack: 'custom', icon: 'document40Icon' },
        hidden: hideDocuments,
      },
      {
        title: 'Payments',
        link: '/payments',
        icon: { pack: 'custom', icon: 'paymentIcon' },
        hidden: hidePayments,
      },
      {
        title: 'Reports',
        link: '/reports',
        icon: { pack: 'custom', icon: 'checklistIcon' },
        // hidden: hideReports,
      },
      {
        title: 'Settings',
        link: '/users',
        icon: { pack: 'custom', icon: 'settingIcon' },
        hidden: hideUserManagement,
      },
      {
        title: 'Compliance',
        icon: { pack: 'custom', icon: 'complianceIcon' },
        hidden: !this.sharedService.isAdmin(),
        skipLocationChange: true,
      },
      // {
      //   title: 'Asset Management',
      //   hidden: !this.sharedService.isAdmin(),
      //   group: true,
      // },
      // {
      //   title: 'Dashboard',
      //   link: '/asset/dashboard',
      //   icon: { pack: 'custom', icon: 'dashboardIcon' },
      //   hidden: hideLenderDashboard || this.sharedService.isLender(),
      // },
      // {
      //   title: `Asset ${this.sharedService.isLender() ? 'Reporting' : 'Management'}`,
      //   link: '/asset/portfolio',
      //   icon: { pack: 'custom', icon: 'investmentIcon' },
      //   hidden: hideAssetManagement,
      // },
      // {
      //   title: 'Documents',
      //   link: '/asset/documents',
      //   icon: { pack: 'custom', icon: 'document40Icon' },
      //   hidden: hideLoadDocument,
      // },
      // {
      //   title: 'Activities',
      //   link: '/asset/activities',
      //   icon: { pack: 'custom', icon: 'activitiesIcon' },
      //   hidden: hideActivities,
      // },
      // {
      //   title: 'Checklists',
      //   link: '/asset/checklist',
      //   icon: { pack: 'custom', icon: 'checklistIcon' },
      //   hidden: hideChecklist,
      // },
    ];
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.unsubscribe();
  }

  /**
   * Updates active menu items based on current URL
   */
  private updateActiveMenuItems(): void {
    const url = this.router.url;

    // Find menu items for Investors and Investments
    // and set their selected state based on the current URL
    const investorsMenuItem = this.menuAdmin.find((item) => item.title === 'Investors');
    const investmentMenuItem = this.menuAdmin.find((item) => item.title === 'Investments');
    if (investorsMenuItem) {
      // Check if current URL is related to investor paths that should highlight the Investors menu
      const isInvestorRoute =
        url.startsWith('/investors') ||
        url.startsWith('/investor/new') ||
        url.startsWith('/investor/edit') ||
        url.startsWith('/investor/investment-dashboard');

      investorsMenuItem.selected = isInvestorRoute;
    }
    if (investmentMenuItem) {
      // Check if current URL is related to investment paths that should highlight the Investments menu
      const isInvestmentRoute = url.startsWith('/investments') || url.startsWith('/investment/view');
      investmentMenuItem.selected = isInvestmentRoute;
    }
  }

  getCompliance(): void {
    this.toast.default(``, 'Downloading started', {
      icon: 'download',
    });
    this.dashboardService.getCompliances({});
  }
}
