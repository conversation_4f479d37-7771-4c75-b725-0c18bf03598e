export default class MenuIcons {
  static dashboardIcon(): string {
    return `<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="10" y="10" width="9" height="11" rx="2" fill="currentColor"/>
      <rect x="10" y="23" width="9" height="7" rx="2" fill="currentColor"/>
      <rect x="21" y="10" width="9" height="7" rx="2" fill="currentColor"/>
      <rect x="21" y="19" width="9" height="11" rx="2" fill="currentColor"/>
      </svg>
      `;
  }

  static investmentIcon(): string {
    return `<svg  width="30" height="30" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="file-invoice-dollar" class="svg-inline--fa fa-file-invoice-dollar fa-w-12" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="currentColor" d="M377 105L279.1 7c-4.5-4.5-10.6-7-17-7H256v128h128v-6.1c0-6.3-2.5-12.4-7-16.9zm-153 31V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zM64 72c0-4.42 3.58-8 8-8h80c4.42 0 8 3.58 8 8v16c0 4.42-3.58 8-8 8H72c-4.42 0-8-3.58-8-8V72zm0 80v-16c0-4.42 3.58-8 8-8h80c4.42 0 8 3.58 8 8v16c0 4.42-3.58 8-8 8H72c-4.42 0-8-3.58-8-8zm144 263.88V440c0 4.42-3.58 8-8 8h-16c-4.42 0-8-3.58-8-8v-24.29c-11.29-.58-22.27-4.52-31.37-11.35-3.9-2.93-4.1-8.77-.57-12.14l11.75-11.21c2.77-2.64 6.89-2.76 10.13-.73 3.87 2.42 8.26 3.72 12.82 3.72h28.11c6.5 0 11.8-5.92 11.8-13.19 0-5.95-3.61-11.19-8.77-12.73l-45-13.5c-18.59-5.58-31.58-23.42-31.58-43.39 0-24.52 19.05-44.44 42.67-45.07V232c0-4.42 3.58-8 8-8h16c4.42 0 8 3.58 8 8v24.29c11.29.58 22.27 4.51 31.37 11.35 3.9 2.93 4.1 8.77.57 12.14l-11.75 11.21c-2.77 2.64-6.89 2.76-10.13.73-3.87-2.43-8.26-3.72-12.82-3.72h-28.11c-6.5 0-11.8 5.92-11.8 13.19 0 5.95 3.61 11.19 8.77 12.73l45 13.5c18.59 5.58 31.58 23.42 31.58 43.39 0 24.53-19.05 44.44-42.67 45.07z"></path></svg>
      `;
  }

  static investmentsIcon(): string {
    return `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M15 26C21.0751 26 26 21.0751 26 15C26 8.92487 21.0751 4 15 4C8.92487 4 4 8.92487 4 15C4 21.0751 8.92487 26 15 26ZM16.0092 16.2897C16.2045 16.4748 16.3021 16.7254 16.3021 17.0415C16.3021 17.3461 16.1938 17.589 15.9773 17.7702C15.7608 17.9476 15.4657 18.0363 15.0921 18.0363C14.642 18.0363 14.296 17.9148 14.054 17.6719C13.8163 17.4252 13.6974 17.0724 13.6974 16.6136H11.8569C11.8569 17.404 12.0946 18.0382 12.5702 18.5163C13.0499 18.9905 13.725 19.2701 14.5953 19.3549V20.5H15.6079V19.3491C16.3976 19.2759 17.0175 19.0349 17.4675 18.6262C17.9176 18.2175 18.1426 17.6854 18.1426 17.03C18.1426 16.6791 18.0832 16.3726 17.9643 16.1104C17.8454 15.8444 17.6735 15.6092 17.4484 15.4048C17.2234 15.1966 16.9474 15.0116 16.6205 14.8496C16.2936 14.6838 15.8754 14.5084 15.3659 14.3233C14.8607 14.1383 14.5147 13.9571 14.3279 13.7797C14.1411 13.6023 14.0477 13.3672 14.0477 13.0741C14.0477 12.758 14.1432 12.5112 14.3342 12.3339C14.5253 12.1526 14.7949 12.062 15.143 12.062C15.4954 12.062 15.7756 12.1777 15.9837 12.409C16.196 12.6365 16.3021 12.9816 16.3021 13.4443H18.1426C18.1426 12.6924 17.9303 12.0813 17.5058 11.6109C17.0812 11.1367 16.4932 10.8514 15.7417 10.755V9.5H14.7227V10.7376C13.9627 10.807 13.3535 11.0499 12.895 11.4664C12.4364 11.8828 12.2072 12.4168 12.2072 13.0683C12.2072 13.4462 12.273 13.772 12.4046 14.0457C12.5404 14.3195 12.7272 14.5585 12.965 14.7629C13.207 14.9634 13.4957 15.1427 13.8311 15.3007C14.1665 15.455 14.5656 15.6111 15.0284 15.7692C15.4912 15.9273 15.8181 16.1008 16.0092 16.2897Z" fill="currentColor"/>
      </svg>
      `;
  }

  static document30Icon(): string {
    return `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path fill-rule="evenodd" clip-rule="evenodd" d="M5 9C5 6.23858 7.23858 4 10 4H20C22.7614 4 25 6.23858 25 9V21C25 23.7614 22.7614 26 20 26H10C7.23858 26 5 23.7614 5 21V9ZM8.79543 21.4998C8.79543 21.0855 9.13122 20.7498 9.54543 20.7498H20.4545C20.8687 20.7498 21.2045 21.0855 21.2045 21.4998C21.2045 21.914 20.8687 22.2498 20.4545 22.2498H9.54543C9.13122 22.2498 8.79543 21.914 8.79543 21.4998ZM9.54543 16.7505C9.13122 16.7505 8.79543 17.0863 8.79543 17.5005C8.79543 17.9147 9.13122 18.2505 9.54543 18.2505H20.4545C20.8687 18.2505 21.2045 17.9147 21.2045 17.5005C21.2045 17.0863 20.8687 16.7505 20.4545 16.7505H9.54543ZM8.79543 13.4995C8.79543 13.0853 9.13122 12.7495 9.54543 12.7495H20.4545C20.8687 12.7495 21.2045 13.0853 21.2045 13.4995C21.2045 13.9137 20.8687 14.2495 20.4545 14.2495H9.54543C9.13122 14.2495 8.79543 13.9137 8.79543 13.4995ZM9.54543 8.75024C9.13122 8.75024 8.79543 9.08603 8.79543 9.50024C8.79543 9.91446 9.13122 10.2502 9.54543 10.2502H15C15.4142 10.2502 15.75 9.91446 15.75 9.50024C15.75 9.08603 15.4142 8.75024 15 8.75024H9.54543Z" fill="currentColor"/>
   </svg>
   `;
  }

  static paymentIcon(): string {
    return `<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.5 13C8.5 11.8954 9.39543 11 10.5 11H26.5C27.6046 11 28.5 11.8954 28.5 13V15H8.5V13Z" fill="currentColor"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M28.5 17H8.5V24C8.5 25.1046 9.39543 26 10.5 26H26.5C27.6046 26 28.5 25.1046 28.5 24V17ZM11.5 22C10.9477 22 10.5 22.4477 10.5 23C10.5 23.5523 10.9477 24 11.5 24H14.5C15.0523 24 15.5 23.5523 15.5 23C15.5 22.4477 15.0523 22 14.5 22H11.5Z" fill="currentColor"/>
      <circle cx="27" cy="24" r="5" fill="#F3F3F3"/>
      <path d="M27.3288 25.2992C27.3288 25.098 27.2666 24.9385 27.1424 24.8207C27.0208 24.7005 26.8127 24.5901 26.5182 24.4895C26.2237 24.3889 25.9698 24.2895 25.7563 24.1914C25.5429 24.0908 25.3592 23.9767 25.2052 23.8491C25.0539 23.7191 24.935 23.5669 24.8485 23.3927C24.7648 23.2185 24.7229 23.0112 24.7229 22.7708C24.7229 22.3561 24.8688 22.0163 25.1606 21.7513C25.4524 21.4863 25.8401 21.3318 26.3237 21.2876V20.5H26.9721V21.2986C27.4504 21.36 27.8246 21.5415 28.0947 21.8433C28.3649 22.1427 28.5 22.5315 28.5 23.01H27.3288C27.3288 22.7156 27.2612 22.496 27.1261 22.3512C26.9938 22.204 26.8154 22.1304 26.5912 22.1304C26.3696 22.1304 26.1981 22.188 26.0765 22.3034C25.9549 22.4162 25.8941 22.5733 25.8941 22.7744C25.8941 22.9609 25.9536 23.1106 26.0724 23.2234C26.1913 23.3363 26.4115 23.4516 26.733 23.5694C27.0572 23.6872 27.3234 23.7988 27.5314 23.9043C27.7394 24.0074 27.9151 24.1251 28.0583 24.2576C28.2015 24.3877 28.3109 24.5373 28.3865 24.7066C28.4622 24.8735 28.5 25.0685 28.5 25.2918C28.5 25.7089 28.3568 26.0475 28.0704 26.3076C27.784 26.5676 27.3896 26.721 26.887 26.7676V27.5H26.2427V26.7713C25.6888 26.7173 25.2592 26.5394 24.9539 26.2376C24.6513 25.9334 24.5 25.5298 24.5 25.0268H25.6712C25.6712 25.3188 25.7469 25.5433 25.8982 25.7003C26.0522 25.8549 26.2724 25.9322 26.5588 25.9322C26.7965 25.9322 26.9843 25.8757 27.1221 25.7629C27.2599 25.6476 27.3288 25.493 27.3288 25.2992Z" fill="currentColor"/>
      </svg>
      `;
  }

  static document40Icon(): string {
    return `<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path fill-rule="evenodd" clip-rule="evenodd" d="M10 15C10 12.2386 12.2386 10 15 10H25C27.7614 10 30 12.2386 30 15V25C30 27.7614 27.7614 30 25 30H15C12.2386 30 10 27.7614 10 25V15ZM13.7954 25.9089C13.7954 25.4947 14.1312 25.1589 14.5454 25.1589H25.4545C25.8687 25.1589 26.2045 25.4947 26.2045 25.9089C26.2045 26.3231 25.8687 26.6589 25.4545 26.6589H14.5454C14.1312 26.6589 13.7954 26.3231 13.7954 25.9089ZM14.5454 21.5228C14.1312 21.5228 13.7954 21.8586 13.7954 22.2728C13.7954 22.687 14.1312 23.0228 14.5454 23.0228H25.4545C25.8687 23.0228 26.2045 22.687 26.2045 22.2728C26.2045 21.8586 25.8687 21.5228 25.4545 21.5228H14.5454ZM13.7954 18.6363C13.7954 18.2221 14.1312 17.8863 14.5454 17.8863H25.4545C25.8687 17.8863 26.2045 18.2221 26.2045 18.6363C26.2045 19.0505 25.8687 19.3863 25.4545 19.3863H14.5454C14.1312 19.3863 13.7954 19.0505 13.7954 18.6363ZM14.5454 14.2502C14.1312 14.2502 13.7954 14.586 13.7954 15.0002C13.7954 15.4144 14.1312 15.7502 14.5454 15.7502H20C20.4142 15.7502 20.75 15.4144 20.75 15.0002C20.75 14.586 20.4142 14.2502 20 14.2502H14.5454Z" fill="currentColor"/>
   </svg>`;
  }

  static clientIcon(): string {
    return `<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M22.2124 15.7624C22.2124 18.4062 20.0489 20.5248 17.3493 20.5248C14.6507 20.5248 12.4863 18.4062 12.4863 15.7624C12.4863 13.1187 14.6507 11 17.3493 11C20.0489 11 22.2124 13.1187 22.2124 15.7624ZM10 25.9174C10 23.47 13.3855 22.8577 17.3493 22.8577C21.3347 22.8577 24.6987 23.4911 24.6987 25.9404C24.6987 28.3877 21.3131 29 17.3493 29C13.364 29 10 28.3666 10 25.9174ZM24.1734 15.8487C24.1734 17.1951 23.7605 18.4513 23.0364 19.4948C22.9611 19.6021 23.0276 19.7468 23.1587 19.7698C23.3407 19.7995 23.5276 19.8177 23.7184 19.8216C25.6167 19.8704 27.3202 18.6736 27.7908 16.8712C28.4885 14.1968 26.4415 11.7954 23.8339 11.7954C23.5511 11.7954 23.2801 11.8242 23.0159 11.8769C22.9797 11.8845 22.9405 11.9018 22.921 11.9325C22.8955 11.9717 22.9141 12.0225 22.9396 12.0561C23.7233 13.1322 24.1734 14.4421 24.1734 15.8487ZM27.3173 21.7023C28.5932 21.9466 29.4317 22.444 29.7791 23.1694C30.0736 23.7635 30.0736 24.4534 29.7791 25.0475C29.2478 26.1705 27.5335 26.5318 26.8672 26.6247C26.7292 26.6439 26.6186 26.5289 26.6333 26.3928C26.9738 23.2805 24.2664 21.8048 23.5658 21.4656C23.5364 21.4493 23.5296 21.4263 23.5325 21.411C23.5345 21.4014 23.5472 21.3861 23.5697 21.3832C25.0854 21.3545 26.7155 21.5586 27.3173 21.7023Z" fill="currentColor"/>
      </svg>
      `;
  }

  static searchIcon(): string {
    return `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
      <rect width="36" height="36" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <circle cx="17.7279" cy="17.7288" r="10.4867" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M25.0215 25.5664L29.1328 29.6671" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      </svg>
      `;
  }

  static bellIcon(): string {
    return `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
      <rect width="36" height="36" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M8.08397 20.0837V19.8282C8.12145 19.0723 8.36371 18.3399 8.78576 17.7066C9.48826 16.9457 9.96916 16.0134 10.178 15.0073C10.178 14.2298 10.178 13.4411 10.2459 12.6636C10.5968 8.92018 14.2982 6.33203 17.9542 6.33203H18.0448C21.7009 6.33203 25.4022 8.92018 25.7644 12.6636C25.8323 13.4411 25.7644 14.2298 25.821 15.0073C26.0327 16.0157 26.5131 16.951 27.2133 17.7177C27.6385 18.3454 27.8811 19.0751 27.9151 19.8282V20.0726C27.9403 21.0881 27.5906 22.0783 26.9303 22.8607C26.0578 23.7754 24.8738 24.3445 23.6025 24.4602C19.8745 24.8601 16.1132 24.8601 12.3852 24.4602C11.1153 24.3395 9.93306 23.7712 9.05742 22.8607C8.40734 22.0777 8.06229 21.0934 8.08397 20.0837Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M15.1475 28.3242C15.7299 29.0553 16.5853 29.5285 17.5243 29.639C18.4633 29.7495 19.4084 29.4882 20.1505 28.9129C20.3788 28.7428 20.5841 28.545 20.7617 28.3242" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      </svg>
      `;
  }

  static settingIcon(): string {
    return `<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M28.4023 21.58C28.76 21.77 29.036 22.07 29.2301 22.37C29.6083 22.99 29.5776 23.75 29.2097 24.42L28.4943 25.62C28.1162 26.26 27.411 26.66 26.6855 26.66C26.3278 26.66 25.9292 26.56 25.6022 26.36C25.3365 26.19 25.0299 26.13 24.7029 26.13C23.6911 26.13 22.8429 26.96 22.8122 27.95C22.8122 29.1 21.872 30 20.6968 30H19.3069C18.1215 30 17.1813 29.1 17.1813 27.95C17.1608 26.96 16.3126 26.13 15.3009 26.13C14.9636 26.13 14.657 26.19 14.4015 26.36C14.0745 26.56 13.6657 26.66 13.3183 26.66C12.5824 26.66 11.8773 26.26 11.4992 25.62L10.794 24.42C10.4159 23.77 10.3955 22.99 10.7736 22.37C10.9371 22.07 11.2437 21.77 11.5911 21.58C11.8773 21.44 12.0612 21.21 12.235 20.94C12.746 20.08 12.4394 18.95 11.5707 18.44C10.559 17.87 10.2319 16.6 10.8145 15.61L11.4992 14.43C12.0919 13.44 13.3591 13.09 14.3811 13.67C15.2702 14.15 16.425 13.83 16.9462 12.98C17.1097 12.7 17.2017 12.4 17.1813 12.1C17.1608 11.71 17.2732 11.34 17.4674 11.04C17.8455 10.42 18.5302 10.02 19.2763 10H20.7172C21.4735 10 22.1582 10.42 22.5363 11.04C22.7203 11.34 22.8429 11.71 22.8122 12.1C22.7918 12.4 22.8838 12.7 23.0473 12.98C23.5685 13.83 24.7233 14.15 25.6226 13.67C26.6344 13.09 27.9118 13.44 28.4943 14.43L29.179 15.61C29.7718 16.6 29.4447 17.87 28.4228 18.44C27.5541 18.95 27.2475 20.08 27.7687 20.94C27.9322 21.21 28.1162 21.44 28.4023 21.58ZM17.1097 20.01C17.1097 21.58 18.4076 22.83 20.0121 22.83C21.6165 22.83 22.8838 21.58 22.8838 20.01C22.8838 18.44 21.6165 17.18 20.0121 17.18C18.4076 17.18 17.1097 18.44 17.1097 20.01Z" fill="currentColor"/>
      </svg>
      `;
  }

  static complianceIcon(): string {
    return `<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
      viewBox="0 0 60 60" width="40" height="30" style="enable-background:new 0 0 60 60;" xml:space="preserve">
            <style type="text/css">
               .st0{fill:#38B6FF;}
               .st1{fill:#FFFFFF;}
               .st2{fill:#3E4550;}
               .st3{fill:none;}
               .st4{fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-miterlimit:10;}
               .st5{fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:7.3333;}
            </style>
               <g>
                  <path  fill="currentColor" d="M8.63,17.36v30.41c0,3,2.44,5.45,5.45,5.45h27.74c3,0,5.45-2.44,5.45-5.45V7.95c0-3-2.44-5.45-5.45-5.45H24.09
                     v11.39c0,1.91-1.56,3.47-3.47,3.47H8.63z M40.63,39.65c0,0.82-0.67,1.49-1.49,1.49h-7.2c-0.82,0-1.49-0.67-1.49-1.49
                     c0-0.82,0.67-1.49,1.49-1.49h7.2C39.97,38.17,40.63,38.83,40.63,39.65z M39.15,33.21h-21.8c-0.82,0-1.49-0.67-1.49-1.49
                     s0.67-1.49,1.49-1.49h21.8c0.82,0,1.49,0.67,1.49,1.49S39.97,33.21,39.15,33.21z M39.15,22.31c0.82,0,1.49,0.67,1.49,1.49
                     c0,0.82-0.67,1.49-1.49,1.49h-21.8c-0.82,0-1.49-0.67-1.49-1.49c0-0.82,0.67-1.49,1.49-1.49H39.15z"/>
                  <path  fill="currentColor" d="M21.11,13.89V3.12c-0.44,0.23-0.86,0.52-1.23,0.87l-9.55,9.04c-0.42,0.4-0.77,0.86-1.04,1.36h11.33
                     C20.89,14.39,21.11,14.17,21.11,13.89L21.11,13.89z"/>
               </g>
               <rect x="15.37" y="19.2" fill="currentColor" width="26.27" height="22.51"/>
               <path class="st1" d="M21.35,21.31h21.8c0.82,0,1.49,0.67,1.49,1.49v0c0,0.82-0.67,1.49-1.49,1.49h-21.8c-0.82,0-1.49-0.67-1.49-1.49
                  v0C19.86,21.98,20.53,21.31,21.35,21.31z"/>
               <path class="st1" d="M21.35,28.82h21.8c0.82,0,1.49,0.67,1.49,1.49v0c0,0.82-0.67,1.49-1.49,1.49h-21.8c-0.82,0-1.49-0.67-1.49-1.49
                  v0C19.86,29.48,20.53,28.82,21.35,28.82z"/>
               <path class="st1" d="M21.35,36.74h7.2c0.82,0,1.49,0.67,1.49,1.49l0,0c0,0.82-0.67,1.49-1.49,1.49h-7.2c-0.82,0-1.49-0.67-1.49-1.49
                  l0,0C19.86,37.41,20.53,36.74,21.35,36.74z"/>
               <g>
                  <circle class="st2" cx="44.45" cy="48.92" r="10.32"/>
               </g>
               <path class="st3" d="M11.45,13.21h44v44h-44V13.21z"/>
               <g>
                  <polyline class="st4" points="41.29,49.55 44.45,52.71 47.61,49.55 	"/>
                  <line class="st4" x1="44.45" y1="45.12" x2="44.45" y2="52.71"/>
               </g>
               <path class="st3" d="M-13.52,23.5h44v44h-44V23.5z"/>
               <path class="st5" d="M12,23.1l1.6,1.6l3.19-3.19"/>
               <path class="st5" d="M12,30.54l1.6,1.6l3.19-3.19"/>
               <path class="st5" d="M12,37.89l1.6,1.6l3.19-3.19"/>
               </svg>
  `;
  }

  static workspaceIcon(): string {
    return `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="36" height="36">
      <rect width="36" height="36" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <path d="M23.455 23.9077H12.5459" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M23.455 20.271H12.5459" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M23.455 16.6381H12.5459" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M18.0004 13.0014H12.5459" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M22.5911 8C22.5911 8 13.3777 8.00435 13.3633 8.00435C10.051 8.02281 8 9.99565 8 13.0049V22.9951C8 26.0196 10.0666 28 13.4077 28C13.4077 28 22.6199 27.9967 22.6355 27.9967C25.9478 27.9783 28 26.0043 28 22.9951V13.0049C28 9.98045 25.9322 8 22.5911 8Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      </svg>
      `;
  }

  static tableViewIcon(): string {
    return `<svg width="21" height="21" viewBox="0 0 23 19"  fill="currentColor" class="bi bi-kanban" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.47266 0.296875C6.38672 0.210938 6.25781 0.125 6.12891 0.125C5.95703 0.125 5.82812 0.210938 5.74219 0.296875L2.99219 3.00391L2.04688 2.05859C1.96094 1.97266 1.83203 1.92969 1.66016 1.92969C1.53125 1.92969 1.40234 1.97266 1.31641 2.05859L0.628906 2.74609C0.542969 2.83203 0.457031 2.96094 0.457031 3.08984C0.457031 3.26172 0.542969 3.39062 0.628906 3.47656L2.69141 5.49609C2.77734 5.58203 2.90625 5.66797 3.03516 5.66797C3.20703 5.66797 3.33594 5.58203 3.42188 5.49609L4.10938 4.85156L7.20312 1.71484C7.28906 1.62891 7.375 1.5 7.375 1.37109C7.375 1.24219 7.28906 1.11328 7.20312 0.984375L6.47266 0.296875ZM6.47266 7.12891C6.38672 7.04297 6.25781 7 6.12891 7C5.95703 7 5.82812 7.04297 5.74219 7.12891L2.99219 9.87891L2.04688 8.93359C1.96094 8.84766 1.83203 8.76172 1.66016 8.76172C1.53125 8.76172 1.40234 8.84766 1.31641 8.93359L0.628906 9.57812C0.542969 9.70703 0.457031 9.83594 0.457031 9.96484C0.457031 10.0938 0.542969 10.2227 0.628906 10.3086L2.69141 12.3711C2.77734 12.457 2.90625 12.543 3.03516 12.543C3.20703 12.543 3.33594 12.457 3.42188 12.3711L4.10938 11.6836L7.20312 8.58984C7.28906 8.50391 7.375 8.375 7.375 8.24609C7.375 8.07422 7.28906 7.94531 7.20312 7.85938L6.47266 7.12891ZM3.25 14.5625C2.08984 14.5625 1.14453 15.5078 1.14453 16.625C1.14453 17.7852 2.08984 18.6875 3.25 18.6875C4.36719 18.6875 5.3125 17.7852 5.3125 16.625C5.3125 15.5078 4.36719 14.5625 3.25 14.5625ZM21.8125 15.25H9.4375C9.05078 15.25 8.75 15.5938 8.75 15.9375V17.3125C8.75 17.6992 9.05078 18 9.4375 18H21.8125C22.1562 18 22.5 17.6992 22.5 17.3125V15.9375C22.5 15.5938 22.1562 15.25 21.8125 15.25ZM21.8125 1.5H9.4375C9.05078 1.5 8.75 1.84375 8.75 2.1875V3.5625C8.75 3.94922 9.05078 4.25 9.4375 4.25H21.8125C22.1562 4.25 22.5 3.94922 22.5 3.5625V2.1875C22.5 1.84375 22.1562 1.5 21.8125 1.5ZM21.8125 8.375H9.4375C9.05078 8.375 8.75 8.71875 8.75 9.0625V10.4375C8.75 10.8242 9.05078 11.125 9.4375 11.125H21.8125C22.1562 11.125 22.5 10.8242 22.5 10.4375V9.0625C22.5 8.71875 22.1562 8.375 21.8125 8.375Z"/>
      </svg>`;
  }

  static kanbanIcon(): string {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-kanban" viewBox="0 0 20 20">
      <path d="M13.5 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1h-11a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h11zm-11-1a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2h-11z"/>
      <path d="M6.5 3a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1V3zm-4 0a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1V3zm8 0a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1V3z"/>
      </svg>`;
  }

  static createWorkspaceIcon(): string {
    return `<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="31" cy="31" r="30.5" fill="#3499D7"/>
      <mask id="path-2-outside-1" maskUnits="userSpaceOnUse" x="28.8887" y="19" width="4" height="24" fill="black">
      <rect fill="white" x="28.8887" y="19" width="4" height="24"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M30.9998 41V21Z"/>
      </mask>
      <path d="M29.4998 41C29.4998 41.8284 30.1714 42.5 30.9998 42.5C31.8282 42.5 32.4998 41.8284 32.4998 41H29.4998ZM32.4998 21C32.4998 20.1716 31.8282 19.5 30.9998 19.5C30.1714 19.5 29.4998 20.1716 29.4998 21H32.4998ZM32.4998 41V21H29.4998V41H32.4998Z" fill="white" mask="url(#path-2-outside-1)"/>
      <mask id="path-4-outside-2" maskUnits="userSpaceOnUse" x="19" y="28.8892" width="24" height="4" fill="black">
      <rect fill="white" x="19" y="28.8892" width="24" height="4"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M41 31.0003H21Z"/>
      </mask>
      <path d="M41 32.5003C41.8284 32.5003 42.5 31.8287 42.5 31.0003C42.5 30.1718 41.8284 29.5003 41 29.5003V32.5003ZM21 29.5003C20.1716 29.5003 19.5 30.1718 19.5 31.0003C19.5 31.8287 20.1716 32.5003 21 32.5003V29.5003ZM41 29.5003H21V32.5003H41V29.5003Z" fill="white" mask="url(#path-4-outside-2)"/>
      </svg>
      `;
  }

  static cloneWorkspaceIcon(): string {
    return `<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="31" cy="31" r="30.5" fill="#3499D7"/>
      <path d="M26 24.607C26 21.837 27.709 20.021 30.469 20.004C30.481 20.004 38.158 20 38.158 20C40.942 20 42.665 21.823 42.665 24.607V33.803C42.665 36.573 40.955 38.39 38.195 38.407" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M32.158 25C32.158 25 24.481 25.004 24.469 25.004C21.709 25.021 20 26.837 20 29.607V38.803C20 41.587 21.722 43.41 24.506 43.41C24.506 43.41 32.182 43.407 32.195 43.407C34.955 43.39 36.665 41.573 36.665 38.803V29.607C36.665 26.823 34.942 25 32.158 25Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      `;
  }

  static editIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.4255 4.03223H8.24957C5.63765 4.03223 4 5.87806 4 8.49123V15.5405C4 18.1537 5.63001 19.9995 8.24957 19.9995H15.7447C18.3651 19.9995 19.9951 18.1537 19.9951 15.5405V12.1252" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M9.16218 10.9276L15.5098 4.5914C16.3006 3.80287 17.5823 3.80287 18.3731 4.5914L19.4068 5.62327C20.1976 6.41264 20.1976 7.69294 19.4068 8.48147L13.0287 14.8482C12.683 15.1933 12.2141 15.3875 11.7248 15.3875H8.54297L8.62281 12.1825C8.6347 11.711 8.82752 11.2617 9.16218 10.9276Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M14.5469 5.57031L18.4253 9.44173" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      `;
  }

  static archiveIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.9 4H20.1L21 8H3L3.9 4Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M5 8H19V18C19 19.1046 18.1046 20 17 20H7C5.89543 20 5 19.1046 5 18V8Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 13V15H15V13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      `;
  }

  static lockIcon(): string {
    return `<svg width="17" height="21" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.1728 7.69804V5.55104C13.1728 3.03804 11.1348 1.00004 8.62176 1.00004C6.10876 0.989045 4.06276 3.01704 4.05176 5.53104V5.55104V7.69804" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M12.433 19.498H4.792C2.698 19.498 1 17.801 1 15.706V11.417C1 9.322 2.698 7.625 4.792 7.625H12.433C14.527 7.625 16.225 9.322 16.225 11.417V15.706C16.225 17.801 14.527 19.498 12.433 19.498Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M8.61279 12.4531V14.6741" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      `;
  }

  static lockBlackIcon(): string {
    return `<svg width="17" height="30" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.1728 7.69804V5.55104C13.1728 3.03804 11.1348 1.00004 8.62176 1.00004C6.10876 0.989045 4.06276 3.01704 4.05176 5.53104V5.55104V7.69804" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M12.433 19.498H4.792C2.698 19.498 1 17.801 1 15.706V11.417C1 9.322 2.698 7.625 4.792 7.625H12.433C14.527 7.625 16.225 9.322 16.225 11.417V15.706C16.225 17.801 14.527 19.498 12.433 19.498Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M8.61279 12.4531V14.6741" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      `;
  }

  static unlockIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.2487 5.97921C15.6408 4.24541 13.9728 3.00004 12.0103 3.00004C9.53441 2.99031 7.51865 4.96345 7.50781 7.40847V7.42793V9.51685" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M15.764 21.0001H8.23596C6.17291 21.0001 4.5 19.348 4.5 17.3097V13.1377C4.5 11.0993 6.17291 9.44727 8.23596 9.44727H15.764C17.8271 9.44727 19.5 11.0993 19.5 13.1377V17.3097C19.5 19.348 17.8271 21.0001 15.764 21.0001Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12.0004 14.1426V16.3045" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      `;
  }

  static keyIcon(): string {
    return `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.3684 14.2632C16.0309 14.2632 19 11.2941 19 7.63158C19 3.96906 16.0309 1 12.3684 1C8.7059 1 5.73684 3.96906 5.73684 7.63158C5.73684 8.19138 5.8062 8.73498 5.9368 9.25425L1 14.2632V19H6.21053V17.1053H8.10526V15.2105C9.62105 15.5895 10.3158 14.5538 10.4737 13.9885C11.074 14.1672 11.71 14.2632 12.3684 14.2632Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="13.5" cy="6.5" r="1.75" stroke="currentColor" stroke-width="1.5"/>
      </svg>
      `;
  }

  static resetPasswordIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
      <rect width="24" height="24" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <path d="M21.1498 17.2058H19.5501C19.219 17.2058 18.9502 16.937 18.9502 16.6059C18.9502 16.2748 19.219 16.006 19.5501 16.006H20.5499V15.0062C20.5499 14.675 20.8187 14.4062 21.1498 14.4062C21.481 14.4062 21.7497 14.675 21.7497 15.0062V16.6059C21.7497 16.937 21.481 17.2058 21.1498 17.2058Z" fill="white"/>
      <path d="M14.3499 21.6003C14.0188 21.6003 13.75 21.3316 13.75 21.0004V19.4007C13.75 19.0695 14.0188 18.8008 14.3499 18.8008H15.9496C16.2808 18.8008 16.5496 19.0695 16.5496 19.4007C16.5496 19.7318 16.2808 20.0006 15.9496 20.0006H14.9498V21.0004C14.9498 21.3316 14.6811 21.6003 14.3499 21.6003Z" fill="white"/>
      <path d="M14.503 17.5994C14.4454 17.5994 14.3862 17.5906 14.3286 17.5738C14.0119 17.4771 13.8327 17.1419 13.9287 16.8252C14.443 15.135 15.9788 14 17.7505 14C19.2303 14 20.5796 14.8119 21.2723 16.1181C21.4267 16.4108 21.3155 16.774 21.0228 16.9299C20.7276 17.0827 20.3669 16.9731 20.2109 16.6804C19.7286 15.7669 18.7847 15.1998 17.7505 15.1998C16.5107 15.1998 15.4364 15.9933 15.0773 17.1739C14.9989 17.4331 14.7606 17.5994 14.503 17.5994Z" fill="white"/>
      <path d="M17.7491 22.0021C16.2694 22.0021 14.92 21.1902 14.2273 19.884C14.0729 19.5913 14.1841 19.2281 14.4768 19.0722C14.7712 18.9186 15.1327 19.029 15.2887 19.3217C15.771 20.2352 16.7149 20.8023 17.7491 20.8023C18.9817 20.8023 20.0568 20.008 20.4239 18.8242C20.5223 18.5082 20.8574 18.3307 21.1742 18.4299C21.4909 18.5274 21.6669 18.8642 21.5685 19.1801C21.0454 20.8679 19.5104 22.0021 17.7491 22.0021Z" fill="white"/>
      <path d="M14.6742 6.5C14.6742 3.5 12.3642 2.00004 10.3722 2.00004C7.8592 1.99004 5.8132 4.01804 5.8022 6.53104V6.55104" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.75 21.249H6.542C4.03366 21.249 2 19.2151 2 16.706V12.418C2 9.90892 4.03366 7.875 6.542 7.875H14.183C16.6913 7.875 18.725 9.90892 18.725 12.418C18.725 12.4967 18.7207 12.59 18.7117 12.6964C18.2463 12.5684 17.7561 12.5 17.25 12.5C14.2124 12.5 11.75 14.9624 11.75 18C11.75 19.1753 12.1186 20.2645 12.7466 21.1582C12.4296 21.2175 12.0976 21.249 11.75 21.249ZM11.1128 13.4531C11.1128 13.0389 10.777 12.7031 10.3628 12.7031C9.94858 12.7031 9.61279 13.0389 9.61279 13.4531V15.6751C9.61279 16.0893 9.94858 16.4251 10.3628 16.4251C10.777 16.4251 11.1128 16.0893 11.1128 15.6751V13.4531Z" fill="white"/>
      </g>
      </svg>
      `;
  }

  static assignedToIcon(): string {
    return `<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M35.4434 31.1887H15.5827C11.6717 31.1887 8.4895 28.0065 8.4895 24.0956C8.4895 23.7035 8.80675 23.3862 9.19883 23.3862C9.59092 23.3862 9.90816 23.7035 9.90816 24.0956C9.90816 27.2251 12.4538 29.7701 15.5827 29.7701H35.4435C38.5731 29.7701 41.118 27.2251 41.118 24.0956C41.118 23.7035 41.4353 23.3862 41.8273 23.3862C42.2194 23.3862 42.5367 23.7035 42.5367 24.0956C42.5366 28.0065 39.3544 31.1887 35.4434 31.1887ZM36.1528 26.9328H14.8733C14.4813 26.9328 14.164 26.6156 14.164 26.2235C14.164 25.8314 14.4813 25.5142 14.8733 25.5142H36.1528C36.5449 25.5142 36.8621 25.8314 36.8621 26.2235C36.8621 26.6156 36.5449 26.9328 36.1528 26.9328Z" fill="#38B6FF"/>
      <path d="M36.8626 30.413C36.0791 30.413 35.444 29.7778 35.444 28.9943V19.8397C35.444 14.3641 30.9886 9.90932 25.5136 9.90932C20.0379 9.90932 15.5831 14.364 15.5831 19.8397V29.7037C15.5831 30.0954 15.2656 30.413 14.8738 30.413C14.4821 30.413 14.1646 30.0954 14.1646 29.7037V19.8397C14.1646 13.582 19.2559 8.49072 25.5136 8.49072C31.7713 8.49072 36.8626 13.582 36.8626 19.8397V30.4129C36.8626 30.413 36.8626 30.413 36.8626 30.413Z" fill="#38B6FF"/>
      <path d="M27.7642 36.6328C27.5239 36.1522 27.0327 35.8486 26.4953 35.8486H23.9929C23.4556 35.8486 22.9644 36.1522 22.7241 36.6328C22.2524 37.5761 22.9383 38.6859 23.9929 38.6859H26.4953C27.5499 38.6859 28.2358 37.5761 27.7642 36.6328Z" fill="#38B6FF"/>
      <circle cx="25" cy="25" r="23" stroke="currentColor" stroke-width="4"/>
      </svg>
      `;
  }

  static exportIcon(): string {
    return `<svg width="26" height="32" viewBox="-3 -6 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.5251 1.2595H15.8483L8.25092 8.86054C8.0049 9.10656 8.0049 9.50681 8.25092 9.75284C8.3721 9.87401 8.53367 9.93644 8.69524 9.93644C8.8568 9.93644 9.01837 9.87401 9.13955 9.75284L16.7406 2.15179V5.47495C16.7406 5.82379 17.0233 6.10654 17.3685 6.10654C17.7173 6.10654 18.0001 5.82379 18.0001 5.47495V0.631584C18.0001 0.282744 17.7173 0 17.3685 0H12.5251C12.1763 0 11.8935 0.282744 11.8935 0.631584C11.8972 0.976752 12.1763 1.2595 12.5251 1.2595Z" fill="black"/>
      <path d="M3.77849 18.0002H14.218C16.3037 18.0002 17.9965 16.3037 17.9965 14.2217V9.30485C17.9965 8.95601 17.7137 8.67694 17.3649 8.67694C17.0161 8.67694 16.737 8.95968 16.737 9.30485V14.218C16.737 15.606 15.606 16.737 14.218 16.737H3.77849C2.39047 16.737 1.2595 15.606 1.2595 14.218V5.92661V3.78216C1.2595 2.39414 2.39047 1.26317 3.77849 1.26317H8.65123C9.00008 1.26317 9.27915 0.980424 9.27915 0.631584C9.27915 0.282744 8.9964 0 8.65123 0H3.77849C1.69279 0 0 1.69646 0 3.77849V5.92294V14.218C0 16.3037 1.69646 18.0002 3.77849 18.0002Z" fill="black"/>
      </svg>
      `;
  }

  static unlockBlackIcon(): string {
    return `<svg width="27" height="33" viewBox="0 0 27 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="27" height="24">
      <rect width="27" height="24" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <path d="M3.0528 5.06204C3.6698 3.28004 5.3628 2.00004 7.3548 2.00004C9.8678 1.99004 11.9138 4.01804 11.9248 6.53104V6.55104V8.69804" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M18.433 20.499H10.792C8.698 20.499 7 18.801 7 16.706V12.418C7 10.323 8.698 8.625 10.792 8.625H18.433C20.527 8.625 22.225 10.323 22.225 12.418V16.706C22.225 18.801 20.527 20.499 18.433 20.499Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M14.6133 13.4531V15.6751" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      </svg>
      `;
  }

  static countryIcon(): string {
    return `<svg width="29" height="19" viewBox="0 0 29 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M28.0775 0.419922H14.7002L14.2509 0.773398L13.8495 0.419922H9.16477L8.2422 1.23887H6.85829L5.93572 0.419922H1.38511C1.34361 0.419922 1.30216 0.421989 1.26094 0.426178L0.836914 0.987505V17.4185C0.836914 17.928 1.24999 18.3411 1.75948 18.3411H28.4415L29.0001 17.9489V1.34255C29.0001 0.832944 28.587 0.419922 28.0775 0.419922Z" fill="#5055A0"/>
      <path d="M1.32296 15.181V1.14799L0.462562 1.00879C0.422399 1.11224 0.400391 1.22474 0.400391 1.34238V3.4038L0.744873 3.79895V6.34986L0.400391 6.63279V9.09673L0.744873 9.34764L0.400391 9.64554V17.9487C0.400391 18.4582 0.813468 18.8713 1.32296 18.8713H28.0776C28.5871 18.8713 29.0002 18.4582 29.0002 17.9487H4.09072C2.56213 17.9487 1.32296 16.7096 1.32296 15.181Z" fill="#3F3F8F"/>
      <path d="M8.70336 3.87955L8.24213 0.419922H6.85822L6.39694 3.87955H0.875V6.18602H6.39694L6.85822 9.64571H8.24213L8.70336 6.18602L14.7001 5.71027V4.32642L8.70336 3.87955Z" fill="#E5646E"/>
      <path d="M1.32296 3.87988L0.400391 4.32675V5.71061L1.32296 6.18636V3.87988Z" fill="#DB4655"/>
      <path d="M14.7002 0.419922H13.8495L9.22441 3.40385H9.16476V0.419922H8.24219V3.87955C8.24219 4.12633 8.44227 4.32642 8.68906 4.32642H14.7002V3.40385H10.9261L14.7002 0.969016V0.419922Z" fill="#F2F2F6"/>
      <path d="M5.93576 0.419922V3.40385H5.87632L1.32286 0.466117L0.847168 0.847858L1.32286 1.56403L4.17463 3.40391H1.32286L0.875097 3.87955L1.32286 4.32642H6.41145C6.65824 4.32642 6.85832 4.12633 6.85832 3.87955V0.419922H5.93576Z" fill="#F2F2F6"/>
      <path d="M0.931152 6.2202L1.32306 6.63345H4.21968L1.32306 8.50221L1.0103 9.12024L1.32306 9.60012L5.92137 6.6335H5.93601V9.64632H6.85857V6.15781C6.85857 5.91102 6.65849 5.71094 6.4117 5.71094H1.32306L0.931152 6.2202Z" fill="#F2F2F6"/>
      <path d="M8.24219 9.64637H9.16476V6.63356H9.1795L13.8493 9.64632H14.7002V9.09739L10.8812 6.6335H14.7002V5.71094H8.68906C8.44227 5.71094 8.24219 5.91102 8.24219 6.15781V9.64637Z" fill="#F2F2F6"/>
      <path d="M1.3228 0.465721L1.26085 0.425781C0.896315 0.450471 0.588755 0.682342 0.462402 1.0085L1.3228 1.56363V0.465721Z" fill="#E1E1E6"/>
      <path d="M0.400391 3.40332H1.32296V4.32589H0.400391V3.40332Z" fill="#E1E1E6"/>
      <path d="M1.32296 8.50195L0.400391 9.09713V9.64595H1.25146L1.32296 9.59981V8.50195Z" fill="#E1E1E6"/>
      <path d="M0.400391 5.71094H1.32296V6.6335H0.400391V5.71094Z" fill="#E1E1E6"/>
      <path d="M9.81813 13.5798L8.77083 13.5705L8.25659 12.4452C8.208 12.2995 8.00193 12.2995 7.95334 12.4452L7.57802 13.5705L6.3918 13.5798C6.23824 13.5809 6.17456 13.7769 6.29807 13.8682L7.25236 14.5728L6.89453 15.7038C6.84817 15.8503 7.01491 15.9714 7.13986 15.8821L8.10494 15.1923L9.07001 15.8821C9.19497 15.9714 9.36171 15.8503 9.31535 15.7038L8.95751 14.5728L9.91181 13.8682C10.0354 13.7769 9.97175 13.581 9.81813 13.5798Z" fill="#F2F2F6"/>
      <path d="M22.5718 14.4996L22.2684 14.4969L22.0644 14.0503C22.0185 13.95 21.9184 13.8857 21.8082 13.8857C21.687 13.8857 21.5794 13.9633 21.5411 14.0782L21.4015 14.4968L20.9981 14.5C20.7394 14.502 20.6322 14.832 20.8403 14.9857L21.1648 15.2254L21.0431 15.61C20.965 15.8566 21.2458 16.0606 21.4562 15.9102L21.7844 15.6756L22.1127 15.9102C22.3231 16.0606 22.6038 15.8566 22.5258 15.61L22.4041 15.2254L22.7293 14.9852C22.9373 14.8317 22.8303 14.5019 22.5718 14.4996Z" fill="#F2F2F6"/>
      <path d="M18.874 8.96344L18.5706 8.96076L18.3666 8.51417C18.3208 8.4139 18.2207 8.34961 18.1105 8.34961C17.9893 8.34961 17.8817 8.42714 17.8434 8.5421L17.7038 8.96071L17.3003 8.96384C17.0417 8.96585 16.9344 9.29586 17.1425 9.44953L17.467 9.68922L17.3453 10.0739C17.2673 10.3204 17.548 10.5244 17.7585 10.3741L18.0867 10.1394L18.4149 10.3741C18.6253 10.5244 18.9061 10.3205 18.828 10.0739L18.7063 9.68922L19.0315 9.44908C19.2395 9.29553 19.1325 8.96568 18.874 8.96344Z" fill="#F2F2F6"/>
      <path d="M26.25 7.09137L25.9466 7.08869L25.7426 6.6421C25.6968 6.54183 25.5967 6.47754 25.4865 6.47754C25.3652 6.47754 25.2577 6.55507 25.2193 6.67003L25.0797 7.08864L24.6763 7.09176C24.4177 7.09378 24.3104 7.42379 24.5185 7.57746L24.843 7.81715L24.7213 8.2018C24.6433 8.44836 24.9241 8.65235 25.1344 8.50198L25.4627 8.26737L25.7909 8.50198C26.0013 8.65235 26.2821 8.44841 26.204 8.2018L26.0823 7.81715L26.4075 7.57701C26.6155 7.42351 26.5085 7.09366 26.25 7.09137Z" fill="#F2F2F6"/>
      <path d="M22.562 4.30622L22.2586 4.30354L22.0546 3.85694C22.0088 3.75668 21.9087 3.69238 21.7985 3.69238C21.6773 3.69238 21.5697 3.76992 21.5314 3.88487L21.3918 4.30348L20.9883 4.30661C20.7297 4.30862 20.6224 4.63863 20.8305 4.7923L21.155 5.03199L21.0333 5.41664C20.9553 5.6632 21.236 5.8672 21.4464 5.71683L21.7747 5.48222L22.1029 5.71683C22.3133 5.8672 22.5941 5.66326 22.516 5.41664L22.3943 5.03199L22.7195 4.79186C22.9275 4.63835 22.8205 4.30851 22.562 4.30622Z" fill="#F2F2F6"/>
      <path d="M23.4431 9.82296L23.2752 9.82151L23.1623 9.57445C23.137 9.51898 23.0816 9.4834 23.0206 9.4834C22.9536 9.4834 22.8941 9.5263 22.8728 9.58992L22.7956 9.82151L22.5724 9.82324C22.4293 9.82436 22.3699 10.007 22.4851 10.0919L22.6646 10.2245L22.5973 10.4374C22.5541 10.5738 22.7095 10.6866 22.8259 10.6034L23.0075 10.4736L23.1891 10.6034C23.3055 10.6866 23.4609 10.5738 23.4177 10.4374L23.3504 10.2245L23.5303 10.0917C23.6453 10.0067 23.5861 9.82425 23.4431 9.82296Z" fill="#F2F2F6"/>
      </svg>
      `;
  }

  static restorewhiteIcon(): string {
    return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
      <rect width="24" height="24" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <rect x="2" y="3" width="20" height="4" rx="1" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M3.5 9.5V19.5C3.5 20.0523 3.94772 20.5 4.5 20.5H19.5C20.0523 20.5 20.5 20.0523 20.5 19.5V9.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      <path d="M12.5 19C10.45 19 8.695 17.765 7.91997 16H9.62997C10.265 16.905 11.31 17.5 12.495 17.5C14.43 17.5 15.995 15.935 15.995 14C15.995 12.065 14.43 10.5 12.495 10.5C11.1399 10.5 9.97998 11.2749 9.39995 12.4049L10.9999 14H7V9.99998L8.29998 11.3C9.19 9.92001 10.735 9 12.5 9C15.26 9 17.5 11.24 17.5 14C17.5 16.76 15.26 19 12.5 19Z" fill="white"/>
      </svg>`;
  }

  static archivewhiteIcon(): string {
    return `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
      <rect width="24" height="24" fill="white"/>
      </mask>
      <g mask="url(#mask0)">
      <rect x="2" y="3" width="20" height="4" rx="1" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <rect x="8" y="11" width="8" height="3" rx="1" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M3.5 9.5V19.5C3.5 20.0523 3.94772 20.5 4.5 20.5H19.5C20.0523 20.5 20.5 20.0523 20.5 19.5V9.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      </svg>`;
  }

  static sendWhiteIcon(): string {
    return `<svg width="24" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
     <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="20">
     <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1426 0L20.0421 9.89949L10.1426 19.799L0.243083 9.89949L10.1426 0Z" fill="white"/>
     </mask>
     <g mask="url(#mask0)">
     <path d="M19.3201 9.9253C19.3266 9.42273 19.0544 8.96083 18.6123 8.71839L8.31012 3.05807C7.84868 2.79728 7.30473 2.82932 6.86992 3.12623C6.42714 3.42814 6.2005 4.14684 6.32428 4.66593L7.28603 8.69531C7.38476 9.10849 7.75456 9.39942 8.17999 9.3976L13.9131 9.37981C14.2067 9.37393 14.4448 9.61203 14.4389 9.90561C14.438 10.1942 14.2034 10.4288 13.9099 10.4347L8.17126 10.448C7.74583 10.4488 7.37423 10.7411 7.27293 11.1549L6.27717 15.2003C6.15626 15.6783 6.29411 16.1556 6.63141 16.4929C6.67109 16.5326 6.71574 16.5772 6.76041 16.6119C7.19723 16.9489 7.76931 16.9919 8.25775 16.7316L18.5949 11.1267C19.0384 10.8915 19.3136 10.4279 19.3201 9.9253Z" fill="white"/>
     </g>
     </svg>`;
  }

  static notesIcon(): string {
    return `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 21L18.5 24.5L25 18" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M5 4H23V15.7638C22.9588 15.8008 22.9183 15.8391 22.8787 15.8787L18.5 20.2574L17.1213 18.8787C15.9497 17.7071 14.0503 17.7071 12.8787 18.8787C11.7071 20.0503 11.7071 21.9497 12.8787 23.1213L15.7574 26H8C6.34315 26 5 24.6569 5 23V4ZM9 8C9.55228 8 10 7.55228 10 7C10 6.44772 9.55228 6 9 6C8.44772 6 8 6.44772 8 7C8 7.55228 8.44772 8 9 8ZM12 6C11.4477 6 11 6.44772 11 7C11 7.55228 11.4477 8 12 8H19C19.5523 8 20 7.55228 20 7C20 6.44772 19.5523 6 19 6H12ZM9 12C9.55228 12 10 11.5523 10 11C10 10.4477 9.55228 10 9 10C8.44772 10 8 10.4477 8 11C8 11.5523 8.44772 12 9 12ZM12 10C11.4477 10 11 10.4477 11 11C11 11.5523 11.4477 12 12 12H19C19.5523 12 20 11.5523 20 11C20 10.4477 19.5523 10 19 10H12ZM10 15C10 15.5523 9.55228 16 9 16C8.44772 16 8 15.5523 8 15C8 14.4477 8.44772 14 9 14C9.55228 14 10 14.4477 10 15ZM11 15C11 14.4477 11.4477 14 12 14H19C19.5523 14 20 14.4477 20 15C20 15.5523 19.5523 16 19 16H12C11.4477 16 11 15.5523 11 15Z" fill="currentColor"/>
      </svg>
      `;
  }

  static logoutWhiteIcon(): string {
    return `<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path d="M18.8597 14.0225H3.26758" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
   <path d="M15.0684 10.2461L18.8599 14.0221L15.0684 17.7981" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
   <path d="M9.39648 8.0226V6.81444C9.39648 4.17927 11.5318 2.04395 14.1683 2.04395H20.4927C23.1214 2.04395 25.2515 4.17409 25.2515 6.80278V21.2282C25.2515 23.8634 23.1149 26 20.4797 26H14.154C11.5266 26 9.39648 23.8686 9.39648 21.2412V20.0213" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
   </svg>`;
  }

  static advisorDetailsIcon(): string {
    return `<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path d="M15.216 8.31C16.44 8.31 17.418 8.646 18.15 9.318C18.894 9.99 19.266 10.908 19.266 12.072C19.266 13.284 18.882 14.196 18.114 14.808C17.346 15.42 16.326 15.726 15.054 15.726L14.982 17.148H13.2L13.11 14.322H13.704C14.868 14.322 15.756 14.166 16.368 13.854C16.992 13.542 17.304 12.948 17.304 12.072C17.304 11.436 17.118 10.938 16.746 10.578C16.386 10.218 15.882 10.038 15.234 10.038C14.586 10.038 14.076 10.212 13.704 10.56C13.332 10.908 13.146 11.394 13.146 12.018H11.22C11.22 11.298 11.382 10.656 11.706 10.092C12.03 9.528 12.492 9.09 13.092 8.778C13.704 8.466 14.412 8.31 15.216 8.31ZM14.064 21.126C13.692 21.126 13.38 21 13.128 20.748C12.876 20.496 12.75 20.184 12.75 19.812C12.75 19.44 12.876 19.128 13.128 18.876C13.38 18.624 13.692 18.498 14.064 18.498C14.424 18.498 14.73 18.624 14.982 18.876C15.234 19.128 15.36 19.44 15.36 19.812C15.36 20.184 15.234 20.496 14.982 20.748C14.73 21 14.424 21.126 14.064 21.126Z" fill="white"/>
   <circle cx="14" cy="14" r="13.25" stroke="white" stroke-width="1.5"/>
   </svg>
   `;
  }

  static notesBlackIcon(): string {
    return `<svg width="34" height="35" viewBox="0 -5 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0)">
      <path d="M14.1531 1.28824H12.706V0.529412C12.706 0.237035 12.469 0 12.1766 0C11.8842 0 11.6472 0.237035 11.6472 0.529412V1.28824H9.52952V0.529412C9.52952 0.237035 9.29249 0 9.00011 0C8.70774 0 8.4707 0.237035 8.4707 0.529412V1.28824H6.35305V0.529412C6.35305 0.237035 6.11602 0 5.82364 0C5.53127 0 5.29423 0.237035 5.29423 0.529412V1.28824H3.84717C3.16603 1.28824 2.61188 1.84239 2.61188 2.52353V16.7647C2.61188 17.4458 3.16603 18 3.84717 18H14.1531C14.8342 18 15.3883 17.4458 15.3883 16.7647V2.52353C15.3883 1.84239 14.8342 1.28824 14.1531 1.28824ZM14.3295 16.7647C14.3295 16.862 14.2504 16.9412 14.1531 16.9412H3.84717C3.74987 16.9412 3.6707 16.862 3.6707 16.7647V2.52353C3.6707 2.42622 3.74987 2.34706 3.84717 2.34706H5.29423V3.10588C5.29423 3.39826 5.53127 3.63529 5.82364 3.63529C6.11602 3.63529 6.35305 3.39826 6.35305 3.10588V2.34706H8.4707V3.10588C8.4707 3.39826 8.70774 3.63529 9.00011 3.63529C9.29249 3.63529 9.52952 3.39826 9.52952 3.10588V2.34706H11.6472V3.10588C11.6472 3.39826 11.8842 3.63529 12.1766 3.63529C12.469 3.63529 12.706 3.39826 12.706 3.10588V2.34706H14.1531C14.2504 2.34706 14.3295 2.42622 14.3295 2.52353V16.7647Z" fill="black"/>
      <path d="M12.1766 6.42969H5.82366C5.53129 6.42969 5.29425 6.66672 5.29425 6.9591C5.29425 7.25148 5.53129 7.48851 5.82366 7.48851H12.1766C12.469 7.48851 12.706 7.25148 12.706 6.9591C12.706 6.66672 12.469 6.42969 12.1766 6.42969Z" fill="black"/>
      <path d="M12.1766 9.61523H5.82366C5.53129 9.61523 5.29425 9.85227 5.29425 10.1446C5.29425 10.437 5.53129 10.6741 5.82366 10.6741H12.1766C12.469 10.6741 12.706 10.437 12.706 10.1446C12.706 9.85227 12.469 9.61523 12.1766 9.61523Z" fill="black"/>
      <path d="M12.1766 12.8018H5.82366C5.53129 12.8018 5.29425 13.0388 5.29425 13.3312C5.29425 13.6235 5.53129 13.8606 5.82366 13.8606H12.1766C12.469 13.8606 12.706 13.6235 12.706 13.3312C12.706 13.0388 12.469 12.8018 12.1766 12.8018Z" fill="black"/>
      </g>
      <defs>
      <clipPath id="clip0">
      <rect width="18" height="18" fill="black"/>
      </clipPath>
      </defs>
      </svg>
      `;
  }

  static eyeIcon(): string {
    return `<svg width="22" height="22" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0003 6.47002C12.4422 6.47002 13.6142 7.6431 13.6142 9.08505C13.6142 10.5259 12.4422 11.6979 11.0003 11.6979C9.5583 11.6979 8.38522 10.5259 8.38522 9.08505C8.38522 7.6431 9.5583 6.47002 11.0003 6.47002ZM11.0003 13.3242C13.3388 13.3242 15.2405 11.4225 15.2405 9.08505C15.2405 6.74648 13.3388 4.84375 11.0003 4.84375C8.66169 4.84375 6.75896 6.74648 6.75896 9.08505C6.75896 11.4225 8.66169 13.3242 11.0003 13.3242Z" fill="currentColor"/>
   <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="22" height="18">
   <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8418 0.359375H0.158407V17.8201H21.8418V0.359375Z" fill="white"/>
   </mask>
   <g mask="url(#mask0)">
   <path fill-rule="evenodd" clip-rule="evenodd" d="M20.1404 9.08917C18.1238 13.5441 14.7271 16.1927 11.0007 16.1938C7.27442 16.1927 3.8777 13.5441 1.86112 9.08917C3.8777 4.63537 7.27442 1.98673 11.0007 1.98564C14.726 1.98673 18.1238 4.63537 20.1404 9.08917ZM10.9981 17.8209H11.0025H11.0035C15.4877 17.8176 19.5143 14.6746 21.7759 9.4109C21.8638 9.20599 21.8638 8.97398 21.7759 8.76907C19.5143 3.50647 15.4866 0.363441 11.0035 0.360188C11.0014 0.359104 11.0014 0.359104 11.0003 0.360188C10.9981 0.359104 10.9981 0.359104 10.997 0.360188C6.51288 0.363441 2.48625 3.50647 0.224653 8.76907C0.135752 8.97398 0.135752 9.20599 0.224653 9.4109C2.48516 14.6746 6.51288 17.8176 10.997 17.8209H10.9981Z" fill="currentColor"/>
   </g>
   </svg>`;
  }

  static overviewIcon(): string {
    return `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 4H21.3215V8.12407C20.4119 7.72267 19.4073 7.5 18.3512 7.5C16.833 7.5 15.4212 7.96019 14.2451 8.75H7.46033C7.04611 8.75 6.71033 9.08579 6.71033 9.5C6.71033 9.91421 7.04611 10.25 7.46033 10.25H12.6044C12.1203 10.8468 11.7255 11.5204 11.4407 12.25H7.46033C7.04611 12.25 6.71033 12.5858 6.71033 13C6.71033 13.4142 7.04611 13.75 7.46033 13.75H11.0283C10.9608 14.1565 10.9256 14.5741 10.9256 15C10.9256 15.2531 10.938 15.5033 10.9623 15.75H7.46033C7.04611 15.75 6.71033 16.0858 6.71033 16.5C6.71033 16.9142 7.04611 17.25 7.46033 17.25H11.2656C12.2118 20.2928 15.0263 22.5 18.3512 22.5C19.4073 22.5 20.4119 22.2773 21.3215 21.8759V23C21.3215 24.6569 19.9783 26 18.3215 26H6.5C4.84315 26 3.5 24.6569 3.5 23V4Z" fill="currentColor"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M21.7927 19.8908C20.8216 20.5892 19.6336 21 18.3507 21C15.0698 21 12.4102 18.3137 12.4102 15C12.4102 11.6863 15.0698 9 18.3507 9C21.6315 9 24.2912 11.6863 24.2912 15C24.2912 16.2958 23.8844 17.4957 23.1929 18.4766L26.2094 21.5233C26.5961 21.9138 26.5961 22.547 26.2094 22.9375C25.8227 23.328 25.1959 23.328 24.8092 22.9375L21.7927 19.8908ZM22.806 15C22.806 17.4853 20.8113 19.5 18.3506 19.5C15.89 19.5 13.8953 17.4853 13.8953 15C13.8953 12.5147 15.89 10.5 18.3506 10.5C20.8113 10.5 22.806 12.5147 22.806 15Z" fill="currentColor"/>
      </svg>
      `;
  }

  static financialsIcon(): string {
    return `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M19 26C22.866 26 26 22.866 26 19C26 15.134 22.866 12 19 12C15.134 12 12 15.134 12 19C12 22.866 15.134 26 19 26ZM19.6424 19.8207C19.7666 19.9385 19.8288 20.098 19.8288 20.2992C19.8288 20.493 19.7599 20.6476 19.6221 20.7629C19.4843 20.8757 19.2965 20.9322 19.0588 20.9322C18.7724 20.9322 18.5522 20.8549 18.3982 20.7003C18.2469 20.5433 18.1712 20.3188 18.1712 20.0268H17C17 20.5298 17.1513 20.9334 17.4539 21.2376C17.7592 21.5394 18.1888 21.7173 18.7427 21.7713V22.5H19.387V21.7676C19.8896 21.721 20.284 21.5676 20.5704 21.3076C20.8568 21.0475 21 20.7089 21 20.2918C21 20.0685 20.9622 19.8735 20.8865 19.7066C20.8109 19.5373 20.7015 19.3877 20.5583 19.2576C20.4151 19.1251 20.2394 19.0074 20.0314 18.9043C19.8234 18.7988 19.5572 18.6872 19.233 18.5694C18.9115 18.4516 18.6913 18.3363 18.5724 18.2234C18.4536 18.1106 18.3941 17.9609 18.3941 17.7744C18.3941 17.5733 18.4549 17.4162 18.5765 17.3034C18.6981 17.188 18.8696 17.1304 19.0912 17.1304C19.3154 17.1304 19.4938 17.204 19.6261 17.3512C19.7612 17.496 19.8288 17.7156 19.8288 18.01H21C21 17.5315 20.8649 17.1427 20.5947 16.8433C20.3246 16.5415 19.9504 16.36 19.4721 16.2986V15.5H18.8237V16.2876C18.3401 16.3318 17.9524 16.4863 17.6606 16.7513C17.3688 17.0163 17.2229 17.3561 17.2229 17.7708C17.2229 18.0112 17.2648 18.2185 17.3485 18.3927C17.435 18.5669 17.5539 18.7191 17.7052 18.8491C17.8592 18.9767 18.0429 19.0908 18.2563 19.1914C18.4698 19.2895 18.7237 19.3889 19.0182 19.4895C19.3127 19.5901 19.5208 19.7005 19.6424 19.8207Z" fill="currentColor"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.8947 4C7.53617 4 4 5.64868 4 7.6842C4 9.71972 7.53617 11.3684 11.8947 11.3684C12.8456 11.3684 13.7573 11.2899 14.6017 11.1461C15.6052 10.5829 16.7276 10.2065 17.9218 10.0639C19.0868 9.42206 19.7894 8.59156 19.7894 7.6842C19.7894 5.64868 16.2532 4 11.8947 4ZM10.1744 20.7721C10.4086 21.945 10.8708 23.0356 11.5128 23.9958C7.33145 23.916 4 22.5326 4 20.8426V18.7363C5.38181 19.9043 7.69471 20.5661 10.1744 20.7721ZM10.3306 16.5745C10.1152 17.3462 10 18.1596 10 19C10 19.2357 10.0091 19.4693 10.0269 19.7005C6.56915 19.3644 4 18.1168 4 16.6326V14.5264C5.41079 15.7188 7.79211 16.3836 10.3306 16.5745ZM12.6217 12.6504C11.8018 13.474 11.1404 14.4554 10.687 15.5451C6.90126 15.3302 4 14.121 4 12.6652V10.3159C5.69818 11.8639 8.80673 12.6652 11.8947 12.6652C12.1371 12.6652 12.3797 12.6603 12.6217 12.6504Z" fill="currentColor"/>
      </svg>
      `;
  }

  static copyIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path d="M7.74549 14.4872C7.74549 13.0061 7.74299 11.525 7.74624 10.0439C7.74849 9.03734 8.33748 8.24173 9.23509 8.03291C9.38552 7.9978 9.54345 7.98583 9.69814 7.98583C12.4812 7.98317 15.2642 7.98264 18.0472 7.9845C19.1862 7.9853 19.9986 8.85114 19.9991 10.062C20.0004 13.0183 20.0004 15.9744 19.9991 18.9308C19.9986 20.1291 19.18 20.9992 18.0555 20.9995C15.2669 21.0003 12.4782 21.0003 9.68964 20.9995C8.57238 20.9992 7.75049 20.135 7.74674 18.9481C7.74174 17.4611 7.74549 15.9742 7.74549 14.4872ZM9.00069 14.4923C9.00069 15.9611 9.00044 17.43 9.00094 18.8989C9.00119 19.4224 9.23134 19.6674 9.72437 19.6674C12.4899 19.6679 15.2555 19.6679 18.0212 19.6674C18.5133 19.6674 18.7442 19.421 18.7442 18.8981C18.7444 15.9603 18.7444 13.0226 18.7442 10.0851C18.7442 9.5619 18.5135 9.31718 18.0205 9.31718C15.255 9.31691 12.4892 9.31691 9.72362 9.31718C9.23034 9.31718 9.00094 9.56163 9.00094 10.0862C9.00044 11.5545 9.00069 13.0234 9.00069 14.4923Z" fill="currentColor"/>
   <path d="M16.2279 6.61883C15.8248 6.61883 15.427 6.61883 14.9997 6.61883C14.9997 6.4446 15.0002 6.27063 14.9997 6.09693C14.9984 5.59525 14.7508 5.32872 14.2832 5.32872C11.5117 5.32845 8.74042 5.32845 5.96888 5.32872C5.49609 5.32872 5.2457 5.59126 5.2457 6.08949C5.2452 9.03334 5.2452 11.9772 5.2457 14.9213C5.2457 15.4225 5.49484 15.6879 5.96263 15.6893C6.13406 15.6898 6.30573 15.6893 6.4889 15.6893C6.4889 16.13 6.4889 16.5572 6.4889 16.9844C5.26119 17.1874 4.24788 16.6043 4.02298 15.3243C4.00474 15.2208 4.00199 15.1131 4.00199 15.0072C4.00074 12.0099 3.99774 9.01285 4.00299 6.01607C4.00474 4.99169 4.64521 4.20113 5.59154 4.02929C5.71099 4.00748 5.83494 4.00668 5.95664 4.00642C8.73367 4.00535 11.5107 4.01626 14.2877 4.00003C15.2888 3.99418 16.1012 4.72968 16.2224 5.71575C16.2581 6.00516 16.2279 6.30388 16.2279 6.61883Z" fill="currentColor"/>
   </svg>
   `;
  }

  static imageLocationIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 10.3178C3.5 5.71789 7.34388 2 11.9934 2C16.6561 2 20.5 5.71789 20.5 10.3178C20.5 12.6357 19.657 14.7876 18.2695 16.6116C16.7388 18.6235 14.8522 20.3765 12.7285 21.7524C12.2425 22.0704 11.8039 22.0944 11.2704 21.7524C9.13474 20.3765 7.24809 18.6235 5.7305 16.6116C4.34198 14.7876 3.5 12.6357 3.5 10.3178ZM9.19423 10.5768C9.19423 12.1177 10.4517 13.3297 11.9934 13.3297C13.5362 13.3297 14.8058 12.1177 14.8058 10.5768C14.8058 9.0478 13.5362 7.77683 11.9934 7.77683C10.4517 7.77683 9.19423 9.0478 9.19423 10.5768Z" fill="white"/>
      </svg>
      `;
  }

  static chatFilterIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M8.87774 6.37856C8.87774 8.24523 7.33886 9.75821 5.43887 9.75821C3.53999 9.75821 2 8.24523 2 6.37856C2 4.51298 3.53999 3 5.43887 3C7.33886 3 8.87774 4.51298 8.87774 6.37856ZM20.4933 4.89833C21.3244 4.89833 22 5.56203 22 6.37856C22 7.19618 21.3244 7.85989 20.4933 7.85989H13.9178C13.0856 7.85989 12.4101 7.19618 12.4101 6.37856C12.4101 5.56203 13.0856 4.89833 13.9178 4.89833H20.4933ZM3.50777 15.958H10.0833C10.9155 15.958 11.5911 16.6217 11.5911 17.4393C11.5911 18.2558 10.9155 18.9206 10.0833 18.9206H3.50777C2.67555 18.9206 2 18.2558 2 17.4393C2 16.6217 2.67555 15.958 3.50777 15.958ZM18.5611 20.7778C20.4611 20.7778 22 19.2648 22 17.3992C22 15.5325 20.4611 14.0196 18.5611 14.0196C16.6623 14.0196 15.1223 15.5325 15.1223 17.3992C15.1223 19.2648 16.6623 20.7778 18.5611 20.7778Z" fill="currentColor"/>
      </svg>`;
  }

  static switchIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M3.27213 15.0536C3.38959 14.9653 3.6594 14.8229 3.6594 14.8229C4.61419 14.2702 6.98895 13.4377 8.19378 13.1704C8.19378 13.1648 8.91016 13.0062 9.25091 13L9.29626 13C9.81843 13 10.3069 13.1585 10.5569 13.4135C10.66 13.5185 10.7559 13.7741 10.8021 13.8975C10.8174 13.9383 10.8273 13.9646 10.8302 13.9662C10.9325 14.3305 11 14.8895 11 15.5031C11 16.1472 10.9325 16.7304 10.8069 17.0892C10.8069 17.0954 10.6825 17.423 10.6023 17.5325C10.4778 17.6904 10.2499 17.824 9.96613 17.9092C9.73935 17.9702 9.49978 18 9.25091 18C8.98924 17.9944 8.5008 17.903 8.30775 17.8607C7.03431 17.5934 4.60256 16.7186 3.67103 16.1839C3.56713 16.1284 3.4583 16.0652 3.38256 16.0212C3.34216 15.9977 3.31117 15.9797 3.29539 15.9713C3.10234 15.8376 3 15.6735 3 15.4969C3 15.3396 3.09071 15.1817 3.27213 15.0536ZM10.0148 8.10751C10.5593 8.10751 11 8.28294 11 8.49965C11 8.71636 10.5593 8.89179 10.0148 8.89179L5.25556 8.9999C4.56173 8.9999 4 8.77582 4 8.49965C4 8.22348 4.56173 7.9999 5.25556 7.9999L10.0148 8.10751ZM13.3977 6.46744C13.5233 6.30952 13.7501 6.17585 14.0339 6.09067C14.2606 6.02974 14.5002 5.9999 14.7503 5.9999C15.0108 6.0055 15.5004 6.09689 15.6934 6.13979C16.9657 6.40651 19.3974 7.28192 20.3301 7.81599C20.4315 7.87061 20.5382 7.93263 20.6137 7.97651L20.6137 7.97652C20.6559 8.00107 20.6883 8.01993 20.7046 8.02862C20.8977 8.16292 21 8.32644 21 8.50239C21 8.66093 20.9093 8.81885 20.7267 8.94631C20.6104 9.03459 20.3406 9.17697 20.3406 9.17697C19.387 9.73031 17.011 10.5622 15.8062 10.8295C15.8062 10.8358 15.091 10.9937 14.7503 10.9999L14.7049 10.9999C14.1816 10.9999 13.6931 10.842 13.4431 10.5864C13.3411 10.4816 13.245 10.2268 13.1983 10.1032L13.1983 10.1032L13.1983 10.1031C13.1827 10.0619 13.1727 10.0353 13.1698 10.0337C13.0675 9.66938 13 9.11044 13 8.49679C13 7.85267 13.0675 7.26949 13.1931 6.91074C13.1931 6.90515 13.3175 6.57687 13.3977 6.46744ZM13 15.4998C13 15.7165 13.4407 15.8919 13.9852 15.8919L18.7444 16C19.4383 16 20 15.7759 20 15.4998C20 15.2236 19.4383 15 18.7444 15L13.9852 15.1076C13.4407 15.1076 13 15.283 13 15.4998Z" fill="#002c24"/>
      </svg>
      `;
  }

  static checklistIcon(): string {
    return `<svg width="30" height="30" viewBox="0 0 21 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.4844 2.875H12.7969C12.7969 3.61192 12.4037 4.29282 11.7656 4.66112C11.1275 5.02958 10.3412 5.02958 9.70312 4.66112C9.06505 4.29282 8.67187 3.61192 8.67187 2.875H7.98437C7.80207 2.875 7.62712 2.94744 7.49821 3.07634C7.36931 3.20524 7.29688 3.38019 7.29688 3.5625V4.9375C7.29688 5.11981 7.36931 5.29476 7.49821 5.42366C7.62712 5.55257 7.80207 5.625 7.98437 5.625H13.4844C13.6667 5.625 13.8416 5.55257 13.9705 5.42366C14.0994 5.29476 14.1719 5.11981 14.1719 4.9375V3.5625C14.1719 3.38019 14.0994 3.20524 13.9705 3.07634C13.8416 2.94744 13.6667 2.875 13.4844 2.875Z" fill="currentColor"/>
      <path d="M12.1094 2.875C12.1094 3.63431 11.4937 4.25 10.7344 4.25C9.97506 4.25 9.35938 3.63431 9.35938 2.875C9.35938 2.11569 9.97506 1.5 10.7344 1.5C11.4937 1.5 12.1094 2.11569 12.1094 2.875Z" fill="currentColor"/>
      <path d="M6.4375 13.1875H7.8125V14.5625H6.4375V13.1875Z" fill="currentColor"/>
      <path d="M6.4375 17.3125H7.8125V18.6875H6.4375V17.3125Z" fill="currentColor"/>
      <path d="M6.4375 9.0625H7.8125V10.4375H6.4375V9.0625Z" fill="currentColor"/>
      <path d="M12.1094 22.8125V18.6875H10.5625C10.3168 18.6875 10.0899 18.5564 9.96708 18.3437C9.84432 18.1311 9.84432 17.8689 9.96708 17.6562C10.0899 17.4436 10.3168 17.3125 10.5625 17.3125H12.1094V15.9375C12.1095 15.4294 12.2981 14.9396 12.6388 14.5625H10.5625C10.3168 14.5625 10.0899 14.4314 9.96708 14.2187C9.84432 14.0061 9.84432 13.7439 9.96708 13.5312C10.0899 13.3186 10.3168 13.1875 10.5625 13.1875H15.0312C15.2136 13.1875 15.3885 13.2599 15.5174 13.3888C15.6463 13.5177 15.7187 13.6927 15.7187 13.875H18.4687V4.25C18.4687 4.06769 18.3963 3.89274 18.2674 3.76384C18.1385 3.63493 17.9636 3.5625 17.7812 3.5625H14.8594V5.625C14.8594 5.80731 14.7869 5.98226 14.658 6.11116C14.5291 6.24006 14.3542 6.3125 14.1719 6.3125H7.29687C7.11457 6.3125 6.93962 6.24006 6.81071 6.11116C6.68181 5.98226 6.60937 5.80731 6.60937 5.625V3.5625H3.6875C3.50519 3.5625 3.33024 3.63493 3.20134 3.76384C3.07243 3.89274 3 4.06769 3 4.25V22.8125C3 22.9948 3.07243 23.1698 3.20134 23.2987C3.33024 23.4276 3.50519 23.5 3.6875 23.5H12.2298C12.1506 23.2795 12.1099 23.0468 12.1095 22.8125H12.1094ZM10.5625 9.0625H15.0312C15.2769 9.0625 15.5039 9.19355 15.6267 9.40625C15.7494 9.61895 15.7494 9.88106 15.6267 10.0937C15.5039 10.3064 15.2769 10.4375 15.0312 10.4375H10.5625C10.3168 10.4375 10.0899 10.3064 9.96708 10.0937C9.84432 9.88105 9.84432 9.61894 9.96708 9.40625C10.0899 9.19356 10.3168 9.0625 10.5625 9.0625ZM8.5 18.6875C8.5 18.8698 8.42756 19.0448 8.29866 19.1737C8.16976 19.3026 7.99481 19.375 7.8125 19.375H6.4375C6.25519 19.375 6.08024 19.3026 5.95134 19.1737C5.82243 19.0448 5.75 18.8698 5.75 18.6875V17.3125C5.75 17.1302 5.82243 16.9552 5.95134 16.8263C6.08024 16.6974 6.25519 16.625 6.4375 16.625H7.8125C7.99481 16.625 8.16976 16.6974 8.29866 16.8263C8.42756 16.9552 8.5 17.1302 8.5 17.3125V18.6875ZM8.5 14.5625C8.5 14.7448 8.42756 14.9198 8.29866 15.0487C8.16976 15.1776 7.99481 15.25 7.8125 15.25H6.4375C6.25519 15.25 6.08024 15.1776 5.95134 15.0487C5.82243 14.9198 5.75 14.7448 5.75 14.5625V13.1875C5.75 13.0052 5.82243 12.8302 5.95134 12.7013C6.08024 12.5724 6.25519 12.5 6.4375 12.5H7.8125C7.99481 12.5 8.16976 12.5724 8.29866 12.7013C8.42756 12.8302 8.5 13.0052 8.5 13.1875V14.5625ZM8.5 10.4375C8.5 10.6198 8.42756 10.7948 8.29866 10.9237C8.16976 11.0526 7.99481 11.125 7.8125 11.125H6.4375C6.25519 11.125 6.08024 11.0526 5.95134 10.9237C5.82243 10.7948 5.75 10.6198 5.75 10.4375V9.0625C5.75 8.88019 5.82243 8.70524 5.95134 8.57634C6.08024 8.44743 6.25519 8.375 6.4375 8.375H7.8125C7.99481 8.375 8.16976 8.44743 8.29866 8.57634C8.42756 8.70524 8.5 8.88019 8.5 9.0625V10.4375Z" fill="currentColor"/>
      <path d="M21.0469 15.25H14.1719C13.9896 15.25 13.8146 15.3224 13.6857 15.4513C13.5568 15.5802 13.4844 15.7552 13.4844 15.9375V22.8125C13.4844 22.9948 13.5568 23.1698 13.6857 23.2987C13.8146 23.4276 13.9896 23.5 14.1719 23.5H21.0469C21.2292 23.5 21.4041 23.4276 21.533 23.2987C21.6619 23.1698 21.7344 22.9948 21.7344 22.8125V15.9375C21.7344 15.7552 21.6619 15.5802 21.533 15.4513C21.4041 15.3224 21.2292 15.25 21.0469 15.25ZM19.8987 18.4779C18.039 19.1275 17.4272 21.0937 17.4203 21.1212C17.3812 21.2526 17.3037 21.3694 17.1976 21.4562C17.0916 21.5432 16.9621 21.5966 16.8256 21.6094H16.7619C16.6353 21.6098 16.5109 21.5751 16.4027 21.5093C16.2944 21.4435 16.2064 21.3491 16.1484 21.2365C15.6896 20.3444 15.2719 20.0797 15.2685 20.078C15.1014 20.0048 14.9702 19.8682 14.9039 19.6983C14.8374 19.5286 14.8413 19.3392 14.9143 19.1721C14.9875 19.0051 15.1241 18.8739 15.294 18.8075C15.4638 18.7412 15.6531 18.7449 15.8202 18.8181C16.1491 18.9878 16.4365 19.2278 16.6624 19.5211C17.2869 18.4397 18.2701 17.6108 19.4416 17.1784C19.6719 17.1033 19.9247 17.1549 20.1071 17.3142C20.2896 17.4735 20.3747 17.717 20.3313 17.9552C20.2879 18.1935 20.1223 18.3913 19.8953 18.476L19.8987 18.4779Z" fill="currentColor"/>
      </svg>`;
  }

  static checklistDeleteIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 5H14C14 4.46957 13.7893 3.96086 13.4142 3.58579C13.0391 3.21071 12.5304 3 12 3C11.4696 3 10.9609 3.21071 10.5858 3.58579C10.2107 3.96086 10 4.46957 10 5ZM8.5 5C8.5 4.54037 8.59053 4.08525 8.76642 3.66061C8.94231 3.23597 9.20012 2.85013 9.52513 2.52513C9.85013 2.20012 10.236 1.94231 10.6606 1.76642C11.0852 1.59053 11.5404 1.5 12 1.5C12.4596 1.5 12.9148 1.59053 13.3394 1.76642C13.764 1.94231 14.1499 2.20012 14.4749 2.52513C14.7999 2.85013 15.0577 3.23597 15.2336 3.66061C15.4095 4.08525 15.5 4.54037 15.5 5H21.25C21.4489 5 21.6397 5.07902 21.7803 5.21967C21.921 5.36032 22 5.55109 22 5.75C22 5.94891 21.921 6.13968 21.7803 6.28033C21.6397 6.42098 21.4489 6.5 21.25 6.5H19.93L18.76 18.611C18.6702 19.539 18.238 20.4002 17.5477 21.0268C16.8573 21.6534 15.9583 22.0004 15.026 22H8.974C8.04186 22.0001 7.1431 21.653 6.45295 21.0265C5.7628 20.3999 5.33073 19.5388 5.241 18.611L4.07 6.5H2.75C2.55109 6.5 2.36032 6.42098 2.21967 6.28033C2.07902 6.13968 2 5.94891 2 5.75C2 5.55109 2.07902 5.36032 2.21967 5.21967C2.36032 5.07902 2.55109 5 2.75 5H8.5ZM10.5 9.75C10.5 9.55109 10.421 9.36032 10.2803 9.21967C10.1397 9.07902 9.94891 9 9.75 9C9.55109 9 9.36032 9.07902 9.21967 9.21967C9.07902 9.36032 9 9.55109 9 9.75V17.25C9 17.4489 9.07902 17.6397 9.21967 17.7803C9.36032 17.921 9.55109 18 9.75 18C9.94891 18 10.1397 17.921 10.2803 17.7803C10.421 17.6397 10.5 17.4489 10.5 17.25V9.75ZM14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75V17.25C15 17.4489 14.921 17.6397 14.7803 17.7803C14.6397 17.921 14.4489 18 14.25 18C14.0511 18 13.8603 17.921 13.7197 17.7803C13.579 17.6397 13.5 17.4489 13.5 17.25V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9ZM6.734 18.467C6.78794 19.0236 7.04724 19.5403 7.46137 19.9161C7.87549 20.292 8.41475 20.5001 8.974 20.5H15.026C15.5853 20.5001 16.1245 20.292 16.5386 19.9161C16.9528 19.5403 17.2121 19.0236 17.266 18.467L18.424 6.5H5.576L6.734 18.467Z" fill="#3E4550"/>
      </svg>
      `;
  }

  static checklistDragIcon(): string {
    return `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7 4C7 4.55228 7.44772 5 8 5C8.55228 5 9 4.55228 9 4C9 3.44772 8.55228 3 8 3C7.44772 3 7 3.44772 7 4Z" stroke="#D5D8DD" stroke-width="2"/>
      <path d="M15 4C15 4.55228 15.4477 5 16 5C16.5523 5 17 4.55228 17 4C17 3.44772 16.5523 3 16 3C15.4477 3 15 3.44772 15 4Z" stroke="#D5D8DD" stroke-width="2"/>
      <path d="M7 12C7 12.5523 7.44772 13 8 13C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11C7.44772 11 7 11.4477 7 12Z" stroke="#D5D8DD" stroke-width="2"/>
      <path d="M15 12C15 12.5523 15.4477 13 16 13C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11C15.4477 11 15 11.4477 15 12Z" stroke="#D5D8DD" stroke-width="2"/>
      <path d="M7 20C7 20.5523 7.44772 21 8 21C8.55228 21 9 20.5523 9 20C9 19.4477 8.55228 19 8 19C7.44772 19 7 19.4477 7 20Z" stroke="#D5D8DD" stroke-width="2"/>
      <path d="M15 20C15 20.5523 15.4477 21 16 21C16.5523 21 17 20.5523 17 20C17 19.4477 16.5523 19 16 19C15.4477 19 15 19.4477 15 20Z" stroke="#D5D8DD" stroke-width="2"/>
      </svg>
      `;
  }

  static covenantTabIcon(): string {
    return `<svg width="30" height="30" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.89409 8.97158H13.6657V4.19996L8.89409 8.97158ZM24.9723 4.00046H14.3532V9.3151C14.3532 9.50422 14.1985 9.65893 14.0093 9.65893H8.6947V24.9395C8.6947 25.5067 9.15534 25.9674 9.72256 25.9674H24.9722C25.5394 25.9674 26.0001 25.5067 26.0001 24.9395L26.0003 5.03133C26.0003 4.46412 25.5394 4 24.9722 4L24.9723 4.00046ZM23.1537 21.7115H11.5409C11.3517 21.7115 11.197 21.5568 11.197 21.3677C11.197 21.1786 11.3517 21.0239 11.5409 21.0239H23.1537C23.3428 21.0239 23.4975 21.1786 23.4975 21.3677C23.4975 21.557 23.3428 21.7115 23.1537 21.7115ZM23.1537 17.4559H11.5409C11.3517 17.4559 11.197 17.3012 11.197 17.112C11.197 16.9229 11.3517 16.7682 11.5409 16.7682H23.1537C23.3428 16.7682 23.4975 16.9229 23.4975 17.112C23.4975 17.3011 23.3428 17.4559 23.1537 17.4559ZM23.1537 13.1997H11.5409C11.3517 13.1997 11.197 13.045 11.197 12.8559C11.197 12.6668 11.3517 12.5121 11.5409 12.5121H23.1537C23.3428 12.5121 23.4975 12.6668 23.4975 12.8559C23.4975 13.045 23.3428 13.1997 23.1537 13.1997ZM23.1537 8.94362H17.7598C17.5672 8.94362 17.416 8.78892 17.416 8.59979C17.416 8.41067 17.5672 8.25597 17.7598 8.25597H23.1537C23.3428 8.25597 23.4975 8.41067 23.4975 8.59979C23.4974 8.78891 23.3427 8.94362 23.1535 8.94362H23.1537ZM8.89392 8.97109H13.6655V4.19947L8.89392 8.97109Z" fill="currentColor"/>
      <path d="M7.44994 8.22868V9.21532L5.73792 9.21516V8.22868C5.73792 8.03957 5.89262 7.88486 6.08174 7.88486H7.10629C7.29525 7.88486 7.44996 8.03956 7.44996 8.22868L7.44994 8.22868Z" fill="currentColor"/>
      <path d="M6.28454 25.4314C6.34295 25.5482 6.46324 25.6238 6.59395 25.6238C6.7245 25.6238 6.84826 25.5482 6.90336 25.4314L7.75497 23.6661H5.43274L6.28454 25.4314Z" fill="currentColor"/>
      <path d="M5.30493 21.7071H7.8833V22.9792H5.30493V21.7071Z" fill="currentColor"/>
      <path d="M7.53923 9.90395H5.6485C5.45872 9.90395 5.30467 10.0578 5.30467 10.2478V10.7721H4.34383C4.15404 10.7721 4 10.926 4 11.116V14.3194C4 14.5094 4.15371 14.6633 4.34383 14.6633C4.53378 14.6633 4.68765 14.5096 4.68765 14.3194V11.4598H5.30481V21.0199H7.88317L7.88301 10.2475C7.88301 10.0577 7.72913 9.90383 7.53918 9.90383L7.53923 9.90395Z" fill="currentColor"/>
      </svg>
      `;
  }
  static covenantEditPencilIcon(): string {
    return `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_270_3818)">
      <path d="M20.6938 6.87722L17.4016 3.56813C17.184 3.35168 16.8896 3.23016 16.5827 3.23016C16.2758 3.23016 15.9814 3.35168 15.7639 3.56813L4.09231 15.2229L3.02667 19.8219C2.98991 19.9901 2.99117 20.1643 3.03036 20.3318C3.06954 20.4994 3.14567 20.6561 3.25318 20.7905C3.36069 20.9249 3.49685 21.0335 3.65174 21.1086C3.80662 21.1836 3.9763 21.223 4.14839 21.2241C4.22858 21.2322 4.30938 21.2322 4.38956 21.2241L9.03912 20.1584L20.6938 8.51494C20.9103 8.29739 21.0318 8.00297 21.0318 7.69608C21.0318 7.38919 20.9103 7.09478 20.6938 6.87722ZM8.47825 19.1489L4.12035 20.0631L5.11308 15.7893L13.8457 7.09035L17.2109 10.4555L8.47825 19.1489ZM17.9624 9.64227L14.5973 6.2771L16.5491 4.33651L19.8582 7.70169L17.9624 9.64227Z" fill="#38B6FF"/>
      </g>
      <defs>
      <clipPath id="clip0_270_3818">
      <rect width="24" height="24" fill="white" transform="translate(0 0.34375)"/>
      </clipPath>
      </defs>
      </svg>
      `;
  }

  static attachFileIcon(): string {
    return `<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
      viewBox="0 0 24 25" style="enable-background:new 0 0 24 25;" xml:space="preserve">
  <style type="text/css">
     .st0{fill:#8F9BB3;stroke:#8F9BB3;stroke-width:0.2;stroke-miterlimit:10;}
  </style>
  <path class="st0" d="M6.8,21c-0.2-0.1-0.4-0.1-0.6-0.2c-2.2-0.8-2.9-3.5-1.2-5.2c3-3,5.9-6,8.9-9c0.9-0.9,2.5-0.7,3,0.5
     c0.3,0.7,0.2,1.4-0.3,2c-0.1,0.1-0.1,0.1-0.2,0.2c-2.8,2.8-5.6,5.7-8.4,8.5c-0.1,0.1-0.2,0.2-0.3,0.2c-0.2,0.1-0.4,0-0.6-0.2
     c-0.1-0.2-0.1-0.5,0-0.6c0-0.1,0.1-0.1,0.2-0.2c2.8-2.8,5.6-5.6,8.4-8.5C15.9,8.3,16,8,15.9,7.7c-0.1-0.5-0.8-0.8-1.2-0.4
     c-0.1,0-0.1,0.1-0.2,0.2c-2.9,3-5.9,5.9-8.8,8.9c-0.8,0.8-0.9,1.9-0.3,2.8c0.7,1,2.2,1.1,3.1,0.3c0.9-0.9,1.8-1.8,2.6-2.7
     c2.2-2.2,4.4-4.5,6.7-6.7c0.7-0.7,1.1-1.6,1.1-2.6c0-1.7-1.3-3-3-3.3c-1.1-0.1-2.1,0.2-3,1c-2.2,2.2-4.3,4.3-6.5,6.5
     c-0.2,0.2-0.3,0.3-0.6,0.2c-0.4-0.1-0.6-0.5-0.3-0.8c0-0.1,0.1-0.1,0.1-0.1c2.2-2.2,4.3-4.3,6.5-6.5c1.4-1.4,3-1.7,4.8-1.1
     c1.6,0.6,2.5,1.8,2.8,3.5c0.2,1.3-0.1,2.4-0.9,3.5c-0.1,0.2-0.3,0.3-0.4,0.5c-3,3.1-6.1,6.1-9.1,9.2c-0.6,0.6-1.2,0.9-2,1
     c0,0,0,0-0.1,0C7.3,21,7,21,6.8,21z"/>
  </svg>
      `;
  }

  static checkBoxIcon(): string {
    return `<svg width="100%" height="100%" viewBox="0 5 38 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 21.9274L18.75 25.6774L25 16.9274M35 20.6774C35 22.6472 34.612 24.5977 33.8582 26.4176C33.1044 28.2375 31.9995 29.8911 30.6066 31.284C29.2137 32.6768 27.5601 33.7817 25.7403 34.5356C23.9204 35.2894 21.9698 35.6774 20 35.6774C18.0302 35.6774 16.0796 35.2894 14.2597 34.5356C12.4399 33.7817 10.7863 32.6768 9.3934 31.284C8.00052 29.8911 6.89563 28.2375 6.14181 26.4176C5.38799 24.5977 5 22.6472 5 20.6774C5 16.6991 6.58035 12.8838 9.3934 10.0708C12.2064 7.25772 16.0218 5.67737 20 5.67737C23.9782 5.67737 27.7936 7.25772 30.6066 10.0708C33.4196 12.8838 35 16.6991 35 20.6774Z" stroke="#8F9BB3" stroke-width="2.18062" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>`;
  }
  static assetEditIcon(): string {
    return `<svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_680_422)">
<path d="M18.6666 20.7988H3.99996V6.13216H12.8133L14.1466 4.79883H3.99996C3.64634 4.79883 3.3072 4.9393 3.05715 5.18935C2.8071 5.4394 2.66663 5.77854 2.66663 6.13216V20.7988C2.66663 21.1525 2.8071 21.4916 3.05715 21.7416C3.3072 21.9917 3.64634 22.1322 3.99996 22.1322H18.6666C19.0202 22.1322 19.3594 21.9917 19.6094 21.7416C19.8595 21.4916 20 21.1525 20 20.7988V10.7988L18.6666 12.1322V20.7988Z" fill="#3E4550"/>
<path d="M22.3534 4.69221L20.1067 2.44554C20.007 2.34556 19.8886 2.26624 19.7581 2.21211C19.6277 2.15799 19.4879 2.13013 19.3467 2.13013C19.2055 2.13013 19.0657 2.15799 18.9353 2.21211C18.8049 2.26624 18.6864 2.34556 18.5867 2.44554L9.44672 11.6389L8.70672 14.8455C8.67519 15.001 8.67851 15.1615 8.71642 15.3155C8.75434 15.4695 8.82592 15.6132 8.926 15.7362C9.02609 15.8592 9.1522 15.9586 9.29526 16.027C9.43832 16.0955 9.59478 16.1314 9.75339 16.1322C9.83536 16.1412 9.91808 16.1412 10.0001 16.1322L13.2334 15.4189L22.3534 6.21221C22.4534 6.1125 22.5327 5.99405 22.5868 5.86363C22.6409 5.73322 22.6688 5.59341 22.6688 5.45221C22.6688 5.31101 22.6409 5.17119 22.5868 5.04078C22.5327 4.91037 22.4534 4.79191 22.3534 4.69221V4.69221ZM12.5401 14.1855L10.1001 14.7255L10.6667 12.3055L17.5467 5.37887L19.4267 7.25887L12.5401 14.1855ZM20.1801 6.50554L18.3001 4.62554L19.3334 3.57221L21.2267 5.46554L20.1801 6.50554Z" fill="#3E4550"/>
</g>
<defs>
<clipPath id="clip0_680_422">
<rect width="24" height="24" fill="white" transform="translate(0 0.798828)"/>
</clipPath>
</defs>
</svg>`;
  }

  static addTaskIcon(): string {
    return `<svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.5 15.1346L7.8075 12.4421L6.75 13.4996L10.5 17.2496L17.25 10.4996L16.1925 9.43457L10.5 15.1346Z" fill="#3E4550"/>
      <path d="M18.75 3.75H16.5V3C16.5 2.60218 16.342 2.22064 16.0607 1.93934C15.7794 1.65804 15.3978 1.5 15 1.5H9C8.60218 1.5 8.22064 1.65804 7.93934 1.93934C7.65804 2.22064 7.5 2.60218 7.5 3V3.75H5.25C4.85218 3.75 4.47064 3.90804 4.18934 4.18934C3.90804 4.47064 3.75 4.85218 3.75 5.25V21C3.75 21.3978 3.90804 21.7794 4.18934 22.0607C4.47064 22.342 4.85218 22.5 5.25 22.5H18.75C19.1478 22.5 19.5294 22.342 19.8107 22.0607C20.092 21.7794 20.25 21.3978 20.25 21V5.25C20.25 4.85218 20.092 4.47064 19.8107 4.18934C19.5294 3.90804 19.1478 3.75 18.75 3.75ZM9 3H15V6H9V3ZM18.75 21H5.25V5.25H7.5V7.5H16.5V5.25H18.75V21Z" fill="#3E4550"/>
      </svg>`;
  }

  static activitiesIcon(): string {
    return `<svg width="30" height="30" viewBox="0 -2 21 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M5.03133 1H17.972C19.1005 1 20.0033 1.90282 20.0033 3.03133V23.0438C20.0033 24.0971 19.1005 25 17.972 25H5.03133C3.90282 25 3 24.0972 3 23.0438V3.03133C3 1.90282 3.90282 1 5.03133 1V1ZM9.69588 8.59853H17.4453C18.4233 8.59853 18.4233 10.028 17.4453 10.028H9.69588C8.71788 10.028 8.71788 8.59853 9.69588 8.59853ZM5.40762 16.6488L5.85894 17.1001L7.06262 15.5954C7.36362 15.2193 7.89015 15.6706 7.66446 15.9716L6.1598 17.8525C6.08463 18.003 5.78363 18.003 5.63313 17.8525L4.88078 17.1001C4.57977 16.7991 5.10644 16.2726 5.40744 16.6488L5.40762 16.6488ZM5.40762 12.9622L5.85894 13.4135L7.06262 11.9089C7.36362 11.5327 7.89015 11.9089 7.66446 12.2851L6.1598 14.1659C6.08463 14.3164 5.78363 14.3164 5.63313 14.1659L4.88078 13.4136C4.57977 13.1126 5.10644 12.586 5.40744 12.9622L5.40762 12.9622ZM9.69588 15.9716H17.4453C18.4233 15.9716 18.4233 17.4011 17.4453 17.4011H9.69588C8.71788 17.4011 8.71788 15.9716 9.69588 15.9716ZM9.69588 12.285H17.4453C18.4233 12.285 18.4233 13.7145 17.4453 13.7145H9.69588C8.71788 13.7145 8.71788 12.285 9.69588 12.285ZM5.40762 9.27567L5.85894 9.727L7.06262 8.22233C7.36362 7.84616 7.89015 8.22233 7.66446 8.5985L6.1598 10.4794C6.08463 10.6299 5.78363 10.6299 5.63313 10.4794L4.88078 9.72701C4.57977 9.42601 5.10644 8.89948 5.40744 9.27568L5.40762 9.27567ZM8.86848 2.12842H14.1347V3.10642H8.86848V2.12842Z" fill="currentColor"/>
      </svg>`;
  }
}
