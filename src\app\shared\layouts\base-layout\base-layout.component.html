<nb-layout>
  <nb-layout-header fixed>
    <!--
      <button nbButton ghost status="info" size="large" shape="semi-round" (click)="toggle()">
        <nb-icon icon="menu"></nb-icon>
      </button>

      &nbsp;
   -->
    <!-- <div class="header-container"> -->
    <!-- Mobile Logo: show only below 480px -->
    <img height="36" class="w-[36px] h-[36px] block sm:hidden" src="assets/images/logo-only-velvet-green.svg" alt="" />

    <!-- Desktop Logo: show only at 480px and up -->
    <div class="hidden sm:block ml-[-10px]">
      <img
        height="50"
        class="h-[50px] max-w-[200px] hidden sm:block"
        src="assets/images/investor-wyde-logo.svg"
        alt="Investor Wyde Logo"
      />
    </div>
    &nbsp;

    <nb-actions>
      <nb-action class="control-item" icon="menu-outline" (click)="toggle()"> </nb-action>
    </nb-actions>

    <!-- </div> -->

    <div class="header-container">
      <nb-actions size="medium">
        <nb-action class="control-item mobile-button">
          <app-help-popover></app-help-popover>
        </nb-action>

        <nb-action *ngIf="isInvestor()" class="control-item mobile-button">
          <app-switch-account (openMessages)="openMessages($event)"></app-switch-account>
        </nb-action>

        <nb-action
          style="width: 48px"
          class="control-item user-bar"
          nbPopoverTrigger="click"
          nbPopoverPlacement="bottom"
          [nbPopover]="userTemplate"
        >
          <nb-user
            *ngIf="!notificationCount"
            nbTooltipStatus="primary"
            nbTooltip="{{ username }}"
            style="cursor: pointer"
            nbTooltipPlacement="left"
            size="medium"
            color="#002c24"
            size="medium"
            [name]="name"
            onlyPicture="true"
          >
          </nb-user>
          <nb-user
            *ngIf="notificationCount > 0"
            nbTooltipStatus="primary"
            nbTooltip="{{ username }}"
            style="cursor: pointer"
            nbTooltipPlacement="left"
            size="medium"
            color="#002c24"
            size="medium"
            [name]="name"
            onlyPicture="true"
            badgeText="{{ notificationCount }}"
            badgeStatus="danger"
            badgePosition="top right"
          >
          </nb-user>
        </nb-action>

        <ng-template #userTemplate>
          <div size="small" class="list-action-container">
            <nb-list>
              <nb-list-item (click)="showMessages()">
                <div class="user-popup">
                  <strong status="default">Messages</strong>
                  <div *ngIf="notificationCount > 0">{{ notificationCount }} Unread</div>
                </div>
              </nb-list-item>
              <nb-list-item (click)="logout()">
                <div class="user-popup">
                  <strong status="default">Logout</strong>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </ng-template>

        <!-- <nb-action class="control-item">
          <button nbButton shape="round" (click)="logout()" status="primary">
            <nb-icon icon="log-out-outline"></nb-icon>
          </button>
        </nb-action> -->

        <!-- <nb-action ghost class="control-item" status="primary" ></nb-action> -->
      </nb-actions>
    </div>

    <app-messages
      *ngIf="isInvestor() ? investorId && visible : visible"
      [investorId]="investorId"
      (updateNotificationCount)="updateNotificationCount($event)"
      (hidePopup)="hidePopup()"
      [visible]="visible"
    >
    </app-messages>
  </nb-layout-header>

  <nb-sidebar [responsive]="true" state="collapsed">
    <app-sidebar> </app-sidebar>
  </nb-sidebar>

  <nb-layout-column>
    <router-outlet></router-outlet>
  </nb-layout-column>
</nb-layout>
