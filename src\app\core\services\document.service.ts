import { Injectable } from '@angular/core';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { SharedService } from './shared.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DocumentService {
  constructor(
    private http: HttpClient,
    private sharedService: SharedService,
  ) {}

  uploadDocument(formData: any): any {
    const uploadURL = `${environment.apiURL}/api/Document/save-document`;

    return this.http.post<any>(uploadURL, formData, {
      reportProgress: true,
      observe: 'events',
    });
  }

  uploadAssetDocument(formData: any): any {
    const uploadURL = `${environment.apiURL}/api/Document/save-asset-document`;

    return this.http.post<any>(uploadURL, formData, {
      reportProgress: true,
      observe: 'events',
    });
  }

  deleteDocument(formData: any): any {
    const uploadURL = `${environment.apiURL}/api/Document/delete-document`;

    return this.http.post<any>(uploadURL, formData);
  }

  deleteAssetDocument(formData: any): any {
    const uploadURL = `${environment.apiURL}/api/Document/delete-asset-document`;

    return this.http.post<any>(uploadURL, formData);
  }

  async getDocument(formDefinition: any): Promise<void> {
    const data = await this.http
      .post<any>(`${environment.apiURL}/api/Document/get-document`, formDefinition)
      .toPromise();
    this.sharedService.download(
      this.sharedService.base64ToBlob(data.payload?.fileData),
      data.payload?.fileName,
      data.payload,
    );
  }

  async getDocumentFile(formDefinition: any): Promise<void> {
    const data = await this.http
      .post<any>(`${environment.apiURL}/api/Document/get-document`, formDefinition)
      .toPromise();
    this.sharedService.downloadFile(
      this.sharedService.base64ToBlob(data.payload?.fileData),
      data.payload?.fileName,
      data.payload,
    );
  }

  getAttachments(filters: any): any {
    return this.http.post(`${environment.apiURL}/api/Document/getallattachments`, filters);
  }
}
