@use "themes" as *;

:host {
  .title h5,
  .chart-title {
    text-decoration-line: underline;
    text-underline-offset: 5px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }

  .Closed {
    border: 1px solid #e12f2f;
    color: #e12f2f;
    padding: 8px 18px;
    border-radius: 10px;
  }

  .Funded {
    border: 1px solid #e4d020;
    color: #e4d020;
    padding: 8px 18px;
    border-radius: 10px;
  }

  .Open {
    border: 1px solid var(--color-lime);
    color: var(--color-lime);
    padding: 8px 18px;
    border-radius: 10px;
  }

  .Draft {
    border: 1px solid var(--color-grey-600);
    color: var(--color-grey-600);
    padding: 8px 18px;
    border-radius: 10px;
  }

  .draft-button {
    background: #ffffff;
    color: #002c24;
    padding: var(--button-filled-medium-padding);
    border-style: var(--button-filled-border-style);
    border-width: var(--button-filled-border-width);
    text-transform: var(--button-filled-text-transform);
    border: 1px solid #002c24;
    border-radius: 4px;
  }
}

.heading-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;
  margin: 0px 15px;
}

.no-hover:hover {
  cursor: default;
}

.investments-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: 0px 20px;
  button {
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
  }

  .image-container {
    position: relative;
    text-align: center;
    color: white;

    img,
    .img {
      width: 100%;
      height: 270px;
      object-fit: cover;
      object-position: 80% 100%;
      border-radius: 10px;
      color: #f7f7f7;
    }

    .invested {
      position: absolute;
      left: 0px;
      top: 48px;
      font-weight: 900;
      font-size: 22px;
      line-height: 24px;
      color: #ffffff;
      background: var(--color-velvet-700);
      padding: 10px;
      box-shadow: 1px 1px 3px #6c757d;
    }

    .location {
      position: absolute;
      left: 22px;
      bottom: 21px;
      font-size: 16px;
      line-height: 24px;
      color: #fbfbfb;
      font-weight: bold;
      text-shadow: 2px 3px 2px #495057;
    }
  }

  .total {
    font-weight: normal;
    font-size: 22px;
    line-height: 28px;
    color: var(--color-grey-600);
    margin: 10px 0px;
  }

  .solid {
    margin: 13px 0px;
  }

  .total-value {
    font-weight: bold;
    font-size: 35px;
    line-height: 30px;
    color: var(--color-velvet-700);
  }

  label {
    font-size: 16px;
    line-height: 24px;
    color: var(--color-grey-600);
  }

  p {
    font-weight: bold;
    font-size: 20px;
    line-height: 28px;
    color: var(--color-velvet-700);
    margin-top: 0;
    margin-bottom: 7px;
  }

  nb-card {
    margin: 10px 0;
    transition: all 2s;
  }
}

.items-rows {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}
.items-rows nb-select {
  // margin-right: 10px;
  min-width: 200px;
}

.items-rows div {
  margin-right: 5px;
  margin-bottom: 8px;
  @media screen and (max-width: 768px) {
    width: 100%;
  }
}
