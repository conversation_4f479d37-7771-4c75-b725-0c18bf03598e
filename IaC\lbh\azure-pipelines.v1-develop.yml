resources:
  repositories:
    - repository: templates
      type: git
      name: 'LBH - DevOps/azure-pipelines-lib'
      ref: refs/tags/v0.2.1

trigger:
  - version/v1/develop

variables:
  - name: imageRepositoryName
    value: swinvestorweb
  - name: imageTagBaseName
    value: v1-dev
  - name: ngConfiguration
    value: development
  - name: portainer.webhook.timeoutinseconds
    value: 60

pool:
  name: $(agentPoolName)

stages:
  - stage: Build
    jobs:
      - job: build_web
        steps:
          - template: steps/docker-build-scan-push-registry.yml@templates
            parameters:
              dockerConnection: $(dockerConnection)
              buildContext: .
              dockerFile: Dockerfile
              imageName: $(imageRepositoryName)
              imageTag: $(imageTagBaseName)-$(Build.BuildNumber)
              imageAltTag: $(imageTagBaseName)-latest
              extraBuildOptions: |
                --build-arg NG_CONFIGURATION=$(ngConfiguration)

  - stage: Deploy
    jobs:
      - deployment: deploy_web
        timeoutInMinutes: 2
        environment: sw-investorv1-dev
        strategy:
          runOnce:
            deploy:
              steps:
                - script: curl -X POST --ssl-no-revoke --max-time $(portainer.webhook.timeoutinseconds) "$(portainer.webhook.url)?tag=$(imageTagBaseName)-$(Build.BuildNumber)"
                  displayName: Trigger Portainer service deployment