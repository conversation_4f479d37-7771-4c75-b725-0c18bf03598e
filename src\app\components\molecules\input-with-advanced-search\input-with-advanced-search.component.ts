import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { BaseComponent } from '@core/models/base.component';
import { InvestmentService } from '@core/services/investment.service';
import { NbButtonModule, NbIconModule, NbInputModule, NbListModule } from '@nebular/theme';
import { Observable, of } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

export interface SearchResult {
  id: number;
  title: string;
  borrower?: string;
  totalOpportunity?: number;
  investmentType?: string;
  assetType?: string;
}

@Component({
  selector: 'app-input-with-advanced-search',
  imports: [CommonModule, ReactiveFormsModule, NbInputModule, NbButtonModule, NbIconModule, NbListModule],
  templateUrl: './input-with-advanced-search.component.html',
  styleUrl: './input-with-advanced-search.component.scss',
})
export class InputWithAdvancedSearchComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() placeholder = 'Enter TMO Loan Id...';
  @Input() disabled = false;
  @Output() selectionChange = new EventEmitter<SearchResult | null>();
  @Output() searchChange = new EventEmitter<string>();

  searchControl = new FormControl('');
  searchResults: SearchResult[] = [];
  selectedItem: SearchResult | null = null;
  isLoading = false;
  showDropdown = false;
  hasSearched = false;
  investmentService = inject(InvestmentService);

  ngOnInit(): void {
    this.updateDisabledState();
    this.setupSearch();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['disabled'] && !changes['disabled'].firstChange) {
      this.updateDisabledState();
    }
  }

  private updateDisabledState(): void {
    if (this.disabled) {
      this.searchControl.disable();
    } else {
      this.searchControl.enable();
    }
  }

  private setupSearch(): void {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(300), // Wait 300ms after user stops typing
        distinctUntilChanged(), // Only emit when value actually changes
        switchMap((searchTerm: string | null): Observable<SearchResult[]> => {
          if (!searchTerm || searchTerm.trim().length < 2) {
            this.searchResults = [];
            this.showDropdown = false;
            this.hasSearched = false;
            this.searchChange.emit(searchTerm || '');
            return of([]); // Return empty array instead of EMPTY
          }

          this.isLoading = true;
          this.hasSearched = true;
          this.searchChange.emit(searchTerm);

          return this.getInvestments(searchTerm.trim()).pipe(
            catchError((error) => {
              console.error('Search error:', error);
              this.isLoading = false;
              this.searchResults = [];
              this.showDropdown = false;
              return of([]);
            }),
          );
        }),
      )
      .subscribe((results: SearchResult[]) => {
        console.log('result', results);
        this.isLoading = false;
        this.searchResults = results;
        this.showDropdown = results.length > 0;
      });
  }

  private getInvestments(searchTerm: string): Observable<SearchResult[]> {
    // Call the investment service API
    const filters = {
      search: searchTerm,
      pageSize: 10, // Limit results for dropdown
      pageNumber: 1,
    };

    return this.investmentService.getInvestments(filters).pipe(
      switchMap((response: any) => {
        if (response.success && response.payload) {
          // Map the API response to SearchResult format
          const mappedResults: SearchResult[] = response.payload.investments.map((investment: any) => ({
            id: investment.id,
            title: investment.title,
            borrower: investment.borrower,
            totalOpportunity: investment.totalOpportunity,
            investmentType: investment.investmentType,
            assetType: investment.assetType,
          }));
          return of(mappedResults);
        }
        return of([]);
      }),
    );
  }

  onItemSelect(item: SearchResult): void {
    this.selectedItem = item;
    this.searchControl.setValue(item.title, { emitEvent: false });
    this.showDropdown = false;
    this.selectionChange.emit(item);
  }

  onClearSearch(): void {
    this.searchControl.setValue('', { emitEvent: false });
    this.selectedItem = null;
    this.searchResults = [];
    this.showDropdown = false;
    this.hasSearched = false;
    this.selectionChange.emit(null);
    this.searchChange.emit('');
  }

  onSearchIconClick(): void {
    console.log('here clicked');

    const searchTerm = this.searchControl.value?.trim() || '';

    // Emit search event to notify parent components
    this.searchChange.emit(searchTerm);

    // Handle different cases
    if (!searchTerm) {
      console.log('Search clicked with empty input');
      // Optionally focus the input or show a message
      return;
    }

    if (searchTerm.length < 2) {
      console.log('Search clicked with insufficient characters:', searchTerm);
      // Optionally show a message that minimum 2 characters are required
      return;
    }

    // Trigger immediate search without debounce delay
    console.log('Triggering immediate search for:', searchTerm);
    this.triggerImmediateSearch(searchTerm);
  }

  private triggerImmediateSearch(searchTerm: string): void {
    this.isLoading = true;
    this.hasSearched = true;

    this.getInvestments(searchTerm).subscribe({
      next: (results: SearchResult[]) => {
        this.isLoading = false;
        this.searchResults = results;
        this.showDropdown = results.length > 0;
        console.log('Immediate search results:', results);
      },
      error: (error) => {
        console.error('Immediate search error:', error);
        this.isLoading = false;
        this.searchResults = [];
        this.showDropdown = false;
      },
    });
  }

  get showClearIcon(): boolean {
    return !!(this.searchControl.value && this.searchControl.value.trim().length > 0) || !!this.selectedItem;
  }

  get showSearchIcon(): boolean {
    return !this.showClearIcon;
  }

  trackByFn(_index: number, item: SearchResult): number {
    return item.id;
  }
}
