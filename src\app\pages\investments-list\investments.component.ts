import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { ImageHelper } from '@core/helpers/image.helper';
import { TypeKey } from '@core/models/config';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { Paginator, PaginatorModule } from 'primeng/paginator';
import { Subject, catchError, finalize, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-investments',
  templateUrl: './investments.component.html',
  styleUrls: ['./investments.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbIconModule,
    NbSpinnerModule,
    NbButtonModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    PaginatorModule,
  ],
})
export class InvestmentsComponent implements OnInit, OnDestroy, AfterViewInit {
  /**
   * References to UI components
   */
  @ViewChild('paginator', { static: true }) paginator?: Paginator;

  /**
   * RxJS subjects for managing subscriptions and async operations
   */
  private readonly destroy$ = new Subject<boolean>();
  private readonly searchSubject$ = new Subject<string>();

  /**
   * Utility and state properties
   */
  readonly imageHelper = ImageHelper; // Static helper for image handling
  loading = false;
  firstLoad = true;

  /**
   * Investment data
   */
  investments: any[] = [];
  investorId?: number;

  /**
   * Pagination state
   */
  currentPage = 0;
  pageSize = 10;
  totalRecords = 0;

  /**
   * Search and filter state
   */
  searchText = '';
  statusData: any[] = [];
  selectedStatus: number | null = null;

  /**
   * API request payload
   */
  payload: Filters = {};

  constructor(
    private toast: NbToastrService,
    private investmentService: InvestmentService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private dom: DomSanitizer,
    private router: Router,
  ) {}

  /**
   * Initialize component with proper sequencing of async operations
   */
  ngOnInit(): void {
    this.setupInitialFilters();
    this.setupSearchDebounce();
    this.initializeComponentFlow();
  }

  /**
   * Manages the initialization flow with proper sequencing:
   * 1. Load status data
   * 2. Apply URL parameters
   * 3. Load investments
   */
  private initializeComponentFlow(): void {
    this.loadStatusData()
      .then(() => {
        this.applyUrlParameters();
        this.loadInvestments();
      })
      .catch((error) => {
        console.error('Error in component initialization flow:', error);
        // Continue with default values if there's an error
        this.applyUrlParameters();
        this.loadInvestments();
      });
  }

  /**
   * Reads and applies URL query parameters to component state
   */
  private applyUrlParameters(): void {
    const queryParams = new URLSearchParams(window.location.search);

    // Apply pagination parameters
    this.applyPaginationFromUrl(queryParams);

    // Apply search and filter parameters
    this.applySearchFromUrl(queryParams);
    this.applyStatusFromUrl(queryParams);
  }

  /**
   * Applies pagination parameters from URL
   */
  private applyPaginationFromUrl(queryParams: URLSearchParams): void {
    // Get page parameter (1-indexed in URL, 0-indexed in component)
    const pageParam = queryParams.get('page');
    if (pageParam) {
      this.currentPage = Math.max(0, parseInt(pageParam, 10) - 1);
      this.payload.pageNumber = this.currentPage;
    } else {
      // Default to first page if not specified
      this.currentPage = 0;
      this.payload.pageNumber = 0;
    }

    // Get page size parameter
    const limitParam = queryParams.get('limit');
    if (limitParam) {
      this.pageSize = parseInt(limitParam, 10);
      this.payload.pageSize = this.pageSize;
    }
  }

  /**
   * Applies search parameter from URL
   */
  private applySearchFromUrl(queryParams: URLSearchParams): void {
    const searchParam = queryParams.get('q');
    if (searchParam !== null && searchParam.trim() !== '') {
      this.searchText = searchParam.trim();
      this.payload.search = this.searchText;
    }
  }

  /**
   * Applies status filter parameter from URL
   */
  private applyStatusFromUrl(queryParams: URLSearchParams): void {
    const statusParam = queryParams.get('s');
    if (statusParam !== null && statusParam !== '') {
      this.selectedStatus = parseInt(statusParam, 10);
      this.payload.statusId = this.selectedStatus;
    }
  }

  /**
   * Sets up debounced search to prevent excessive API calls
   */
  private setupSearchDebounce(): void {
    this.searchSubject$
      .pipe(
        debounceTime(500), // Wait 500ms after last input
        distinctUntilChanged(), // Only emit when value changes
        takeUntil(this.destroy$), // Auto-unsubscribe on component destroy
      )
      .subscribe(() => this.onSearch());
  }

  /**
   * Sets up initial filter state and investor context
   */
  private setupInitialFilters(): void {
    // Initialize payload with default values
    this.initializeDefaultPayload();

    // Set up investor-specific context if applicable
    this.setupInvestorContext();
  }

  /**
   * Initializes the default API request payload
   */
  private initializeDefaultPayload(): void {
    this.currentPage = 0;
    this.payload = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      investmentId: 0,
    };
  }

  /**
   * Sets up investor-specific context if the current user is an investor
   */
  private setupInvestorContext(): void {
    if (this.sharedService.isInvestor()) {
      this.investorsService.account.pipe(takeUntil(this.destroy$)).subscribe((value: any) => {
        if (this.sharedService.isInvestor()) {
          this.investorId = this.investorsService.accountValue?.investorId || 0;

          // Update payload with investor ID
          this.payload = {
            ...this.payload,
            investorId: this.investorId,
          };
        }
      });
    }
  }

  /**
   * Loads investment status data from the API
   * @returns Promise that resolves when status data is loaded
   */
  loadStatusData(): Promise<void> {
    return new Promise<void>((resolve) => {
      this.investmentService
        .getEntityType({ typeKey: TypeKey.AdminInvestmentStatus })
        .pipe(
          takeUntil(this.destroy$),
          catchError((err) => {
            console.error('Error loading status data:', err);
            // Return empty array on error
            return of({ success: false, payload: [] });
          }),
        )
        .subscribe({
          next: (response: any) => {
            this.processStatusData(response);
            resolve();
          },
          error: () => {
            // Fallback to empty array if error handler missed something
            this.statusData = [];
            resolve();
          },
        });
    });
  }

  /**
   * Processes status data response
   * @param response The API response containing status data
   */
  private processStatusData(response: any): void {
    if (!response?.success) {
      this.statusData = [];
      return;
    }

    // Filter out Draft status (ID 30) for investors
    if (this.sharedService.isInvestor()) {
      this.statusData = response.payload.filter((status: any) => status.id !== 30);
    } else {
      this.statusData = response.payload;
    }
  }

  /**
   * Loads investment data from the API
   */
  loadInvestments(): void {
    // Skip if already loading
    if (this.loading) {
      return;
    }

    // Sanitize page number
    this.sanitizePageNumber();

    // Clear existing data and show loading state
    this.investments = [];
    this.loading = true;

    this.investmentService
      .getInvestments(this.payload)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.loading = false; // Always clear loading state when finished
        }),
      )
      .subscribe({
        next: (response: any) => this.handleInvestmentsResponse(response),
        error: (error: any) => this.handleInvestmentsError(error),
      });
  }

  /**
   * Ensures page number is valid
   */
  private sanitizePageNumber(): void {
    if (this.currentPage < 0) {
      this.currentPage = 0;
      this.payload.pageNumber = 0;
    }
  }

  /**
   * Handles successful investment data response
   */
  private handleInvestmentsResponse(response: any): void {
    if (!response?.success) {
      this.handleInvestmentsError(new Error('API returned failure status'));
      return;
    }

    this.firstLoad = false;
    this.investments = response.payload.investments || [];
    this.totalRecords = response.payload.rows || 0;

    // Handle page navigation to non-existent page
    if (this.shouldNavigateToFirstPage()) {
      this.navigateToFirstPage();
    }
  }

  /**
   * Checks if we should navigate to first page (current page has no data)
   */
  private shouldNavigateToFirstPage(): boolean {
    return this.totalRecords > 0 && this.currentPage > 0 && this.investments.length === 0;
  }

  /**
   * Navigates to first page when current page has no data
   */
  private navigateToFirstPage(): void {
    this.currentPage = 0;
    this.payload.pageNumber = 0;
    this.updateUrlParams();
    this.loadInvestments();
  }

  /**
   * Handles errors from investment data API
   */
  private handleInvestmentsError(error: any): void {
    this.investments = [];
    this.toast.danger('Failed to load investments', 'Error');
    console.error('Error loading investments:', error);
  }

  /**
   * Handles pagination control changes
   * @param event The pagination event from the UI component
   */
  onPageChange(event: any): void {
    // Update pagination state
    const previousPage = this.currentPage;
    this.currentPage = event.page;
    this.pageSize = event.rows;

    // Update payload with new pagination values
    this.payload.pageNumber = this.currentPage;
    this.payload.pageSize = this.pageSize;

    // Load data with new pagination
    this.loadInvestments();

    // Update URL to reflect the new pagination state
    this.updateUrlParams();
  }

  /**
   * Fetches and sanitizes an image from the API
   * @param image The image ID/path to fetch
   */
  async getImage(image: string): Promise<any> {
    try {
      const blob = await this.investmentService.getImage(image);
      if (!blob) {
        return null;
      }
      const url = window.URL.createObjectURL(blob);
      return this.dom.bypassSecurityTrustUrl(url);
    } catch (error) {
      console.error('Error loading image:', error);
      return null;
    }
  }

  editInvestor(investment: any): void {
    this.sharedService.setFormParamValue({
      investmentId: investment.id,
      userId: investment.userId,
      investmentTitle: investment.title,
      changeTab: false,
    });
    this.router.navigate(['/investment/view']);
  }

  addNewInvestment(): void {
    this.sharedService.setFormParamValue({});
    this.router.navigate(['/investment/view']);
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isManager(): boolean {
    return this.sharedService.isManager();
  }

  exportInvestment(): void {
    const payload: any = this.investorId ? { investorId: this.investorId } : {};
    payload.export = true;
    this.toast.default(`Downloading started`, '', {
      icon: 'download',
    });
    this.investmentService.getInvestmentsExport(payload);
  }

  /**
   * Triggers search with debounce when search input changes
   */
  onSearchChange(): void {
    // Feed text to debounce subject
    this.searchSubject$.next(this.searchText);
  }

  /**
   * Performs search after debounce period
   */
  onSearch(): void {
    // Reset to first page when searching
    this.resetToFirstPage();

    // Handle search text
    this.processSearchText();

    // Load filtered data and update URL
    this.loadInvestments();
    this.updateUrlParams();
  }

  /**
   * Process and clean search text
   */
  private processSearchText(): void {
    if (this.searchText && this.searchText.trim()) {
      this.payload.search = this.searchText.trim();
    } else {
      // Handle empty search
      delete this.payload.search;
      this.searchText = '';
    }
  }

  /**
   * Handles status filter change
   * @param statusId The selected status ID
   */
  onStatusChange(statusId: number): void {
    // Reset to first page when filtering
    this.resetToFirstPage();

    // Update status filter
    this.processStatusChange(statusId);

    // Load filtered data and update URL
    this.loadInvestments();
    this.updateUrlParams();
  }

  /**
   * Process status filter changes
   */
  private processStatusChange(statusId: number): void {
    if (statusId) {
      this.payload.statusId = statusId;
    } else {
      delete this.payload.statusId;
      this.selectedStatus = null;
    }
  }

  /**
   * Reset pagination to first page
   */
  private resetToFirstPage(): void {
    this.currentPage = 0;
    this.payload.pageNumber = 0;

    // Reset the paginator UI immediately without loading data yet
    // (data will be loaded by the calling method)
    if (this.paginator) {
      setTimeout(() => {
        this.paginator?.changePage(0);
      });
    }
  }

  /**
   * After component view initialization, sync the paginator with URL parameters
   */
  ngAfterViewInit(): void {
    // Sync paginator with URL parameters after view is initialized
    this.syncPaginatorWithUrlParams();
  }

  /**
   * Synchronizes the paginator UI with the current URL parameters
   */
  private syncPaginatorWithUrlParams(): void {
    if (!this.paginator) return;

    const queryParams = new URLSearchParams(window.location.search);
    const pageParam = queryParams.get('page');

    if (pageParam) {
      // Convert from 1-indexed (URL) to 0-indexed (component)
      const pageIndex = Math.max(0, parseInt(pageParam, 10) - 1);

      // Update paginator UI to match URL
      setTimeout(() => {
        this.paginator?.changePage(pageIndex);
        console.log('Paginator synced with URL, page:', pageIndex);
      }, 800); // Delay to ensure paginator is ready
    }
  }

  /**
   * Clears all filters and resets to initial state
   */
  clearFilters(): void {
    // Reset all filter values
    this.searchText = '';
    this.selectedStatus = null;
    this.resetToFirstPage();

    // Reset payload filters
    delete this.payload.search;
    delete this.payload.statusId;

    // Load unfiltered data and update URL
    this.loadInvestments();
    this.updateUrlParams();
  }

  /**
   * Updates URL with current filter and pagination state
   */
  private updateUrlParams(): void {
    const queryParams = this.buildQueryParams();

    this.router.navigate([], {
      queryParams,
      queryParamsHandling: null, // Replace all query parameters
      preserveFragment: true, // Keep URL fragments
      replaceUrl: true, // Replace URL in history (don't add)
    });
  }

  /**
   * Builds query parameters object based on current state
   */
  private buildQueryParams(): any {
    // Always include pagination parameters
    const queryParams: any = {
      page: this.currentPage + 1, // 1-indexed for URL
      limit: this.pageSize,
    };

    // Add optional filter parameters
    this.addStatusParamIfNeeded(queryParams);
    this.addSearchParamIfNeeded(queryParams);

    return queryParams;
  }

  /**
   * Adds status parameter to query params if it has a value
   */
  private addStatusParamIfNeeded(queryParams: any): void {
    if (this.selectedStatus !== null) {
      queryParams.s = this.selectedStatus;
    }
  }

  /**
   * Adds search parameter to query params if it has content
   */
  private addSearchParamIfNeeded(queryParams: any): void {
    if (this.searchText && this.searchText.trim()) {
      queryParams.q = this.searchText.trim();
    }
  }

  /**
   * Clean up resources on component destruction
   */
  ngOnDestroy(): void {
    // Complete all observables to prevent memory leaks
    this.destroy$.next(true);
    this.destroy$.complete();
    this.searchSubject$.complete();
  }
}
