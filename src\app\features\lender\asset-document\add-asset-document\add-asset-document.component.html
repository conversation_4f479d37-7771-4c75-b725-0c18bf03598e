<form class="" [formGroup]="form" (ngSubmit)="onSubmit()">
  <nb-card class="scrollable-card">
    <nb-card-header>
      <div class="flex flex-wrap -mx-2">
        <h5 class="w-6/12 px-2">
          <div class="title">Add New Document</div>
        </h5>

        <div class="w-6/12 px-2">
          <div class="popup-close float-right">
            <button ghost nbButton (click)="close()">
              <nb-icon icon="close"></nb-icon>
            </button>
          </div>
        </div>
      </div>
    </nb-card-header>
    <nb-card-body>
      <div class="document-popup">
        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label>
              <strong for="inStore">Document Type</strong> <strong class="text-lime required"> &nbsp; * </strong>
            </label>

            <nb-select
              placeholder="Please Select General or Investment Title"
              fullWidth
              size="large"
              shape="semi-round"
              status="basic"
              name="documentType"
              formControlName="documentType"
            >
              <nb-option *ngFor="let document of docTypes" [value]="document.id" [disabled]="document.disabled">
                {{ document.name }}
              </nb-option>
            </nb-select>
            <div *ngIf="submitted && f.documentType.errors" class="invalid-feedback">
              <div *ngIf="f.documentType.errors.required">Document Type is required.</div>
            </div>
          </div>

          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]" *ngIf="form.value.documentType === 54">
            <label>
              <strong for="inStore"> Update Title</strong>
            </label>
            <nb-select
              placeholder="Select Lender"
              fullWidth
              size="large"
              shape="semi-round"
              status="basic"
              name="lenderId"
              formControlName="lenderId"
            >
              <nb-option *ngFor="let lender of lenders" [value]="lender.id">
                {{ lender.updateTitle }}
              </nb-option>
            </nb-select>
          </div>

          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]" *ngIf="form.value.documentType === 54">
            <label>
              <strong for="inStore">Update Category</strong> <strong class="text-lime required"> &nbsp; * </strong>
            </label>

            <nb-select
              fullWidth
              size="large"
              shape="semi-round"
              status="basic"
              name="documentTypeSub"
              formControlName="documentTypeSub"
            >
              <nb-option *ngFor="let document of docTypesSub" [value]="document.id" [disabled]="document.disabled">
                {{ document.name }}
              </nb-option>
            </nb-select>
            <div *ngIf="subDocumentInvalid" class="invalid-feedback">
              <div>Update Category is required.</div>
            </div>
          </div>
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <nb-progress-bar style="width: 100%" *ngIf="progress" [value]="progress" status="primary">
              Uploading {{ progress }}%
            </nb-progress-bar>
          </div>
          <div class="lg:w-full px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <!--[hidden]="uploadedDocuments"-->

            <div class="file-container" appDnd (fileDropped)="onFileDropped($event)">
              <!-- for="fileDropRef"-->

              <input type="hidden" />

              <input
                type="file"
                multiple
                #fileDropRef
                id="fileDropRef"
                (change)="fileBrowseHandler($event)"
                accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
              />

              <p class="m-0" for="fileDropRef">
                <nb-icon icon="file-add"></nb-icon>
                Drop Document here or Click to upload.
              </p>
            </div>

            <div *ngIf="submitted && !this.uploadedDocuments" class="invalid-feedback">
              <div *ngIf="!this.uploadedDocuments">Document is required.</div>
            </div>
          </div>

          <div class="w-8/12 px-2" *ngIf="uploadedDocuments && uploadedDocuments.length > 0">
            <div class="files-list">
              <div class="single-file" *ngFor="let file of uploadedDocuments; let i = index">
                <div class="info">
                  <div class="name">
                    {{ file?.name }}
                  </div>
                  <div class="delete" (click)="clearDocuments(file)">
                    <nb-icon class="file-delete" icon="close-circle"></nb-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="w-full px-2">
        <button
          class="float-right"
          [disabled]="!uploadedDocuments || uploadedDocuments.length < 1"
          [nbSpinner]="loading"
          nbButton
          status="primary"
          style="min-width: 135px"
        >
          IMPORT
        </button>
      </div>
    </nb-card-footer>
  </nb-card>
</form>
