import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { UserTypes } from '@core/helpers';
import { RegisterUser } from '@core/models/auth';
import { TypeKey } from '@core/models/config';
import { AuthenticationService } from '@core/services/authentication.service';
import { OrgService } from '@core/services/org.service';
import { SharedService } from '@core/services/shared.service';
import { UserManagementService } from '@core/services/user-management.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbCardModule,
  NbDialogRef,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxMaskDirective } from 'ngx-mask';
import { SelectModule } from 'primeng/select';
// import { EntityService } from '@core/_services/entity.service';

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbAlertModule,
    NbInputModule,
    NbFormFieldModule,
    NbSpinnerModule,
    NbButtonModule,
    SelectModule,
    NgxMaskDirective,
  ],
})
export class AddUserComponent implements OnInit, AfterViewChecked {
  @Input() getFormParamValue: any;

  roleData: any[] = [];
  orgData: any[] = [];
  userTypeData: any[] = [];
  userForm: UntypedFormGroup;
  loading = false;
  submitted = false;
  returnUrl = '';
  error = '';
  title = '';
  userId: any;
  orgId: number | undefined;
  creditAdvisor = false;
  UserTypes = UserTypes;
  selectedCountry: any;

  pattern = {
    X: {
      pattern: new RegExp('[#+(0-9)]'),
    },
    0: {
      pattern: new RegExp('[(0-9)]'),
    },
  };

  countries = [];

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    public toastr: NbToastrService,
    private userManagementService: UserManagementService,
    private authenticationService: AuthenticationService,
    private sharedService: SharedService,
    // private entityService: EntityService
    private cdr: ChangeDetectorRef,
    private orgService: OrgService,
  ) {
    this.getFormParamValue = {};

    this.userForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,60}$')]],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      mobile: ['', Validators.required],
      countryCode: ['', Validators.required],
      roleId: ['', Validators.required],
      lenderOrgId: ['', Validators.required],
      userTypeId: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.countries = this.sharedService.getMobileCodes().default;
    this.selectedCountry = this.countries.find((ele: any) => ele.dial_code === '+61');

    if (this.getFormParamValue) {
      this.userId = this.getFormParamValue?.userId;
      this.orgId = this.getFormParamValue?.orgId;
    }

    if (this.userId && this.orgId) {
      this.title = 'Edit User';
      this.userManagementService.getUserByIdForAdmin(this.userId).subscribe((userData: any) => {
        if (userData.success) {
          this.userForm.patchValue(userData.payload);
        }
        if (userData.payload.countryCode) {
          this.selectedCountry = this.countries.find((ele: any) => ele.dial_code === userData.payload.countryCode);
        }
        if (userData.payload.userTypeId === UserTypes.Lender) {
          this.getRolesAdmin(UserTypes.Lender);
        } else {
          this.getRolesAdmin();
        }
      });
    } else {
      this.title = 'Add User';
      this.getRolesAdmin();
    }

    this.getOrg();
    this.getUserTypesAdmin();

    this.returnUrl = this.route.snapshot.queryParams.returnUrl || '/';

    this.userForm.controls.userTypeId.valueChanges.subscribe((userType: any) => {
      if (userType !== UserTypes.Lender) {
        this.userForm.controls.lenderOrgId.disable();
      } else {
        this.userForm.controls.lenderOrgId.enable();
        this.userForm.controls.lenderOrgId.reset();
      }
    });
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getRolesAdmin(userType: UserTypes = UserTypes.Staff): void {
    this.userManagementService.getRoleForAdmin(userType).subscribe((roleData: any) => {
      if (roleData.success) {
        this.roleData = roleData.payload;

        if (userType === UserTypes.Lender) {
          const [selectedRole] = this.roleData.filter((ele: any) => ele.id === 5);
          this.userForm.controls.roleId.patchValue(selectedRole?.id);
        }
      }
    });
  }

  private getOrg(): void {
    this.orgService.getOrgByRole(TypeKey.AssetManagement_LenderOrg).subscribe((orgData: any) => {
      if (orgData.success) {
        this.orgData = orgData.payload;
      }
    });
  }

  private getUserTypesAdmin(): void {
    this.userManagementService.getUserTypesAdmin().subscribe((roleData: any) => {
      if (roleData.success) {
        this.userTypeData = roleData.payload;
        this.userTypeData = this.userTypeData.filter((ele: any) => ele.id !== UserTypes.Client);
      }
    });
  }

  close(): void {
    this.dialogRef.close(false);
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.userForm.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    // stop here if form is invalid
    if (this.userForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.userId && this.orgId) {
      this.updateUser();
    } else {
      this.createNewUser();
    }
  }

  private createNewUser(): void {
    const registerData: RegisterUser = {
      email: this.userForm.value.email,
      host: window.location.host,
      mobile: `${this.userForm.value.mobile}`,
      countryCode: this.selectedCountry.dial_code,
      roleId: this.userForm.value.roleId,
      lenderOrgId: this.userForm.value.lenderOrgId,
      userTypeId: this.userForm.value.userTypeId as number,
      profileAttributes: [
        {
          key: 'firstName',
          value: this.userForm.value.firstName,
        },
        {
          key: 'lastName',
          value: this.userForm.value.lastName,
        },
      ],
    };

    if (registerData.userTypeId === UserTypes.Lender) {
      registerData.assetManagementUser = true;
    }

    this.authenticationService.registerWithProfile(registerData).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            if (data.payload) {
              this.loading = false;
              this.dialogRef.close(true);
            }
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  private updateUser(): void {
    const updateUserData = {
      userId: this.userId,
      roleId: this.userForm.value.roleId,
      userTypeId: this.userForm.value.userTypeId,
      firstName: this.userForm.value.firstName,
      lastName: this.userForm.value.lastName,
      email: this.userForm.value.email,
      host: window.location.host,
      mobile: `${this.userForm.value.mobile}`,
      countryCode: this.selectedCountry.dial_code,
      lenderOrgId: this.userForm.value.lenderOrgId,
    };

    this.userManagementService.updateUser(updateUserData).subscribe(
      (data: any) => {
        setTimeout(() => {
          if (data.success) {
            this.loading = false;
            this.dialogRef.close(true);
          } else {
            this.toastr.danger(data.error.message, 'Error!');
            this.loading = false;
          }
        }, 200);
      },
      (err: any) => {
        this.toastr.danger(err.error.message, 'Error!');
        this.loading = false;
      },
    );
  }

  backtoList(): void {
    this.router.navigate(['/users']);
  }

  getCode(code: string): string {
    return this.sharedService.codeToFlag(code);
  }

  updateUserRole(event: any): void {
    this.userForm.controls.roleId.reset();
    this.getRolesAdmin(event);
  }
}
