<div class="flex flex-wrap -mx-2" *ngIf="(isInvestorStaffUsers() || isInvestor()) && !isAssetDashboard()">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Dashboard</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto">
    <!-- <button class="float-right" nbButton status="primary" (click)="createWorkspace()">
            Create New Workspace
        </button> -->
  </div>
</div>

<div class="flex flex-wrap -mx-2" *ngIf="(isInvestorStaffUsers() || isInvestor()) && !isAssetDashboard()">
  <div class="md:w-4/12 sm:w-full xs:w-full px-2" *ngIf="isInvestorStaffUsers()">
    <nb-card>
      <nb-card-body [nbSpinner]="!activeInvestorsData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Active Investors</div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="activeInvestorsData && activeInvestorsData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <p class="chart-data-title">{{ getTotal(activeInvestorsData) }}</p>
              <p>Total</p>
            </div>
          </div>

          <div class="my-[15px] flex flex-wrap -mx-2" style="height: 250px">
            <ngx-charts-pie-chart
              [scheme]="colorScheme"
              [results]="activeInvestorsData"
              [gradient]="gradient"
              [legend]="showLegend"
              [legendPosition]="legendPosition"
              [labels]="showLabels"
              [doughnut]="isDoughnut"
              (select)="onSelect($event)"
              (activate)="onActivate($event)"
              (deactivate)="onDeactivate($event)"
              [legendTitle]="''"
            >
            </ngx-charts-pie-chart>
          </div>
        </ng-container>

        <div class="no-data" *ngIf="activeInvestorsData && activeInvestorsData.length === 0">No Data Available.</div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2" *ngIf="isInvestorStaffUsers()">
    <nb-card>
      <nb-card-body [nbSpinner]="!applicationsMonthToDateData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Applications Month To Date</div>
            </div>
          </div>
        </div>

        <ng-container *ngIf="applicationsMonthToDateData && applicationsMonthToDateData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <p class="chart-data-title">{{ getTotal(applicationsMonthToDateData) }}</p>
              <p>Total</p>
            </div>
          </div>

          <div class="my-[15px] flex flex-wrap -mx-2" style="height: 250px">
            <ngx-charts-pie-chart
              [scheme]="colorScheme"
              [results]="applicationsMonthToDateData"
              [gradient]="gradient"
              [legend]="showLegend"
              [legendPosition]="legendPosition"
              [labels]="showLabels"
              [doughnut]="isDoughnut"
              (select)="onSelect($event)"
              (activate)="onActivate($event)"
              (deactivate)="onDeactivate($event)"
              [legendTitle]="''"
            >
            </ngx-charts-pie-chart>
          </div>
        </ng-container>

        <div class="no-data" *ngIf="applicationsMonthToDateData && applicationsMonthToDateData.length === 0">
          No Data Available.
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2" *ngIf="isInvestorStaffUsers()">
    <nb-card>
      <nb-card-body [nbSpinner]="!fundsInvestedData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Funds Invested</div>
            </div>
          </div>
        </div>

        <ng-container *ngIf="fundsInvestedData && fundsInvestedData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <!-- <p class="chart-data-title">{{getTotal(fundsInvestedData)}}</p> -->
            </div>
          </div>

          <div class="my-[15px] flex flex-wrap -mx-2" style="height: 400px">
            <!-- <ngx-charts-area-chart [scheme]="colorScheme" [showGridLines]="false" [xAxis]="true"
                            [yAxis]="true" [results]="fundsInvestedData" [gradient]="gradient" [legend]="false"
                            [legendPosition]="legendPosition" (select)="onSelect($event)"
                            (activate)="onActivate($event)" (deactivate)="onDeactivate($event)" [legendTitle]="''">
                        </ngx-charts-area-chart> -->
            <ngx-charts-bar-vertical
              [scheme]="colorScheme"
              [results]="fundsInvestedData"
              [gradient]="false"
              [legend]="false"
              [showGridLines]="false"
              (select)="onSelect($event)"
              [xAxis]="true"
              [yAxis]="true"
              (activate)="onActivate($event)"
              [yAxisTickFormatting]="yAxisTickFormatting"
              [dataLabelFormatting]="yAxisTickFormatting"
              (deactivate)="onDeactivate($event)"
              [legendTitle]="''"
            >
            </ngx-charts-bar-vertical>
          </div>
        </ng-container>

        <div class="no-data" *ngIf="fundsInvestedData && fundsInvestedData.length === 0">No Data Available.</div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-8/12 px-2 sm:w-full px-2 w-full px-2" *ngIf="isInvestor()">
    <nb-card>
      <nb-card-body [nbSpinner]="!opportunitiesReviewedData" class="card-body">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title" *ngIf="opportunitiesReviewedData && opportunitiesReviewedData.length > 0">
              <div>Opportunities Reviewed</div>
            </div>
          </div>
          <!-- <div class="md:w-2/12 px-2 text-right" style="margin: auto;">

                        <button class="float-right" nbButton ghost>
                            <nb-icon icon="more-horizontal-outline"></nb-icon>
                        </button>

                    </div> -->
        </div>

        <ng-container *ngIf="opportunitiesReviewedData && opportunitiesReviewedData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <!-- <p class="chart-data-title">{{getTotal(opportunitiesReviewedData)}}</p> -->
            </div>
          </div>

          <div class="my-[15px] flex flex-wrap -mx-2 card-chart">
            <app-ngx-charts-advanced-pie-chart
              [scheme]="colorScheme"
              [results]="opportunitiesReviewedData"
              [animations]="true"
              [valueFormatting]="yAxisTickFormatting"
              [gradient]="false"
              (select)="onSelect($event)"
              (activate)="onActivate($event)"
              (deactivate)="onDeactivate($event)"
            >
            </app-ngx-charts-advanced-pie-chart>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2" *ngIf="isInvestor()">
    <nb-card>
      <nb-card-body [nbSpinner]="!currentInvestmentsData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Current Investments</div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="currentInvestmentsData && currentInvestmentsData.length > 0">
          <div class="ci-list">
            <nb-list>
              <nb-list-item class="list-item" *ngFor="let investment of currentInvestmentsData">
                <div class="list" (click)="investmentDashboard(investment)">
                  <div class="img-investment">
                    <ng-container *ngIf="investment.imageUrl">
                      <img [src]="investment.imageUrl" />
                    </ng-container>

                    <ng-container *ngIf="!investment.imageUrl && investment.documentFileDetail">
                      <img
                        src="{{
                          'data:' +
                            investment.documentFileDetail.contentType +
                            ';base64,' +
                            investment.documentFileDetail.fileData
                        }}"
                      />
                    </ng-container>
                    <ng-container *ngIf="!investment.imageUrl && !investment.documentFileDetail">
                      <nb-icon icon="image-2"></nb-icon>
                    </ng-container>
                  </div>
                  <div class="ci-details">
                    <div class="chart-title">
                      <div>{{ investment.title }}</div>
                    </div>
                    <p style="line-height: 23px">
                      Investment -
                      <span class="text-velvet-700">
                        {{
                          (investment.investedAmount > 0 ? investment.investedAmount : 0)
                            | currency: "USD" : "symbol" : "1.0"
                        }}
                      </span>
                      <br />
                      Return - <span class="text-velvet-700"> {{ investment.investmentReturn }}% </span>
                    </p>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </ng-container>

        <ng-container *ngIf="currentInvestmentsData && currentInvestmentsData.length === 0">
          <div class="no-data">
            <p class="no-data-text">You have no current investments.</p>
            <button routerLink="/investments" nbButton status="primary">Browse Investments</button>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="w-full px-2">
    <nb-card>
      <nb-card-body style="padding: 0">
        <nb-tabset>
          <nb-tab tabTitle="Recent Messages" active>
            <div *ngFor="let logs of recentMessages">
              <div class="logs">
                <div class="clickable" (click)="setShowMessageValue(logs)">
                  <i pBadge *ngIf="logs.isRead !== 'True'" severity="danger"></i>

                  <nb-user
                    color="#002c24"
                    size="medium"
                    [name]="logs.entityName"
                    status="success"
                    [title]="logs.message"
                  >
                  </nb-user>
                  <!-- <p style="margin-left: 50px;">
                                        {{logs.message}}
                                    </p> -->
                  <!-- {{logs.message}} -->
                </div>
                <div>
                  <i>{{ logs.dateCreated | date: "dd/MM/YYYY" }}</i>
                </div>
              </div>
            </div>
          </nb-tab>
          <nb-tab tabTitle="Recent Activities">
            <div *ngFor="let logs of recentActivities">
              <div class="logs">
                <div>
                  <a class="text-velvet-700 hover:no-underline">{{ logs.userName }}</a>
                  {{ logs.message }}
                </div>
                <div>
                  <i>{{ logs.dateCreated | date: "dd/MM/YYYY" }}</i>
                </div>
              </div>
            </div>
          </nb-tab>
        </nb-tabset>
      </nb-card-body>
    </nb-card>
  </div>
</div>

<div *ngIf="(isLender() || isAssetStaffUsers()) && isAssetDashboard()">
  <app-asset-dashboard></app-asset-dashboard>
</div>
