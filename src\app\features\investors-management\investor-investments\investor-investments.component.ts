import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { TypeKey } from '@core/models/config';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbAlertModule,
  NbButtonModule,
  NbDatepickerModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-investor-investments',
  templateUrl: './investor-investments.component.html',
  styleUrls: ['./investor-investments.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NbIconModule,
    NbSelectModule,
    NbAlertModule,
    NbInputModule,
    NbDatepickerModule,
    NbFormFieldModule,
    NbSpinnerModule,
    NbButtonModule,
  ],
})
export class InvestorInvestmentsComponent implements OnInit {
  @Output() changeTab = new EventEmitter<boolean>();

  investmentForm: UntypedFormGroup = new UntypedFormGroup({});
  submitted = false;
  loading = false;
  error = '';

  investmentData: any;
  investorId: any;
  investmentLookup: any;
  id: any;
  entityTypeData: any;

  constructor(
    public spinner: NgxSpinnerService,
    private formBuilder: UntypedFormBuilder,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private investmentService: InvestmentService,
    private dialogService: NbDialogService,
    private toastr: NbToastrService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.investorId = this.sharedService.getFormParamValue.investorId;

    this.investmentForm = this.formBuilder.group({
      investments: this.formBuilder.array([]),
    });

    if (this.sharedService.isAdmin()) {
      this.investmentForm.enable();
    } else {
      this.investmentForm.disable();
    }

    if (this.investorId) {
      this.investmentService.getInvestmentLookup({}).subscribe((response: any) => {
        if (response.success) {
          this.investmentLookup = response.payload;
          this.getInvestments();
        }
      });
      this.getEntityType();
    }
  }

  private getEntityType(): void {
    this.investmentService
      .getEntityType({
        typeKey: TypeKey.InvestmentStatus,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.entityTypeData = userData.payload;
        }
      });
  }

  removeInvestments(investment: any, i: number): void {
    investment = investment.value;
    if (investment.id === 0) {
      this.investments.removeAt(i);
    } else {
      this.dialogService
        .open(ConfirmPopupComponent, {
          context: {
            title: 'Delete Confirm!',
            message: 'Are you sure you want to delete this investment?',
            yesButton: 'Remove',
            noButton: 'Cancel',
          },
          autoFocus: false,
        })
        .onClose.subscribe((res: any) => {
          if (res) {
            this.deleteInvestment(investment, i);
          }
        });
    }
  }

  private deleteInvestment(investment: any, i: number): void {
    this.investorsService
      .deleteInvestment({
        id: investment.id,
        investmentId: investment.investmentId,
        investorId: this.investorId,
      } as any)
      .subscribe((response: any) => {
        if (response.success) {
          this.toastr.success('Removed Successfully', 'Success!');
          this.investments.removeAt(i);
        } else {
          this.toastr.danger(response.error.message, 'Error!');
        }
      });
  }

  private getInvestments(): void {
    this.investments.clear();
    this.investorsService
      .getInvestments({
        investorId: this.investorId,
        forApproval: false,
      } as Filters)
      .subscribe((response: any) => {
        if (response.success) {
          if (response.payload && response.payload.length > 0) {
            this.investmentData = response.payload;
            response.payload.forEach((ele: any) => {
              ele.investmentDate = new Date(ele.investmentDate);
              const [selected] = this.investmentLookup.filter((investment: any) => investment.id === ele.investmentId);
              if (selected) {
                const formData = this.formBuilder.group(ele);
                if (this.sharedService.isAdmin()) {
                  formData.enable();
                } else {
                  formData.disable();
                }

                if (selected.disabled) {
                  formData.controls.investmentId.disable();
                }
                this.investments.push(formData);
              }
            });
          }
        }
      });
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.investmentForm.controls;
  }

  newInvestments(): UntypedFormGroup {
    return this.formBuilder.group({
      id: 0,
      investorId: this.investorId,
      investmentId: [null, Validators.required],
      investmentAmount: [null, Validators.required],
      depositReference: [''],
      investmentDate: [null, Validators.required],
      investmentStatusId: [null, Validators.required],
    });
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  getValue(i: number, controlName: string): string {
    const control = ((this.investmentForm.get('investments') as UntypedFormArray).controls[i] as UntypedFormGroup)
      .controls[controlName];
    return control.value;
  }

  dateChange(event: any): void {
    this.investmentForm.patchValue({
      investmentDate: event,
    });
  }

  public get investments(): UntypedFormArray {
    return this.investmentForm.get('investments') as UntypedFormArray;
  }

  getInvestmentsValidity(i: number, controlName: string): string {
    const control = ((this.investmentForm.get('investments') as UntypedFormArray).controls[i] as UntypedFormGroup)
      .controls[controlName];
    return this.submitted && control.errors?.required && (control.dirty || control.touched || control.invalid)
      ? 'danger'
      : 'basic';
  }

  addInvestments(): void {
    this.submitted = false;
    this.investments.push(this.newInvestments());
    // this.scrollTo('applicant' + (this.investments.length - 1));
  }

  onSubmit(): void {
    this.submitted = true;
    // stop here if form is invalid
    if (this.investmentForm.invalid) {
      return;
    }

    this.loading = true;

    const investmentData = { ...this.investmentForm.value };

    if (this.investorId) {
      this.investorsService.saveInvestment(investmentData.investments).subscribe(
        (data: any) => {
          setTimeout(() => {
            if (data.success) {
              this.toastr.success('Saved Successfully', 'Success!');
              this.loading = false;
              if (this.investmentData) {
                this.getInvestments();
              } else {
                this.changeTab.emit(true);
              }
            } else {
              this.toastr.danger(data.error.message, 'Error!');
              this.loading = false;
            }
          }, 200);
        },
        (err: any) => {
          this.toastr.danger(err.error.message, 'Error!');
          this.loading = false;
        },
      );
    }
  }

  // scrollTo(className: string): void {
  //   setTimeout(() => {
  //     const elementList = document.querySelectorAll('.' + className);
  //     const element = elementList[0] as HTMLElement;
  //     element.scrollIntoView({ behavior: 'smooth' });
  //   }, 300);
  // }
}
