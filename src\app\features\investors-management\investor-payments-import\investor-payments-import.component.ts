import { CommonModule } from '@angular/common';
import { HttpEventType } from '@angular/common/http';
import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { InvestorsService } from '@core/services/investors.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogRef,
  NbIconModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-investor-payments-import',
  templateUrl: './investor-payments-import.component.html',
  styleUrls: ['./investor-payments-import.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbIconModule, NbSpinnerModule, NbButtonModule],
})
export class InvestorPaymentsImportComponent implements OnInit {
  loading = false;
  uploadedDocuments: any;
  investmentId: any;
  investorId: any;
  progress: any;
  submitted = false;

  @Input() getFormParamValue: any;
  @ViewChild('fileDropRef', { static: false }) fileDropRef!: ElementRef;

  constructor(
    protected dialogRef: NbDialogRef<any>,
    public toastr: NbToastrService,
    private investorService: InvestorsService,
    private cdr: ChangeDetectorRef,
  ) { }

  ngOnInit(): void { }

  close(): void {
    this.dialogRef.close(false);
  }

  clearDocuments(): void {
    this.uploadedDocuments = null;
    this.fileDropRef.nativeElement.value = '';
    this.loading = false;
    this.cdr.detectChanges();
  }

  importPayments(): void {
    this.loading = true;
    this.submitted = true;

    // stop here if form is invalid
    if (!this.uploadedDocuments) {
      return;
    }

    this.prepareFilesList(this.uploadedDocuments);
    this.cdr.detectChanges();
  }

  prepareFilesList(files: any[]): void {
    const allowedExtensions = /(\.xls|\.xlsx)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toastr.danger('File type not supported', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = null;
          this.loading = false;
          this.submitted = false;
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toastr.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          this.uploadedDocuments = null;
          this.submitted = false;
          this.loading = false;
          return;
        }
      }
    }

    const formData = new FormData();

    if (this.investmentId) {
      formData.append('investmentId', this.investmentId as any);
      formData.append('investorId', this.investorId as any);
    }

    for (const item of files) {
      item.progress = 0;
      formData.append('Files', item, item.name);
    }

    this.investorService
      .importPayments(formData)
      .pipe(
        map((event: any) => {
          switch (event.type) {
            case HttpEventType.UploadProgress:
              if (event.total) {
                this.progress = Math.round((100 * event.loaded) / event.total);
              }
              return { status: 'progress', message: this.progress };

            case HttpEventType.Response:
              return event.body;
            default:
              return `Unhandled event: ${event.type}`;
          }
        }),
      )
      .subscribe(
        (res: any) => {
          console.log(res);
          if (res instanceof Object) {
            if (res.success) {
              this.loading = false;
              this.dialogRef.close(true);
              this.uploadedDocuments = [];
            } else if (res.error) {
              this.loading = false;
              this.toastr.danger(res.error.message, 'Upload Error!');
            }
          }
        },
        (err: any) => this.toastr.success(err.error.message, 'Failed!'),
      );
  }

  onFileDropped(event: any): void {
    if (event && event.length > 1) {
      this.toastr.danger('Multiple files upload not supported, Please drag single file only', 'Upload Error!');
      return;
    }
    this.uploadedDocuments = event;
  }

  fileBrowseHandler(event: any): void {
    this.uploadedDocuments = event.target.files;
  }
}
