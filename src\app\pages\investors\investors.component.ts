import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmPopupComponent } from '@components/templates/confirm-popup/confirm-popup.component';
import { InvestorStatus, TypeKey } from '@core/models/config';
import { AuthenticationService } from '@core/services/authentication.service';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import { UserManagementService } from '@core/services/user-management.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { SkeletonModule } from 'primeng/skeleton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-investors',
  templateUrl: './investors.component.html',
  styleUrls: ['./investors.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    TableModule,
    NbFormFieldModule,
    TableModule,
    NbInputModule,
    SkeletonModule,
    NbButtonModule,
  ],
})
export class InvestorsComponent implements OnInit, OnDestroy {
  @ViewChild('dt') dt: any;
  @ViewChild('admin', { static: false }) admin: any;
  users: any[] = [];
  statusData: any[] = [];
  sourceData: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  loading = false;

  dtTrigger: Subject<any> = new Subject<any>();
  roleData: any;
  eventFilters: any;
  entityTypeData: any;
  constructor(
    private spinner: NgxSpinnerService,
    private toast: NbToastrService,
    private router: Router,
    private authenticationService: AuthenticationService,
    private sharedService: SharedService,
    private investorsService: InvestorsService,
    private dialogService: NbDialogService,
    private investmentService: InvestmentService,
    private userManagementService: UserManagementService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 1,
      pageSize: 10,
      userTypeId: 1, // Client users only
    } as Filters;

    this.investmentService
      .getEntityType({
        typeKey: TypeKey.InvestorStatus,
      })
      .subscribe((response: any) => {
        if (response.success) {
          this.statusData = response.payload;
        }
      });

    this.investmentService
      .getEntityType({
        typeKey: TypeKey.EntityType,
      })
      .subscribe((userData: any) => {
        if (userData.success) {
          this.entityTypeData = userData.payload;
        }
      });
  }

  private getList(): void {
    this.investorsService.getInvestors(this.filterParams).subscribe((data: any) => {
      if (data.success) {
        this.users = (data as any).payload.investors;
        this.totalRecords = (data as any).payload.rows;
        this.dtTrigger.next(this.users);
        this.spinner.hide();
        this.loading = false;
      }
    });
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.loading = true;
    this.eventFilters = event.filters;
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.filterParams.export = false;
    this.getList();
  }

  exportInvestor(): void {
    this.filterParams.export = true;
  }

  ngOnDestroy(): void {}

  editInvestor(user: any): void {
    this.sharedService.setFormParamValue({
      investorId: user.investorId,
      userId: user.userId,
      changeTab: false,
    });
    this.router.navigate(['/investor/edit']);
  }

  createInvestor(): void {
    this.sharedService.setFormParamValue({});
    this.router.navigate(['/investor/new']);
  }

  onSelectChange(user: any, statusId: string): void {}

  getStatusState(status: string): string {
    return this.sharedService.getStatusState(status);
  }

  timeAgo(time: any): any {
    return this.sharedService.timeAgo(time);
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  deleteInvestorConfirm(user: any): void {
    this.confirmArchive(user);
  }

  confirmArchive(user: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Archive Investor',
          message: 'Are you sure you want to proceed?',
          yesButton: 'Archive Investor',
          yesButtonIcon: 'archive-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.updateStatus(user, InvestorStatus.Archive);
        }
      });
  }

  confirmDisable(user: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Disable Investor',
          message: 'Are you sure you want to disable this investor?',
          yesButton: 'Disable Investor',
          yesButtonIcon: 'lock-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.updateStatus(user, InvestorStatus.Disable); // Disable user
        }
      });
  }

  activeInvestor(user: any): void {
    this.dialogService
      .open(ConfirmPopupComponent, {
        context: {
          title: 'Activate Investor',
          message: 'Are you sure you want to activate this user?',
          yesButton: 'Activate Investor',
          yesButtonIcon: 'unlock-outline',
          yesButtonIconPack: 'eva',
        },
        autoFocus: false,
      })
      .onClose.subscribe((res) => {
        if (res) {
          this.updateStatus(user, InvestorStatus.Active);
        }
      });
  }

  enableIcons(user: any): boolean {
    return user.statusId !== InvestorStatus.Pending;
  }

  updateStatus(user: any, statusId: number): void {
    this.investorsService
      .updateStatus({
        investorId: user.investorId,
        statusId,
      })
      .subscribe((data: any) => {
        if (data.success) {
          if (statusId === InvestorStatus.Active) {
            this.toast.success('Investor Updated successfully.', 'Success!');
          } else if (statusId === InvestorStatus.Archive) {
            this.toast.success('Investor Archived successfully.', 'Success!');
          } else if (statusId === InvestorStatus.Disable) {
            this.toast.success('Investor Disabled successfully.', 'Success!');
          }
          this.getList();
        } else {
          this.toast.danger(data.error.message, 'Error!');
        }
      });
  }

  copyLink(user: any): void {
    this.userManagementService
      .copyLink({
        userId: user.userId,
        host: window.location.host,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.copyToClipboard(data.payload.link);
          this.toast.success(null, 'Link copied to clipboard');
        } else {
          this.toast.danger(data.error.message, 'Error!');
        }
      });
  }

  copyToClipboard(val: string): void {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isManager(): boolean {
    return this.sharedService.isManager();
  }
}
