:host ::ng-deep {
  nb-card {
    box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
    max-height: 85vh !important;
    max-width: 1080px;
  }

  nb-card-body {
    overflow-y: scroll !important;
    margin: 10px 60px 10px 60px;
  }

  ::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
    /* make scrollbar transparent */
  }

  nb-card-footer {
    border-top: 0px !important;
  }

  .p-datatable .p-datatable-header {
    background: white !important;
    color: rgba(143, 155, 179, 1) !important;
    padding: 1rem 1rem !important;
    border: 0px white !important;
    border-width: 0 !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    line-height: 24px !important;
  }
}

.card-body {
  padding: 140px 46px 128px 80px;
}

p {
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
}

.button {
  min-width: 176px;
  min-height: 50px;
  margin-right: 50px;
}
