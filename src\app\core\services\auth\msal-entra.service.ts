import { Inject, inject, Injectable, OnD<PERSON>roy } from '@angular/core';
import {
  MSAL_GUARD_CONFIG,
  MSAL_INSTANCE,
  MsalBroadcastService,
  MsalGuardConfiguration,
  MsalService,
} from '@azure/msal-angular';
import {
  AuthenticationResult,
  EventMessage,
  EventType,
  InteractionStatus,
  InteractionType,
  IPublicClientApplication,
  RedirectRequest,
} from '@azure/msal-browser';
import { HttpResponse } from '@core/models/response/http.response';
import { ExternalLoginResponse } from '@core/models/response/user-data.response';
import { loginEntraRequest } from '@shared/configs/msal-entra-config';
import { BehaviorSubject, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { AuthenticationService, TypeLoginEnum } from '../authentication.service';
import { SharedService } from '../shared.service';

@Injectable({ providedIn: 'root' })
export class EntraAuthService implements OnDestroy {
  private msal: IPublicClientApplication;
  private msalService = inject(MsalService);
  private msalBroadcastService = inject(MsalBroadcastService);
  private authService = inject(AuthenticationService);
  private sharedService = inject(SharedService);

  private msalGuardConfig = inject<MsalGuardConfiguration>(MSAL_GUARD_CONFIG);
  private readonly _destroying$ = new Subject<void>();
  private ready: Promise<void>;
  private initialized = false;

  loading = false;
  isIframe = false;
  // <!--This is to avoid reload during acquireTokenSilent() because of hidden iframe -->
  // <router-outlet *ngIf="!isIframe"></router-outlet>

  public isLoggedIn = false;
  public isAuthorized$ = new BehaviorSubject<boolean>(false);
  public acquireToken$ = new BehaviorSubject<any>(undefined);
  //       await this.msalService.initialize(); // required by MSAL v3+

  constructor(@Inject(MSAL_INSTANCE) msalInstance: IPublicClientApplication) {
    this.isIframe = window !== window.parent && !window.opener;
    this.msal = msalInstance;

    this.ready = this.ensureInitialized();

    // Optional - This will enable ACCOUNT_ADDED and ACCOUNT_REMOVED events emitted when a user logs in or out of another tab or window
    this.msalService.instance.enableAccountStorageEvents();

    this.trackInteractStatus();
  }

  /** Call once on application start-up */
  async init(): Promise<void> {
    await this.ready;
    this.checkAndSetActiveAccount();
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.msal.initialize(); // **mandatory in MSAL v3+**
      this.initialized = true;
    }
  }

  public checkAndSetActiveAccount(isActive?: boolean) {
    this.isLoggedIn = this.msalService.instance.getAllAccounts().length > 0;
    this.isAuthorized$.next(this.isLoggedIn);

    if (!isActive) return;
    const activeAccount = this.msalService.instance.getActiveAccount();

    const accounts = this.msalService.instance.getAllAccounts();
    if (!activeAccount && accounts.length > 0) {
      // add your code for handling multiple accounts here
      this.msalService.instance.setActiveAccount(accounts[0]);
    }
  }

  async login(): Promise<void> {
    try {
      if (this.msalGuardConfig.interactionType === InteractionType.Popup) {
        this.msalService.loginPopup(loginEntraRequest).subscribe((response: AuthenticationResult) => {
          console.log('🚀 loginPopup:', response);
          this.msalService.instance.setActiveAccount(response.account);
        });
      } else {
        if (this.msalGuardConfig.authRequest) {
          this.msalService.loginRedirect({
            ...this.msalGuardConfig.authRequest,
          } as RedirectRequest);
        } else {
          this.msalService.loginRedirect();
        }
      }
    } catch (error) {
      console.log('🚀 ~  ~ error:', error);
    }
  }

  // TODO: UNNECESSARY
  // async logout(): Promise<void> {
  //   const activeAccount = this.msalService.instance.getActiveAccount() || this.msalService.instance.getAllAccounts()[0];

  //   this.msalService.logoutPopup({
  //     account: activeAccount,
  //   });
  //   // this.msalService.logoutRedirect();
  // }

  async getAccessToken(): Promise<string> {
    try {
      const account = this.msalService.instance.getActiveAccount();

      const { accessToken } = await this.msalService.instance.acquireTokenSilent({
        account: account!,
        scopes: loginEntraRequest.scopes,
      });

      this.acquireToken$.next(accessToken);
      return accessToken;
    } catch (error) {
      console.log('🚀  getAccessToken ~ error:', error);
      return '';
    }
  }

  public trackAddAndDeleteAccount() {
    this.msalBroadcastService.msalSubject$
      .pipe(
        filter(
          (msg: EventMessage) =>
            msg.eventType === EventType.ACCOUNT_ADDED || msg.eventType === EventType.ACCOUNT_REMOVED,
        ),
      )
      .subscribe((result: EventMessage) => {
        console.log('🚀 ~ trackAddAndDeleteAccount', result);
        if (this.msalService.instance.getAllAccounts().length === 0) {
          window.location.pathname = '/';
        } else {
          this.checkAndSetActiveAccount();
        }
      });
  }
  public trackInteractStatus() {
    this.msalBroadcastService.inProgress$
      .pipe(
        filter((status: InteractionStatus) => status === InteractionStatus.None),
        takeUntil(this._destroying$),
      )
      .subscribe((val) => {
        console.log('🚀 ~ trackInteractStatus ~ . ~ val:', val);
        this.checkAndSetActiveAccount(true);
      });
  }

  public trackLogoutState() {
    this.msalBroadcastService.msalSubject$
      .pipe(
        filter((msg: EventMessage) => msg.eventType === EventType.LOGOUT_SUCCESS),
        takeUntil(this._destroying$),
      )
      .subscribe((result: EventMessage) => {
        console.log('🚀 ~ trackLogoutState:', result);
        this.checkAndSetActiveAccount();
      });
  }

  public trackLoginState() {
    this.msalBroadcastService.msalSubject$
      .pipe(
        filter(
          (msg: EventMessage) =>
            msg.eventType === EventType.LOGIN_SUCCESS ||
            msg.eventType === EventType.ACQUIRE_TOKEN_SUCCESS ||
            msg.eventType === EventType.SSO_SILENT_SUCCESS,
        ),
        takeUntil(this._destroying$),
      )
      .subscribe((result: EventMessage) => {
        this.loading = true; // Set loading state to true

        const payload = result.payload as AuthenticationResult;
        console.log('🚀 ~ trackLoginState:', result);

        this.msalService.instance.setActiveAccount(payload.account);

        this.authService.externalLogin(payload.accessToken, TypeLoginEnum.Microsoft).subscribe(
          (userResponse: HttpResponse<ExternalLoginResponse>) => {
            console.log('🚀 ~ this.authService.externalLogin ~ userResponse:', userResponse);
            this.sharedService.authenticate(userResponse?.payload?.token, userResponse);
          },
          (error: any) => {
            console.error('🚀 ~ this.authService.externalLogin ~ error:', error);
          },
        );
      });
  }

  public trackLoginFailed() {
    this.msalBroadcastService.msalSubject$
      .pipe(
        filter(
          (msg: EventMessage) =>
            msg.eventType === EventType.LOGIN_FAILURE || msg.eventType === EventType.ACQUIRE_TOKEN_FAILURE,
        ),
        takeUntil(this._destroying$),
      )
      .subscribe((result: EventMessage) => {
        console.log('🚀 ~ trackLoginFailed', result);
      });
  }

  public trackEventsEntra() {
    this.trackAddAndDeleteAccount();
    this.trackLoginState();
    this.trackLogoutState();
    this.trackLoginFailed();
  }

  ngOnDestroy() {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }
}
