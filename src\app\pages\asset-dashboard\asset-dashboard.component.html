<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Sydney Wyde Capital Partners Dashboard</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto">
    <!-- <button class="float-right" nbButton status="primary" (click)="createWorkspace()">
            Create New Workspace
        </button> -->
  </div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="!activeLoanByTypeData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Loan <PERSON>folio Composition</div>
            </div>
          </div>
        </div>

        <ng-container *ngIf="activeLoanByTypeData && activeLoanByTypeData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <p class="chart-data-title">{{ getTotal(activeLoanByTypeData) }}</p>
              <p>Total</p>
            </div>
          </div>

          <div #activeLoanByTypeDataRef class="my-[15px] flex flex-wrap -mx-2" style="height: 250px">
            <ngx-charts-pie-chart
              [view]="[activeLoanByTypeDataRef.offsetWidth, activeLoanByTypeDataRef.offsetHeight]"
              [scheme]="colorScheme"
              [results]="activeLoanByTypeData"
              [gradient]="gradient"
              [legend]="showLegend"
              [legendPosition]="legendPosition"
              [labels]="showLabels"
              [doughnut]="isDoughnut"
              (select)="onSelect($event)"
              (activate)="onActivate($event)"
              (deactivate)="onDeactivate($event)"
              [legendTitle]="''"
            >
            </ngx-charts-pie-chart>
          </div>
        </ng-container>

        <div class="no-data" *ngIf="activeLoanByTypeData && activeLoanByTypeData.length === 0">No Data Available.</div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="!geoLocationSummaryData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Geographic Location</div>
            </div>
          </div>
        </div>

        <ng-container *ngIf="geoLocationSummaryData && geoLocationSummaryData.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <p class="chart-data-title">{{ getTotal(geoLocationSummaryData) }}</p>
              <p>Total</p>
            </div>
          </div>

          <div #geoLocationSummaryDataRef class="my-[15px] flex flex-wrap -mx-2" style="height: 250px">
            <ngx-charts-pie-chart
              [view]="[geoLocationSummaryDataRef.offsetWidth, geoLocationSummaryDataRef.offsetHeight]"
              [scheme]="colorScheme"
              [results]="geoLocationSummaryData"
              [gradient]="gradient"
              [legend]="showLegend"
              [legendPosition]="legendPosition"
              [labels]="showLabels"
              [doughnut]="isDoughnut"
              (select)="onSelect($event)"
              (activate)="onActivate($event)"
              (deactivate)="onDeactivate($event)"
              [legendTitle]="''"
            >
            </ngx-charts-pie-chart>
          </div>
        </ng-container>

        <div class="no-data" *ngIf="geoLocationSummaryData && geoLocationSummaryData.length === 0">
          No Data Available.
        </div>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="!drawnBalanceSummary" class="card-body">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Total Drawn Balance</div>
            </div>
          </div>
        </div>

        <!--
                <div class="flex flex-wrap -mx-2">
                    <div class="md:w-full px-2" style="margin: auto;">
                        <div class="chart-title" *ngIf="drawnBalanceSummary && drawnBalanceSummary.length > 0">
                            <div> {{drawnBalanceSummary[0].title}} </div>
                        </div>
                    </div>

                </div> -->

        <ng-container *ngIf="drawnBalanceSummary && drawnBalanceSummary.length > 0">
          <div class="my-[15px]">
            <div class="text-center">
              <!-- <p class="chart-data-title">{{getTotal(opportunitiesReviewedData)}}</p> -->
            </div>
          </div>

          <div #drawnBalanceSummaryRef class="my-[15px] flex flex-wrap -mx-2 card-chart">
            <app-ngx-charts-advanced-pie-chart
              [view]="[drawnBalanceSummaryRef.offsetWidth, drawnBalanceSummaryRef.offsetHeight]"
              [scheme]="colorScheme"
              [results]="drawnBalanceSummary"
              [animations]="false"
              [valueFormatting]="yAxisTickFormatting"
              [gradient]="false"
              (select)="onSelect($event)"
              (activate)="onActivate($event)"
              (deactivate)="onDeactivate($event)"
            >
            </app-ngx-charts-advanced-pie-chart>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="w-full px-2">
    <nb-card>
      <nb-card-body style="padding: 0">
        <nb-tabset>
          <nb-tab tabTitle="Recent Messages" active>
            <div style="height: 400px; overflow: auto">
              <div *ngFor="let logs of recentMessages">
                <div class="logs">
                  <div class="clickable" (click)="setShowMessageValue(logs)">
                    <!-- <i pBadge *ngIf="logs.isRead !== 'True'" severity="danger"></i> -->
                    <div class="recent-msg">
                      <nb-user
                        color="#002c24"
                        size="medium"
                        [name]="logs.userName"
                        [showName]="false"
                        [showTitle]="false"
                        status="success"
                        [title]="getMessageText(logs)"
                      >
                      </nb-user>
                      <div>
                        <div>
                          <strong class="text-bold-500">{{ logs.userName }} </strong>
                          <span *ngIf="logs.facilityName">
                            | <span class="text-blue-600"> {{ logs.facilityName }} </span>
                          </span>
                        </div>
                        <div>
                          <span class="user-title">{{ logs.message }} </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <i>{{ logs.dateCreated | date: "dd/MM/YYYY" }}</i>
                  </div>
                </div>
              </div>
            </div>
          </nb-tab>
        </nb-tabset>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="!loanInvestmentsData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Land & Investment Loans</div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="loanInvestmentsData && loanInvestmentsData.length > 0">
          <div class="ci-list">
            <nb-list>
              <nb-list-item class="list-item" *ngFor="let investment of loanInvestmentsData">
                <div class="list" (click)="investmentDashboard(investment)">
                  <div class="img-investment">
                    <ng-container *ngIf="investment.imageUrl">
                      <img [src]="investment.imageUrl" />
                    </ng-container>

                    <ng-container *ngIf="!investment.imageUrl">
                      <nb-icon icon="image-2"></nb-icon>
                    </ng-container>
                  </div>
                  <div class="ci-details">
                    <div class="chart-title">
                      <div>{{ investment.facilityName }}</div>
                    </div>
                    <p style="line-height: 23px">
                      Facility Limit -
                      <span class="text-blue-600">
                        {{
                          (investment.facilityLimit > 0 ? investment.facilityLimit : 0)
                            | currency: "USD" : "symbol" : "1.0"
                        }}
                      </span>
                      <br />
                      LVR Limit - <span class="text-blue-600"> {{ investment.lvrLimit }}% </span>
                    </p>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </ng-container>

        <ng-container *ngIf="loanInvestmentsData && loanInvestmentsData.length === 0">
          <div class="no-data">
            <p class="no-data-text">You have no loan investments.</p>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="!constructionData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Construction</div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="constructionData && constructionData.length > 0">
          <div class="ci-list">
            <nb-list>
              <nb-list-item class="list-item" *ngFor="let construction of constructionData">
                <div class="list" (click)="investmentDashboard(construction)">
                  <div class="img-investment">
                    <ng-container *ngIf="construction.imageUrl">
                      <img [src]="construction.imageUrl" />
                    </ng-container>

                    <ng-container *ngIf="!construction.imageUrl">
                      <nb-icon icon="image-2"></nb-icon>
                    </ng-container>
                  </div>
                  <div class="ci-details">
                    <div class="chart-title">
                      <div>{{ construction.facilityName }}</div>
                    </div>
                    <p style="line-height: 23px">
                      Facility Limit -
                      <span class="text-blue-600">
                        {{
                          (construction.facilityLimit > 0 ? construction.facilityLimit : 0)
                            | currency: "USD" : "symbol" : "1.0"
                        }}
                      </span>
                      <br />
                      LVR Limit - <span class="text-blue-600"> {{ construction.lvrLimit }}% </span>
                    </p>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </ng-container>

        <ng-container *ngIf="constructionData && constructionData.length === 0">
          <div class="no-data">
            <p class="no-data-text">You have no construction data.</p>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>

  <div class="md:w-4/12 px-2 sm:w-full px-2 w-full px-2">
    <nb-card>
      <nb-card-body [nbSpinner]="!watchlistData" style="min-height: 500px; height: auto">
        <div class="flex flex-wrap -mx-2">
          <div class="md:w-full px-2" style="margin: auto">
            <div class="chart-title">
              <div>Watchlist</div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="watchlistData && watchlistData.length > 0">
          <div class="ci-list">
            <nb-list>
              <nb-list-item class="list-item" *ngFor="let watchlist of watchlistData">
                <div class="list" (click)="investmentDashboard(watchlist)">
                  <div class="img-investment">
                    <ng-container *ngIf="watchlist.imageUrl">
                      <img [src]="watchlist.imageUrl" />
                    </ng-container>

                    <ng-container *ngIf="!watchlist.imageUrl">
                      <nb-icon icon="image-2"></nb-icon>
                    </ng-container>
                  </div>
                  <div class="ci-details">
                    <div class="chart-title">
                      <div>{{ watchlist.facilityName }}</div>
                    </div>
                    <p style="line-height: 23px">
                      Facility Limit -
                      <span class="text-blue-600">
                        {{
                          (watchlist.facilityLimit > 0 ? watchlist.facilityLimit : 0)
                            | currency: "USD" : "symbol" : "1.0"
                        }}
                      </span>
                      <br />
                      LVR Limit - <span class="text-blue-600"> {{ watchlist.lvrLimit }}% </span>
                    </p>
                  </div>
                </div>
              </nb-list-item>
            </nb-list>
          </div>
        </ng-container>

        <ng-container *ngIf="watchlistData && watchlistData.length === 0">
          <div class="no-data">
            <p class="no-data-text">You have no watchlist.</p>
          </div>
        </ng-container>
      </nb-card-body>
    </nb-card>
  </div>
</div>
