import { HTTP_INTERCEPTORS, provideHttpClient, with<PERSON><PERSON><PERSON>, withInterceptorsFromDi } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';

// Third-party modules
import { NbDateFnsDateModule } from '@nebular/date-fns';
import { NbEvaIconsModule } from '@nebular/eva-icons';
import { NbRoleProvider, NbSecurityModule } from '@nebular/security';
import * as theme from '@nebular/theme';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { provideEnvironmentNgxMask } from 'ngx-mask';
import { NgxSpinnerModule } from 'ngx-spinner';

// PrimeNG modules
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SidebarModule } from 'primeng/sidebar';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';

// Routes
import { routes } from './app.routes';

// Services and providers
import { SocialLoginModule } from '@abacritt/angularx-social-login';
import { CurrencyPipe } from '@angular/common';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { MsalModule } from '@azure/msal-angular';
import { RoleProvider } from '@core/services/role.provider';
import Aura from '@primeng/themes/aura';
import { MSALProviders } from '@shared/configs/msal-entra-config';
import { providePrimeNG } from 'primeng/config';
import { BasicAuthInterceptor, ErrorInterceptor } from './core/helpers';
import { socialAuthConfig } from './core/models/social-auth-config';
import { KeysPipe } from './core/pipes/keys.pipe';
import { NbMenuInternalService, NbMenuService } from './core/services/menu.service';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideAnimations(),
    provideHttpClient(withInterceptorsFromDi(), withFetch()),

    // Import third-party modules
    provideEnvironmentNgxMask(),
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset: Aura,
        options: {
          darkModeSelector: 'none',
        },
      },
    }),
    importProvidersFrom(
      // Angular forms
      FormsModule,
      ReactiveFormsModule,

      // PrimeNG modules
      ButtonModule,
      CheckboxModule,
      DropdownModule,
      SkeletonModule,
      MultiSelectModule,
      CalendarModule,
      TableModule,
      DialogModule,
      SidebarModule,
      BadgeModule,
      SelectButtonModule,

      // Nebular modules
      NbEvaIconsModule,
      NbSecurityModule.forRoot(),
      NgxSpinnerModule,
      theme.NbThemeModule.forRoot({ name: 'default' }),
      theme.NbLayoutModule,
      theme.NbSidebarModule.forRoot(),
      theme.NbCardModule,
      theme.NbInputModule,
      theme.NbCheckboxModule,
      theme.NbButtonModule,
      theme.NbUserModule,
      theme.NbActionsModule,
      theme.NbContextMenuModule,
      theme.NbAlertModule,
      theme.NbSpinnerModule,
      theme.NbBadgeModule,
      theme.NbIconModule,
      theme.NbSelectModule,
      theme.NbToastrModule.forRoot(),
      theme.NbFormFieldModule,
      theme.NbPopoverModule,
      theme.NbTooltipModule,
      theme.NbDialogModule.forRoot(),
      theme.NbDatepickerModule.forRoot(),
      theme.NbMenuModule.forRoot(), // Ensure this is included in the root module

      theme.NbToggleModule,
      theme.NbStepperModule,
      theme.NbRadioModule,
      theme.NbProgressBarModule,
      theme.NbTagModule,
      theme.NbAutocompleteModule,
      theme.NbListModule,
      // DynamicFormBuilderModule,
      NbDateFnsDateModule.forRoot({ format: 'dd/MM/yyyy' }),
      NgxChartsModule,
      MsalModule,
      SocialLoginModule.initialize(socialAuthConfig),
    ),

    ...MSALProviders(),

    // Services
    KeysPipe,
    CurrencyPipe,
    { provide: HTTP_INTERCEPTORS, useClass: BasicAuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    { provide: NbRoleProvider, useClass: RoleProvider },
    Title,
    NbMenuService,
    NbMenuInternalService,
  ],
};
