<div class="flex flex-row flex-wrap mb-2" *ngIf="droppedFiles?.length">
  <ng-container *ngFor="let file of droppedFiles">
    <div *ngIf="file.urlStyle" [style.background-image]="file.urlStyle">
      <span class="absolute -right-2 -top-3.5 text-sm leading-none cursor-pointer" (click)="removeFile(file)"
        >&times;</span
      >
    </div>
    <div
      [nbTooltip]="file.name"
      nbTooltipStatus="primary"
      nbTooltipPlacement="top"
      class="bg-cover w-12 h-12 rounded-md mb-2 mr-2 border !border-velvet-700 text-center text-2xl relative"
    >
      <nb-icon
        *ngIf="!file.urlStyle"
        icon="file-text-outline"
        pack="nebular-essentials"
        class="!w-[65%] !h-full"
      ></nb-icon>
      <span class="absolute -right-2 -top-3.5 text-sm leading-none cursor-pointer" (click)="removeFile(file)"
        >&times;</span
      >
    </div>
  </ng-container>
</div>
<div *ngIf="!disabled" class="message-form-bar">
  <div *ngIf="hasGlobalMessageTypeAsset">
    <nb-select
      class="select-facility"
      [disabled]="disabled"
      [placeholder]="'Facility'"
      [(ngModel)]="selectedFacilityId"
      [ngModelOptions]="{ standalone: true }"
      (ngModelChange)="onFacilityChange($event)"
    >
      <nb-option [value]="null"> -- Select -- </nb-option>
      <nb-option *ngFor="let facility of facilities" [value]="facility.id">{{ facility.facilityName }}</nb-option>
    </nb-select>
  </div>

  <div style="flex: 1; margin-right: 7px">
    <nb-form-field>
      <input
        rows="1"
        nbInput
        fullWidth
        [status]="getInputStatus()"
        (focus)="inputFocus = true"
        (blur)="inputFocus = false"
        (mouseenter)="inputHover = true"
        (mouseleave)="inputHover = false"
        [(ngModel)]="message"
        [class.with-button]="showButton"
        type="text"
        maxlength="500"
        placeholder="{{ fileOver ? dropFilePlaceholder : messagePlaceholder }}"
        (keyup.enter)="sendMessage()"
      />

      <button nbSuffix nbButton ghost style="width: 60px; height: 40px">
        <div class="file-container" appDnd (fileDropped)="onFileDropped($event)">
          <input
            type="file"
            #fileDropRef
            id="fileDropRef"
            [multiple]="false"
            (change)="fileBrowseHandler($event)"
            accept="image/png, image/jpeg, image/jpg, .pdf, .xls, .xlsx, .doc, .docx, .odt"
          />
          <div class="m-0" for="fileDropRef">
            <nb-icon icon="attach-outline"></nb-icon>
          </div>
        </div>
      </button>
    </nb-form-field>
  </div>
  <div>
    <button
      nbButton
      [status]="getButtonStatus()"
      *ngIf="showButton"
      [class.with-icon]="!buttonTitle"
      (click)="sendMessage()"
      class=""
    >
      <div *ngIf="!showFilter; then sendText; else updateText"></div>
      <ng-template #sendText> SEND </ng-template>
      <ng-template #updateText> UPDATE </ng-template>
      <ng-template #title>{{ buttonTitle }}</ng-template>
    </button>
  </div>

  <div *ngIf="isAdmin" class="send-button-area">
    <nb-checkbox fieldSize="large" (checkedChange)="isInternalNoteToggle()" [(ngModel)]="isInternalNote">
      Internal Note
    </nb-checkbox>
  </div>
</div>
<div class="send-button-area"></div>
<div></div>
