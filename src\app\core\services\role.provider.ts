import { Injectable } from '@angular/core';
import { NbRoleProvider } from '@nebular/security';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AuthenticationService } from './authentication.service';

@Injectable()
export class RoleProvider implements NbRoleProvider {
  constructor(private authService: AuthenticationService) {}

  getRole(): Observable<string> {
    return this.authService.user.pipe(
      map((token: any) => {
        return token.orgList[0].roles[0].displayName.split(' ').join('');
      }),
    );
  }
}
