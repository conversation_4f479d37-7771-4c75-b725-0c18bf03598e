import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogRef, NbCardModule, NbButtonModule, NbIconModule } from '@nebular/theme';
import { NgxSpinnerService } from 'ngx-spinner';
import { DocumentService } from '@core/services/document.service';
declare const google: any;

const place = null as typeof google.maps.places.PlaceResult;

@Component({
  selector: 'app-confirm-popup',
  templateUrl: './confirm-popup.component.html',
  styleUrls: ['./confirm-popup.component.scss'],
  standalone: true,
  imports: [CommonModule, NbCardModule, NbButtonModule, NbIconModule],
})
export class ConfirmPopupComponent implements OnInit {
  @Input() message: string;
  @Input() title?: string;
  @Input() icon?: string;
  @Input() yesButton: string;
  @Input() noButton: string;
  @Input() yesButtonIcon = '';
  @Input() yesButtonIconPack = 'default';
  @Input() yesSuccessPopUp = 'Yes';

  constructor(
    protected dialogRef: NbDialogRef<any>,
    public spinner: NgxSpinnerService,
  ) {
    this.message = '';
    this.yesButton = 'Yes';
    this.noButton = 'No';
  }

  async ngOnInit(): Promise<void> {}

  close(response: boolean): void {
    this.dialogRef.close(response);
  }
}
