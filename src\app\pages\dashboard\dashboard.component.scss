@use "themes" as *;

:host {
  .title h5 {
    text-decoration-line: underline;
    text-underline-offset: 5px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }
}

.chart-title {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  color: var(--color-velvet-700);
}

.logs {
  display: flex;
  justify-content: space-between;
  padding: 14px 6px;
  border-bottom: 1px solid #f0f0f0;
  justify-content: space-between;
}
.img-investment {
  height: 120px;
  width: 120px;
  border-radius: 10px;
  margin: 15px;
}
.img-investment img {
  object-fit: cover;
  border-radius: 10px;
  width: 100%;
  height: 100%;
}
.img-investment nb-icon {
  color: #f3f3f3;
  height: 120px;
  width: 120px;
  border: 1px solid #f3f3f3;
  border-radius: 10px;
}
.list {
  display: flex;
  align-items: center;
  flex-direction: row;
  width: 100%;
}

.list-item :hover,
.selected {
  background: var(--color-mist);
  border-radius: 10px;
  cursor: pointer;
}

.advanced-pie-legend-wrapper {
  width: 100% !important;
}

.no-data-text {
  font-weight: normal;
  font-size: 20px;
  line-height: 32px;
  color: var(--color-velvet-700);
  text-align: center;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .card-body {
    min-height: 300px !important;
    height: auto;
  }
  .card-chart {
    height: 300px !important;
    display: grid;
  }
}

.card-body {
  min-height: 500px;
  height: auto;
}
.card-chart {
  height: 400px;
  display: grid;
}
