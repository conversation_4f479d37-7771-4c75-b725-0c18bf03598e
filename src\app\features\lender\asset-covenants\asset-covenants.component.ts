import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { RegisterUser } from '@core/models/auth';
import { AssetTaskType } from '@core/models/config';
import { AssetService } from '@core/services/asset.service';
import { DocumentService } from '@core/services/document.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDialogService,
  NbIconModule,
  NbInputModule,
  NbListModule,
  NbPopoverModule,
  NbSpinnerModule,
  NbToastrService,
  NbTooltipModule,
  NbUserModule,
} from '@nebular/theme';
import { TableModule } from 'primeng/table';
import { AssetAddTaskComponent } from '../asset-add-task/asset-add-task.component';
import { AssetNotesComponent } from '../asset-notes/asset-notes.component';

@Component({
  selector: 'app-asset-covenants',
  templateUrl: './asset-covenants.component.html',
  styleUrls: ['./asset-covenants.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbUserModule,
    NbTooltipModule,
    NbButtonModule,
    NbInputModule,
    NbPopoverModule,
    NbSpinnerModule,
    NbListModule,
    TableModule,
  ],
})
export class AssetCovenantsComponent implements OnInit {
  @Output() userChange = new EventEmitter<RegisterUser>();
  @Output() unsavedItemsEvent = new EventEmitter<number>();
  facility: any;
  unsavedItems = false;
  tabId = 7; // covenant tabId = 6 (7 - 1)
  loading = false;
  submitLoading = false;
  updating = false;
  resetTaskObject: any = {};

  filterParams: any = {};
  userId: any;

  taskList!: any[];
  originatorUsers: any;
  allUploadedDocuments: any = [];
  allNotes: any = [];

  constructor(
    public sanitizer: DomSanitizer,
    private assetService: AssetService,
    private sharedService: SharedService,
    private dialogService: NbDialogService,
    private toast: NbToastrService,
    private documentService: DocumentService,
  ) {}

  ngOnInit(): void {
    if (this.sharedService.isAdmin() || this.sharedService.isOriginatorManager()) {
      this.userId = this.sharedService.getUserIdValue.userId;
      if (this.sharedService.getFormParamValue.facility) {
        this.facility = this.sharedService.getFormParamValue.facility;
        this.userChange.emit(this.facility);
      }

      this.convenantView();
    }
  }

  convenantView(): void {
    this.loading = true;
    this.assetService.getLenderOriginatorUsers().subscribe((data: any) => {
      // get originator users for mapping
      if (data.success) {
        this.originatorUsers = (data as any).payload.userResult.filter(
          (user: any) => user.roleId === 4 || user.roleId == 1,
        );
        this.assetService
          .getCovenantTasks(AssetTaskType.Covenant, this.sharedService.getFormParamValue.assetKeyDataId)
          .subscribe(
            (res: any) => {
              if (res.success) {
                this.taskList = res.payload;
                const mapUserBoolean = this.taskList.find(
                  (row) => Boolean(row.loanManagerId) || Boolean(row.reviewerId),
                );
                if (mapUserBoolean) {
                  this.taskList = this.taskList.reduce((agg: any, item: any) => {
                    const o = { ...item };
                    if (item.loanManagerId) {
                      const loanManagerObj = this.originatorUsers.find((i: any) => i.userId == item.loanManagerId);
                      o['loanManagerName'] = loanManagerObj ? loanManagerObj.contactName : 'No User';
                      o['loanManagerDate'] = new Date(item.loanManagerDate);
                    }
                    if (item.reviewerId) {
                      const reviewerObj = this.originatorUsers.find((i: any) => i.userId == item.reviewerId);
                      o['reviewerName'] = reviewerObj ? reviewerObj.contactName : 'No User';
                      o['reviewerDate'] = new Date(item.reviewerDate);
                    }
                    if (item.completedBy) {
                      o['completedByDate'] = new Date(item.completedByDate);
                    }
                    agg.push(o);
                    return agg;
                  }, []);
                }
                this.calculateDocuments();
                this.calculateNotes();
                this.loading = false;
              } else {
                this.loading = false;
                this.toast.danger(res.error.message, 'Oops!');
              }
            },
            () => {
              this.loading = false;
              this.toast.danger('Something went wrong', 'Oops!');
            },
          );
      }
    });
  }

  editTask(task: any) {
    this.dialogService
      .open(AssetAddTaskComponent, {
        context: {
          data: task ? task : null,
          edit: task ? true : false,
          type: AssetTaskType.Covenant,
        },
        autoFocus: false,
        hasBackdrop: true,
        closeOnEsc: false,
        closeOnBackdropClick: false,
        hasScroll: true,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          this.unsavedItemsEvent.emit(0);
          this.unsavedItems = false;
          this.convenantView();
        }
      });
  }

  editNote(data: any, inputNote: any) {
    let userNotes = null,
      userIndex: string | number | null = null,
      editMode = true;
    const notes = data['notes'];
    if (inputNote) {
      const note = notes.find((et: any) => et.id == inputNote.id);
      userNotes = note;
      if (note.userId == this.userId) {
        userIndex = notes.findIndex((et: any) => et.userId == this.userId);
        editMode = true;
      } else {
        editMode = false;
      }
    } else if (notes && notes.find((et: any) => et.userId == this.userId)) {
      const note = notes.find((et: any) => et.userId == this.userId);
      userIndex = notes.findIndex((et: any) => et.userId == this.userId);
      userNotes = note;
    }
    this.dialogService
      .open(AssetNotesComponent, {
        context: {
          editorContent: userNotes ? userNotes['noteContent'] : null,
          uploadedDocuments: userNotes ? userNotes['noteDocuments'] || [] : [],
          pendingDocuments: userNotes ? userNotes['documents'] || [] : [],
          deleteDocuments: userNotes ? userNotes['deletedDocuments'] || [] : [],
          taskId: data.id,
          userId: this.userId,
          noteId: userNotes ? userNotes['id'] : null,
          taskType: AssetTaskType.Covenant,
          editMode: editMode,
        },
        autoFocus: false,
      })
      .onClose.subscribe((res: any) => {
        if (res) {
          if (res.updated) {
            this.convenantView();
          }
        }
      });
  }

  toggleTaskCompletion(type: string, data: any) {
    if (this.isOriginatorManager()) {
      // A single originator manager should not be able to complete both sign off & review
      if (
        (type == 'loanManager' && data['reviewerId'] == this.userId) ||
        (type == 'reviewer' && data['loanManagerId'] == this.userId)
      ) {
        return;
      }

      data[type + 'Id'] = this.userId;
      const matched = this.originatorUsers.find((i: any) => i.userId == data[type + 'Id']);
      data[type + 'Name'] = matched.contactName;
      data[type + 'Date'] = new Date(Date.now());
      data[type + 'Edited'] = true;

      if (data['loanManagerId'] && data['reviewerId']) {
        data['completedBy'] = data['reviewerId'] || this.userId;
        data['completedByDate'] = new Date(Date.now());
      }
      this.unsavedItemsEvent.emit(this.tabId);
      this.unsavedItems = true;
    } else if (this.isAdmin() && this.resetTaskObject[type + data['id']]) {
      data[type + 'Date'] = this.resetTaskObject[type + data['id']][type + 'Date'];
      data[type + 'Id'] = this.resetTaskObject[type + data['id']][type + 'Id'];
      data[type + 'Name'] = this.resetTaskObject[type + data['id']][type + 'Name'];
      data[type + 'Edited'] = this.resetTaskObject[type + data['id']][type + 'Edited'];
      this.resetTaskObject[type + data['id']] = null;
      this.unsavedItems = false;
    }
  }

  resetTaskCompletion(type: string, data: any) {
    if (this.isAdmin() || data[type + 'Edited']) {
      this.resetTaskObject[type + data['id']] = {
        [type + 'Date']: data[type + 'Date'],
        [type + 'Id']: data[type + 'Id'],
        [type + 'Name']: data[type + 'Name'],
        [type + 'Edited']: false,
      };
      data[type + 'Date'] = null;
      data[type + 'Id'] = null;
      data[type + 'Name'] = null;
      data['completedBy'] = null;
      data['completedByDate'] = null;

      data[type + 'Edited'] = false;
      this.unsavedItemsEvent.emit(0);
      this.unsavedItems = false;
      if (this.isAdmin()) {
        data[type + 'Edited'] = true;
        this.unsavedItemsEvent.emit(this.tabId);
        this.unsavedItems = true;
      }
    }
  }

  submitTaskChanges() {
    // filter rows where loanmanager or reviewer check has been updated
    if (this.updating) {
      return;
    } else {
      const updatedItems = this.taskList.filter((row) => Boolean(row.loanManagerEdited) || Boolean(row.reviewerEdited));
      if (this.unsavedItems) {
        this.updating = true;
        this.submitLoading = true;
        this.assetService.saveTask(updatedItems).subscribe(
          (res: any) => {
            if (res.success) {
              this.unsavedItemsEvent.emit(0);
              this.unsavedItems = false;
              this.submitLoading = false;
              this.updating = false;
              this.toast.success('Covenants Updated', 'Success!');
              this.convenantView();
            } else if (res.error) {
              this.updating = false;
              this.submitLoading = false;
              this.toast.danger(res.error.message, 'Update Error!');
            }
          },
          (err: any) => this.toast.success(err.error.message, 'Covenants Failed!'),
        );
      } else {
        this.unsavedItems = false;
        this.toast.warning('No task changes to update.', 'Error!');
        this.submitLoading = false;
        this.updating = false;
      }
    }
  }

  calculateNotes() {
    const noteObjects: any = {};
    this.taskList.forEach((el: any) => {
      if (el.id) {
        noteObjects[el.id] = [];
        if (el.notes) {
          el.notes.forEach((note: any) => {
            const userObj = this.originatorUsers.find((i: any) => i.userId == note.userId);
            note.userName = userObj ? userObj.contactName : 'No User';
          });
          noteObjects[el.id] = el.notes;
        }
      }
    });
    this.allNotes = noteObjects;
  }

  calculateDocuments() {
    const documentObjects: any = {};
    this.taskList.forEach((el: any) => {
      if (el.id) {
        documentObjects[el.id] = [];
        if (el.notes) {
          el.notes.forEach((note: any) => {
            if (note.noteDocuments) {
              documentObjects[el.id] = documentObjects[el.id].concat(note.noteDocuments);
            }
          });
        }
      }
    });
    this.allUploadedDocuments = documentObjects;
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isOriginatorManager(): boolean {
    return this.sharedService.isOriginatorManager();
  }
}
