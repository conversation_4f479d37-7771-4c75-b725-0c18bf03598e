import { Injectable, inject } from '@angular/core';
import { CanDeactivateFn, UrlTree } from '@angular/router';
import { NbDialogService } from '@nebular/theme';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { DirtyCheckComponent } from '@components/templates/dirty-check/dirty-check.component';
import { DirtyComponent } from './dirty-component';

@Injectable({ providedIn: 'root' })
export class DirtyCheckGuardService {
  constructor(private dialogService: NbDialogService) {}

  canDeactivate(
    component: DirtyComponent,
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (component && component.canDeactivate()) {
      return this.dialogService
        .open(DirtyCheckComponent, {
          context: '',
          autoFocus: false,
        })
        .onClose.pipe(map((data) => data));
    } else {
      return true;
    }
  }
}

// New functional guard approach for Angular 16
export const DirtyCheckGuard: CanDeactivateFn<DirtyComponent> = (component: DirtyComponent) => {
  return inject(DirtyCheckGuardService).canDeactivate(component);
};
