{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "697ba792-a632-41f3-8c99-36529db41652", "schematicCollections": ["angular-eslint"]}, "version": 1, "newProjectRoot": "projects", "projects": {"Sydney-Wyde": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/Sydney-<PERSON><PERSON><PERSON>"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/primeicons/primeicons.css", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "stylePreprocessorOptions": {"includePaths": ["src/app/shared/styles"]}, "scripts": ["node_modules/jquery/dist/jquery.js"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "128kb", "maximumError": "128kb"}]}, "uat": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "128kb", "maximumError": "128kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "Sydney-Wyde:build", "port": 3000}, "configurations": {"production": {"buildTarget": "Sydney-Wyde:build:production"}, "uat": {"buildTarget": "Sydney-Wyde:build:uat"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"outputPath": "src/locale", "format": "xlf"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "Sydney-Wyde:serve"}, "configurations": {"production": {"devServerTarget": "Sydney-Wyde:serve:production"}, "uat": {"devServerTarget": "Sydney-Wyde:build:uat"}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}