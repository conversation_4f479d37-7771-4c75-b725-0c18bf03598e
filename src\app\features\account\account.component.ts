import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { AuthenticationService } from '@core/services/authentication.service';
import { SharedService } from '@core/services/shared.service';
import { NbLayoutModule } from '@nebular/theme';

@Component({
  selector: 'app-account',
  templateUrl: './account.component.html',
  styleUrls: ['./account.component.scss'],
  standalone: true,
  imports: [NbLayoutModule, RouterOutlet],
})
export class AccountComponent implements OnInit {
  constructor(
    private router: Router,
    private sharedService: SharedService,
    private authenticationService: AuthenticationService,
  ) {
    this.sharedService.loadImages();
  }

  ngOnInit(): void {
    if (this.authenticationService.userValue && !this.router.url.includes('authenticate')) {
      this.router.navigate(['/']);
    }
  }
}
