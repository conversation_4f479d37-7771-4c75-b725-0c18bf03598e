import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalWithDataTableComponent } from './modal-with-data-table.component';

describe('ModalWithDataTableComponent', () => {
  let component: ModalWithDataTableComponent;
  let fixture: ComponentFixture<ModalWithDataTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalWithDataTableComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalWithDataTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
