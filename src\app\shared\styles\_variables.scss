// Sydney Wyde Brand Variables Only
// =================================
// This file contains only the SCSS variables for easy importing in components

// Primary Brand Colors
$sw-velvet-green: #002c24;
$sw-velvet-green-rgb: 0, 44, 36;

$sw-velvet-green-light: #0f3b33;
$sw-velvet-green-light-rgb: 15, 59, 51;

$sw-closing-bell: #021a12;
$sw-closing-bell-rgb: 2, 26, 18;

// Neutral Colors
$sw-mist: #e7eceb;
$sw-mist-rgb: 231, 236, 235;

$sw-white: #ffffff;
$sw-white-rgb: 255, 255, 255;

// Text Colors
$sw-grey: #576460;
$sw-grey-rgb: 87, 100, 96;

$sw-light-grey: #a6b2ae;
$sw-light-grey-rgb: 166, 178, 174;

// Accent Color
$sw-lime: #86b503;
$sw-lime-rgb: 134, 181, 3;

// Semantic Color Assignments
$sw-primary: $sw-velvet-green;
$sw-primary-light: $sw-velvet-green-light;
$sw-primary-dark: $sw-closing-bell;

$sw-background: $sw-white;
$sw-background-alt: $sw-mist;

$sw-text-primary: $sw-velvet-green;
$sw-text-secondary: $sw-grey;
$sw-text-tertiary: $sw-light-grey;
$sw-text-inverse: $sw-white;

$sw-accent: $sw-lime;

// Typography Variables
$sw-font-feature: "Bitter", serif;
$sw-font-heading: "Lato", sans-serif;
$sw-font-body: "Lato", sans-serif;

$sw-font-weight-light: 300;
$sw-font-weight-regular: 400;
$sw-font-weight-medium: 500;
$sw-font-weight-semibold: 600;
$sw-font-weight-bold: 700;

// Spacing Variables (following 8px grid)
$sw-space-xs: 0.25rem; // 4px
$sw-space-sm: 0.5rem; // 8px
$sw-space-md: 1rem; // 16px
$sw-space-lg: 1.5rem; // 24px
$sw-space-xl: 2rem; // 32px
$sw-space-2xl: 3rem; // 48px
$sw-space-3xl: 4rem; // 64px

// Border Radius Variables
$sw-radius-sm: 0.25rem; // 4px
$sw-radius-md: 0.5rem; // 8px
$sw-radius-lg: 0.75rem; // 12px
$sw-radius-xl: 1rem; // 16px

// Transition Variables
$sw-transition-fast: 0.15s ease-in-out;
$sw-transition-normal: 0.3s ease-in-out;
$sw-transition-slow: 0.5s ease-in-out;

// Z-index Variables
$sw-z-dropdown: 1000;
$sw-z-sticky: 1020;
$sw-z-fixed: 1030;
$sw-z-modal-backdrop: 1040;
$sw-z-modal: 1050;
$sw-z-popover: 1060;
$sw-z-tooltip: 1070;

// Breakpoint Variables
$sw-breakpoints: (
    sm: 576px,
    md: 768px,
    lg: 992px,
    xl: 1200px,
    xxl: 1400px
);

// SCSS Maps for easier iteration
$sw-colors: (
    primary: $sw-primary,
    primary-light: $sw-primary-light,
    primary-dark: $sw-primary-dark,
    accent: $sw-accent,
    mist: $sw-mist,
    white: $sw-white,
    grey: $sw-grey,
    light-grey: $sw-light-grey
);

$sw-text-colors: (
    primary: $sw-text-primary,
    secondary: $sw-text-secondary,
    tertiary: $sw-text-tertiary,
    inverse: $sw-text-inverse,
    accent: $sw-accent
);

$sw-spacing: (
    xs: $sw-space-xs,
    sm: $sw-space-sm,
    md: $sw-space-md,
    lg: $sw-space-lg,
    xl: $sw-space-xl,
    2xl: $sw-space-2xl,
    3xl: $sw-space-3xl
);

$sw-radii: (
    sm: $sw-radius-sm,
    md: $sw-radius-md,
    lg: $sw-radius-lg,
    xl: $sw-radius-xl
);
