<nb-card>
  <nb-card-header>
    <div class="flex flex-wrap -mx-2">
      <h5 class="w-6/12 px-2">
        <div class="title">Search User</div>
      </h5>

      <div class="w-6/12 px-2">
        <div class="popup-close float-right">
          <button ghost nbButton (click)="close()">
            <nb-icon icon="close"></nb-icon>
          </button>
        </div>
      </div>
    </div>
  </nb-card-header>

  <nb-card-body style="max-width: 750px">
    <div>
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>

    <br />

    <p-skeleton *ngIf="!users"></p-skeleton>

    <div *ngIf="users">
      <p-table
        #dt
        [filterDelay]="700"
        [value]="users"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [responsive]="true"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="['userId', 'firstName', 'lastName', 'entity', 'orgName', 'roleName']"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 100px">
              <div>
                <div>First Name</div>
              </div>
            </th>
            <th style="min-width: 100px">
              <div>
                <div>Last Name</div>
              </div>
            </th>
            <th style="min-width: 225px">
              <div>
                <div>Email</div>
              </div>
            </th>
            <th style="min-width: 125px">
              <div>
                <div>Mobile</div>
              </div>
            </th>
            <th style="min-width: 100px; text-align: center">Action</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-user>
          <tr>
            <td style="min-width: 100px">{{ user.firstName }}</td>
            <td style="min-width: 100px">{{ user.lastName }}</td>
            <td style="min-width: 225px">{{ user.email }}</td>
            <td style="min-width: 125px">{{ user.mobile }}</td>
            <td style="min-width: 100px">
              <button nbButton ghost shape="round" status="primary" class="button-icon" (click)="close(user)">
                Select
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" *ngIf="!eventFilters" [attr.colspan]="10">No users found</td>
            <td style="text-align: center; display: block" *ngIf="eventFilters" [attr.colspan]="10">
              Sorry, your search did not return any matching results. Please try again
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </nb-card-body>
</nb-card>
