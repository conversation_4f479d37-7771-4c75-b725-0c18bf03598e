import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewEncapsulation,
} from '@angular/core';
import {
  BaseChartComponent,
  calculateViewDimensions,
  ColorHelper,
  DataItem,
  NgxChartsModule,
  ScaleType,
  ViewDimensions,
} from '@swimlane/ngx-charts';

@Component({
  selector: 'app-ngx-charts-advanced-pie-chart',
  templateUrl: 'advanced-pie-chart.component.html',
  styleUrls: ['./base-chart.component.scss', './advanced-pie-chart.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgxChartsModule],
})
export class YSAdvancedPieChartComponent extends BaseChartComponent {
  @Input() gradient!: boolean;
  @Input() activeEntries: any[] = [];
  @Input() tooltipDisabled = false;
  @Input() tooltipText: any;
  @Input() label = 'Total';

  @Output() activate = new EventEmitter<any>();
  @Output() deactivate = new EventEmitter<any>();

  @ContentChild('tooltipTemplate') tooltipTemplate!: TemplateRef<any>;

  dims!: ViewDimensions;
  domain!: string[];
  outerRadius!: number;
  innerRadius!: number;
  transform!: string;
  colors!: ColorHelper;
  legendWidth!: number;
  margin: number[] = [20, 20, 20, 20];

  @Input() valueFormatting!: (value: number | string | Date) => any;
  @Input() nameFormatting!: (value: string) => any;
  @Input() percentageFormatting!: (value: number) => any;

  override update(): void {
    super.update();

    this.dims = calculateViewDimensions({
      width: (this.width * 4) / 12.0,
      height: this.height,
      margins: this.margin,
    });

    this.formatDates();

    this.domain = this.getDomain();
    this.setColors();

    const xOffset = this.dims.width / 2;
    const yOffset = this.margin[0] + this.dims.height / 2;
    this.legendWidth = this.width - this.dims.width - this.margin[1];

    this.outerRadius = Math.min(this.dims.width, this.dims.height) / 2.5;
    this.innerRadius = this.outerRadius * 0.75;

    this.transform = `translate(${xOffset} , ${yOffset})`;
  }

  getDomain(): string[] {
    return this.results.map((d: { label: any }) => d.label);
  }

  onClick(data: DataItem) {
    this.select.emit(data);
  }

  setColors(): void {
    this.colors = new ColorHelper(this.scheme, ScaleType.Ordinal, this.domain, this.customColors);
  }

  onActivate(item: { name: any; value: any; series?: any }, fromLegend = false): void {
    item = this.results.find((d: { label: any; name: any }) => {
      if (fromLegend) {
        return d.label === item.name;
      } else {
        return d.name === item.name;
      }
    });

    const idx = this.activeEntries.findIndex((d) => {
      return d.name === item.name && d.value === item.value && d.series === item.series;
    });
    if (idx > -1) {
      return;
    }

    this.activeEntries = [item, ...this.activeEntries];
    this.activate.emit({ value: item, entries: this.activeEntries });
  }

  onDeactivate(item: { name: any; value: any; series?: any }, fromLegend = false): void {
    item = this.results.find((d: { label: any; name: any }): boolean => {
      if (fromLegend) {
        return d.label === item.name;
      } else {
        return d.name === item.name;
      }
    });

    const idx = this.activeEntries.findIndex((d) => {
      return d.name === item.name && d.value === item.value && d.series === item.series;
    });

    this.activeEntries.splice(idx, 1);
    this.activeEntries = [...this.activeEntries];

    this.deactivate.emit({ value: item, entries: this.activeEntries });
  }
}
