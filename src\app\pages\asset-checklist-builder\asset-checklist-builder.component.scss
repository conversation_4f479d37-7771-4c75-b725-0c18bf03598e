@use "themes" as *;

:host {
  .title h5,
  .chart-title {
    text-decoration-line: underline;
    text-underline-offset: 10px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }
}

.chart-title {
  font-size: 18px;
  line-height: 30px;
}

.text-right {
  text-align: right;
}

nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

.buttonRow {
  margin: 40px;
  justify-content: space-between;
}

.new-item-button {
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(62, 69, 80, 1) !important;
  color: rgba(62, 69, 80, 1) !important;
}

.draggable-item {
  line-height: 50px;
  width: 100%;
  display: block;
  background-color: #fff;
  outline: 0;
  border: 1px solid rgba(0, 0, 0, 0.125);
  margin-bottom: 20px;
  margin-top: 20px;
}

.card-ghost {
  transition: transform 0.18s ease;
  transform: rotateZ(5deg);
}

.card-ghost-drop {
  transition: transform 0.18s ease-in-out;
  transform: rotateZ(0deg);
}

:host ::ng-deep {
  .smooth-dnd-container.vertical > .smooth-dnd-draggable-wrapper {
    overflow: hidden;
    display: block;
  }

  .smooth-dnd-container.vertical {
    background: white !important;
    border-radius: 0px 0px 10px 10px;
    min-height: 150px;
    margin-bottom: 20px;
  }
  .ck-editor__editable_inline {
    min-height: 500px;
  }
}

.ck-content {
  line-height: 1.6;
  word-break: break-word;
}

.editor-container_classic-editor .editor-container__editor {
  min-width: 795px;
  max-width: 795px;
}
