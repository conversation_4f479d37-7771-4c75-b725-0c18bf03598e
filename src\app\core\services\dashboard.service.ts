import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Filters } from './shared.service';
import { environment } from 'src/environments/environment';
import { ExportService } from './export.service';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  constructor(
    private http: HttpClient,
    private exportService: ExportService,
  ) {}

  getFundsInvested(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/funds-invested`, userFilters);
  }

  getFundsDeployed(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/funds-deployed`, userFilters);
  }

  getInterestPaidByOffering(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/interest-paid-by-offering`, userFilters);
  }

  getActiveInvestors(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/active-investors`, userFilters);
  }

  getApplicationsMonthToDate(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/applications-month-todate`, userFilters);
  }

  getInvestedAmountByOffering(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/invested-amount-by-offering`, userFilters);
  }

  getCurrentInvestments(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/current-investments`, userFilters);
  }

  getRecentLogs(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/recent-logs`, userFilters);
  }

  getInvestorGlobalChat(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/investor-global-chat`, userFilters);
  }

  getOpportunitiesReviewed(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/opportunities-reviewed`, userFilters);
  }

  getCurrentInvestmentOverview(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/current-investment-overview`, userFilters);
  }

  getMonthlyDistribution(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/monthly-distribution`, userFilters);
  }

  getTransactionHistory(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Dashboard/transaction-history`, userFilters);
  }

  getDocumentsByType(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/dashboard/get-documents-bytype`, userFilters);
  }

  async getCompliances(filters: any): Promise<void> {
    this.exportService.export(
      filters,
      `${environment.apiURL}/api/dashboard/compliance`,
      `Logs_${new Date().getTime()}.xlsx`,
    );
  }
}
