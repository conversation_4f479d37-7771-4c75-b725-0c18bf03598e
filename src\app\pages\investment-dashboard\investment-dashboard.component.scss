@use "themes" as *;

:host {
  .title h5 {
    text-decoration-line: underline;
    text-underline-offset: 5px;
    font-weight: 400;
    text-decoration-color: nb-theme(menu-item-active-text-color);
  }
}

.chart-title {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  color: var(--color-velvet-700);
}

.logs {
  display: flex;
  justify-content: space-between;
  padding: 14px 6px;
  border-bottom: 1px solid #f0f0f0;
  justify-content: space-between;
  align-items: center;
}

.file-icon {
  font-size: 35px !important;
  color: #838d9c;
  min-height: 35px !important;
  min-width: 35px !important;
}

.file-name {
  overflow-wrap: anywhere !important;
  word-wrap: break-word !important;
  padding: 0px 20px;
}

.img-investment {
  height: 120px;
  width: 120px;
  border-radius: 10px;
  margin: 15px;
}

.img-investment img {
  object-fit: cover;
  border-radius: 10px;
  width: 100%;
  height: 100%;
}

.img-investment nb-icon {
  color: #f3f3f3;
  height: 120px;
  width: 120px;
  border: 1px solid #f3f3f3;
  border-radius: 10px;
}

.list {
  display: flex;
  align-items: center;
  flex-direction: row;
  width: 100%;
}

.list-item :hover,
.selected {
  background: var(--color-mist);
  border-radius: 10px;
  cursor: pointer;
}

.advanced-pie-legend-wrapper {
  width: 100% !important;
}

.no-data-text {
  font-weight: normal;
  font-size: 20px;
  line-height: 32px;
  color: var(--color-velvet-700);
  text-align: center;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .card-body {
    min-height: 300px !important;
    height: auto;
  }

  .card-chart {
    height: 300px !important;
    display: grid;
  }
}

.card-body {
  min-height: 500px;
  height: auto;
}

.card-chart {
  height: 400px;
  display: grid;
}

.image-container {
  position: relative;
  text-align: center;
  color: white;

  img,
  .img {
    width: 100%;
    height: 410px;
    object-fit: cover;
    object-position: 80% 100%;
    border-radius: 10px;
    color: #f7f7f7;
  }

  .invested {
    position: absolute;
    left: 0px;
    top: 48px;
    font-weight: 900;
    font-size: 22px;
    line-height: 24px;
    color: #ffffff;
    background: var(--color-velvet-700);
    padding: 10px;
    box-shadow: 1px 1px 3px #6c757d;
  }

  .location {
    position: absolute;
    left: 22px;
    bottom: 21px;
    font-size: 16px;
    line-height: 24px;
    color: #fbfbfb;
    font-weight: bold;
    text-shadow: 2px 3px 2px #495057;
  }
}

.total {
  font-weight: normal;
  font-size: 22px;
  line-height: 28px;
  color: var(--color-grey-600);
  margin: 10px 0px;
}

.nb-scroll-body {
  max-height: 431px;
  min-height: 431px;
  overflow: auto;
  padding: 0 10px;
}
