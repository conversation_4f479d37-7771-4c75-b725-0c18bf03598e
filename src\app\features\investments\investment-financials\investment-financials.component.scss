nb-card {
  box-shadow: 0px 1px 20px 0px rgba(0, 0, 0, 0.08) !important;
}

:host ::ng-deep .html-contant {
  // font-size: 16px;
  // line-height: 30px;
  p,
  ul,
  ul li {
    line-height: 30px;
  }
  ul,
  ol {
    padding-left: 20px;
  }
  ol {
    list-style-type: decimal;
  }
}

:host ::ng-deep .html-contant {
  font-size: 16px;
  line-height: 30px;
}

h6 {
  margin-block-start: 1.33em;
  margin-block-end: 1.33em;
}

p {
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 30px;
}

.title-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.title {
  font-weight: 600;
  font-size: 20px;
  line-height: 40px;
  color: #0a0a0a;
}

.outer-circle,
.inner-circle {
  left: 13px !important;
}

.p-mb-3 {
  margin-bottom: 1rem !important;
}
.p-d-flex {
  display: flex !important;
}

.icon-list-item {
  margin: 5px;
  border-radius: 20px;
}

.icon-list-item img {
  height: 50px;
  width: 50px;
}

.icon-list-item:hover {
  cursor: pointer;
  background: #f8f8f8;
  background: rgba(0, 0, 0, 0.08);
}

.html-contant {
  margin-left: 12px;
}

.cke_editable {
  cursor: text;
  border: 1px solid #e4e9f2;
  border-radius: 10px;
}

.disable-html {
  cursor: text;
  border: 1px solid #e4e9f2;
  border-radius: 10px;
  margin-left: 12px;
  height: 168px;
  background-color: #e7eceb;
  padding: 10px;
  margin: 12px;
}
