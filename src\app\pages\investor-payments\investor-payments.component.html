<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Payments</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2" *ngIf="!isInvestor">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        fullWidth
        placeholder="Type"
        name="transactionType"
        id="transactionType"
        (selectedChange)="dt.filter($event, 'transactionTypeId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let source of transactionTypeData" [value]="source.id">
          {{ source.name }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <nb-select
        fullWidth
        placeholder="Investment"
        id="investmentId"
        (selectedChange)="dt.filter($event, 'investmentId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let source of investmentData" [value]="source.id">
          {{ source.title }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <nb-select
        placeholder="Date"
        name="paymentDate"
        id="value"
        [selected]=""
        (selectedChange)="dt.filter($event, 'paymentDate', 'equals')"
      >
        <nb-option *ngFor="let dateFilter of dateFilterData" [value]="dateFilter.value">
          {{ dateFilter.name }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>
  </div>

  <div class="lg:w-3/12 px-2 my-[15px] text-right">
    <button nbButton ghost status="primary" *ngIf="!isManager" (click)="importPayment()">
      <i class="pi pi-upload"></i>
    </button>

    <button nbButton ghost status="primary" *ngIf="!isManager" (click)="exportPayment()">
      <i class="pi pi-download"></i>
    </button>
  </div>
</div>

<div *ngIf="payments">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="payments"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="false"
        [rows]="200"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="[
          'id',
          'description',
          'transactionType',
          'investment',
          'investor',
          'dateCreated',
          'amount',
          'investmentId',
          'transactionTypeId',
        ]"
        sortField="id"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 100px; max-width: 100px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>

            <th style="width: 250px" [pSortableColumn]="'description'">
              <div>
                <div>Description</div>
                <p-sortIcon [field]="'description'"></p-sortIcon>
              </div>
            </th>

            <th style="width: 250px" [pSortableColumn]="'investment'" *ngIf="!isInvestor">
              <div>
                <div>Investment</div>
                <p-sortIcon [field]="'investment'"></p-sortIcon>
              </div>
            </th>

            <th style="width: 250px" [pSortableColumn]="'investor'" *ngIf="!isInvestor">
              <div>
                <div>Investor</div>
                <p-sortIcon [field]="'investor'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'transactionType'">
              <div>
                <div>Transaction Type</div>
                <p-sortIcon [field]="'transactionType'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'paymentDate'">
              <div>
                <div>Date</div>
                <p-sortIcon [field]="'paymentDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'amount'">
              <div>
                <div>Amount</div>
                <p-sortIcon [field]="'amount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 125px" *ngIf="isAdmin">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-payment>
          <tr>
            <td style="min-width: 100px; max-width: 100px">{{ payment.id }}</td>
            <td style="width: 250px">{{ payment.description }}</td>
            <td style="width: 250px" *ngIf="!isInvestor">{{ payment.investment }}</td>
            <td style="width: 250px" *ngIf="!isInvestor">{{ payment.investor }}</td>
            <td style="min-width: 150px">{{ payment.transactionType }}</td>
            <td style="min-width: 150px">{{ payment.paymentDate | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 100px">{{ payment.amount | currency: "USD" : "symbol" : "1.0" }}</td>
            <td style="min-width: 125px" *ngIf="isAdmin">
              <button
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                (click)="deletePayment(payment.id)"
                nbTooltip="Delete Payment"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="trash-2-outline"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" [attr.colspan]="isInvestor ? 5 : 7">No payments yet.</td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
