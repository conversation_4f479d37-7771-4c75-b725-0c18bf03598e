import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { NbTabsetComponent } from '@components/molecules/tabset/tabset.component';
import { SharedService } from '@core/services/shared.service';
import { AssetCovenantsComponent } from '@features/lender/asset-covenants/asset-covenants.component';
import { AssetDocumentsComponent } from '@features/lender/asset-document/asset-documents/asset-documents.component';
import { AssetDynamicChecklistComponent } from '@features/lender/asset-dynamic-checklist/asset-dynamic-checklist.component';
import { AssetKeyDataComponent } from '@features/lender/asset-key-data/asset-key-data.component';
import { AssetLenderUpdatesComponent } from '@features/lender/asset-lender-updates/asset-lender-updates.component';
import { AssetNotesChatComponent } from '@features/lender/asset-notes-chat/asset-notes-chat.component';
import { NbAccordionModule, NbCardModule, NbIconModule, NbInputModule, NbTabsetModule } from '@nebular/theme';
@Component({
  selector: 'app-asset-details',
  templateUrl: './asset-details.component.html',
  styleUrls: ['./asset-details.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbTabsetModule,
    NbAccordionModule,
    NbInputModule,
    AssetKeyDataComponent,
    AssetLenderUpdatesComponent,
    AssetDocumentsComponent,
    AssetDynamicChecklistComponent,
    AssetNotesChatComponent,
    AssetCovenantsComponent,
  ],
})
export class AssetDetailsComponent implements OnInit, OnDestroy {
  isMobile = false;

  @ViewChild('tabset') tabsetEl!: NbTabsetComponent;

  assetKeyDataId: any;
  entityName: any;
  tabId = 1;
  userId: any;
  checklistIds: any[] = [
    { id: 1, title: 'Set Up Checklist', tabId: '4' },
    { id: 2, title: 'Closing Checklist', tabId: '7' },
  ];
  unsavedItems = 0;
  unsavedNavigate = false;
  constructor(
    private sharedService: SharedService,
    protected cd: ChangeDetectorRef,
  ) { }
  ngOnDestroy(): void {
    // this.sharedService.setFormParamValue({});
  }

  async ngOnInit(): Promise<void> {
    this.isMobile = this.getIsMobile();
    window.onresize = () => {
      this.isMobile = this.getIsMobile();
    };
    this.assetKeyDataId = this.sharedService.getFormParamValue.assetKeyDataId;
    this.userId = this.sharedService.getFormParamValue.userId;
    this.tabId = this.sharedService.getFormParamValue.tabId ? this.sharedService.getFormParamValue.tabId : 1;

    const changeTab = this.sharedService.getFormParamValue.changeTab;
    if (changeTab) {
      this.sharedService.setFormParamValue({
        userId: this.userId,
        assetKeyDataId: this.assetKeyDataId,
        facility: this.sharedService.getFormParamValue?.facility,
        changeTab: false,
        tabId: 0, // reset
      });
      this.changeTab(null);
    }
  }

  getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 700;
    if (w < breakpoint) {
      return true;
    } else {
      return false;
    }
  }

  userChange(event: any): void {
    this.entityName = event.facilityName;
  }

  tabClick(event: any): void {
    if (this.unsavedItems !== 0 && !this.unsavedNavigate) {
      if (confirm('Are you sure you want to exit this screen without saving the data?')) {
        this.unsavedItems = 0;
        this.unsavedNavigate = false;
      } else {
        const activateTab = this.tabsetEl.tabs.find((t: any) => t.tabId === (this.unsavedItems - 1).toString());
        if (activateTab) {
          this.unsavedNavigate = true;
          this.tabsetEl.selectTab(activateTab);
        }
        this.cd.detectChanges();
        return;
      }
    }
    this.unsavedNavigate = false;
    this.tabId = Number(event.tabId);
  }

  changeTab(event: any): void {
    setTimeout(() => {
      if (this.tabsetEl.tabs) {
        const activateTab = this.tabsetEl.tabs.find((t: any) => t.tabId === (this.tabId + 1).toString());
        if (activateTab) {
          this.tabsetEl.selectTab(activateTab);
        }
        this.cd.detectChanges();
      }
    }, 1000);
  }
}
