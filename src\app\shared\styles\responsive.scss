// .p-sidebar-bottom {
//   bottom: 0;
//   left: 2% !important;
//   width: 96% !important;
//   height: calc(100vh - 200px) !important;
//   height: -moz-calc(100vh - 200px) !important;
//   height: -webkit-calc(100vh - 200px) !important;
//   max-height: calc(100vh - 200px) !important;
//   max-height: -moz-calc(100vh - 200px) !important;
//   max-height: -webkit-calc(100vh - 200px) !important;
//   border-top-left-radius: 13px;
//   border-top-right-radius: 13px;
//   z-index: 12000000 !important;
// }

.hide-chat {
  width: 33.33%;
}

.hide-tab-mobile {
  display: inline-block;
}

.ci-list {
  overflow: auto;
  height: 428px;
}

@media only screen and (device-width: 768px) {
  // .logo {
  //   width: 100%;
  // }

  .deals-body {
    min-height: auto !important;
    max-height: fit-content !important;
  }

  .art-scroll {
    max-height: fit-content !important;
    min-height: auto !important;
  }
  .admin-login-padding {
    padding: 0px 5px !important;
  }

  .auth-container {
    margin: 25% auto;
    max-width: 100%;
  }

  .col-20-per {
    flex: 0 0 30% !important;
    max-width: 30% !important;
  }

  .hide-chat {
    width: 33.33%;
  }

  .p-dialog {
    width: 100% !important;
    overflow: auto !important;
  }
}

@media only screen and (min-device-width: 481px) and (max-device-width: 1024px) and (orientation: portrait) {
  // .logo {
  //   width: 100%;
  // }
  .col-20-per {
    flex: 0 0 30% !important;
    max-width: 30% !important;
  }
  .hide-chat {
    width: 33.33%;
  }
}

@media only screen and (min-device-width: 481px) and (max-device-width: 1024px) and (orientation: landscape) {
  // .logo {
  //   width: 100%;
  // }
  .col-20-per {
    flex: 0 0 30% !important;
    max-width: 30% !important;
  }
  .hide-chat {
    width: 33.33%;
  }
}

// Mobile Only
@media only screen and (min-device-width: 200px) and (max-device-width: 480px) and (-webkit-min-device-pixel-ratio: 2) {
  // .logo {
  //   width: 100%;
  // }

  nb-option-list .option-list {
    --option-list-max-height: 18rem !important;
  }
  .p-dialog {
    width: 100% !important;
    overflow: auto !important;
  }

  .show-on-mobile {
    display: contents !important;
  }

  .hide-chat {
    width: 0px !important;
    display: none;
  }

  .mobile-logo {
    width: 26px;
    object-fit: cover;
    object-position: 0% 100%;
  }

  button.mb-4 {
    width: 100%;
  }

  .hide-on-mobile {
    display: none;
  }

  .artical img {
    width: 100%;
    height: auto;
  }

  .deals-body {
    min-height: 200px !important;
    max-height: fit-content !important;
  }

  .art-scroll {
    max-height: fit-content !important;
    min-height: auto !important;
  }

  nb-card,
  nb-card-body {
    width: 100% !important;
  }

  .admin-login-padding {
    padding: 0px 5px !important;
  }

  .mobile-logo {
    max-height: 50px;
  }

  .hide-on-mobile {
    display: none;
  }

  .mobile-button {
    padding: 5px !important;
    font-size: 12px !important;
  }

  .hide-tab-mobile {
    display: none !important;
  }

  .col-20-per {
    flex: 0 0 50% !important;
    max-width: 50% !important;
  }

  .user-list-item {
    min-width: 200px !important;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  // .p-sidebar-bottom {
  //   height: calc(100vh - 150px) !important;
  //   height: -moz-calc(100vh - 150px) !important;
  //   height: -webkit-calc(100vh - 150px) !important;
  //   max-height: calc(100vh - 150px) !important;
  //   max-height: -moz-calc(100vh - 150px) !important;
  //   max-height: -webkit-calc(100vh - 150px) !important;
  // }

  app-messages {
    app-chat.size-large {
      overflow-y: auto !important;
      height: calc(100vh - 252px) !important;
      height: -moz-calc(100vh - 252px) !important;
      height: -webkit-calc(100vh - 252px) !important;
      max-height: calc(100vh - 252px) !important;
      max-height: -moz-calc(100vh - 252px) !important;
      max-height: -webkit-calc(100vh - 252px) !important;
    }
  }

  .list {
    max-height: calc(100vh - 300px) !important;
    max-height: -moz-calc(100vh - 300px) !important;
    max-height: -webkit-calc(100vh - 300px) !important;
    height: calc(100vh - 300px) !important;
    height: -moz-calc(100vh - 300px) !important;
    height: -webkit-calc(100vh - 300px) !important;
  }

  .list {
    display: flex !important;
    align-items: flex-start !important;
    flex-direction: column !important;
    height: max-content !important;
  }

  .ci-list {
    overflow: visible;
    height: auto;
  }
  .ci-details {
    margin: 15px;
  }
  app-chat-form {
    --form-field-addon-medium-width: 50px !important;
    padding: 5px 0px !important;

    [nbsuffix] {
      margin-right: 0px !important;
    }
  }

  .chat-back {
    padding: 10px !important;
  }

  .investment-card {
    width: 100%;
  }

  .chat-time {
    width: 100%;
    text-align: end;
    margin-top: 11px;
  }

  .document-popup {
    margin: 0 !important;
    width: 300px !important;
  }

  p-selectbutton .p-button {
    padding: 0.4rem 0.4rem !important;
  }

  .chat-user-list {
    padding: 0px 5px !important;
  }

  .p-sidebar .p-sidebar-content {
    padding: 0.6rem !important;
  }
}
