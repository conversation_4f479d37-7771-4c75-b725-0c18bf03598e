import { CommonModule, CurrencyPipe } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { DocumentType } from '@core/models/config';
import { DashboardService } from '@core/services/dashboard.service';
import { DocumentService } from '@core/services/document.service';
import { InvestmentService } from '@core/services/investment.service';
import { InvestorsService } from '@core/services/investors.service';
import { SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbIconModule,
  NbOptionModule,
  NbSelectModule,
  NbSpinnerModule,
  NbToastrService,
} from '@nebular/theme';
import { Color, NgxChartsModule, ScaleType } from '@swimlane/ngx-charts';
import { TableModule } from 'primeng/table';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-investment-dashboard',
  templateUrl: './investment-dashboard.component.html',
  styleUrls: ['./investment-dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbOptionModule,
    NbSpinnerModule,
    TableModule,
    CurrencyPipe,
    NgxChartsModule,
    NbButtonModule,
  ],
})
export class InvestmentDashboardComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject();

  recentActivities!: any[];
  recentMessages!: any[];

  single = [];
  view: any[] = [700, 400];

  // options
  gradient = false;
  showLegend = true;
  showLabels = false;
  isDoughnut = true;
  legendPosition = 'below';

  colorScheme: Color = {
    name: 'customScheme',
    selectable: true,
    group: ScaleType.Ordinal,
    domain: ['#002C24'],
  };

  fundsDeployedData!: any[];
  investorId!: number;
  currentInvestmentOverviewData: any;
  investmentId: number | undefined;
  monthlyDistributionData = [];
  transactionHistoryData: any;
  loading!: boolean;
  totalRecords = 0;
  quarterlyUpdatesData: any;
  fundDocumentsData: any;
  investmentLookup: any;
  yAxisTicks: any[] = [];
  isMonthlyDistLoading = false;

  constructor(
    private route: Router,
    private currencyPipe: CurrencyPipe,
    private toast: NbToastrService,
    private documentService: DocumentService,
    private investorsService: InvestorsService,
    private dashboardService: DashboardService,
    private investmentService: InvestmentService,
    private sharedService: SharedService,
  ) {}
  ngAfterViewInit(): void {
    // this.scrollToInvestor();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  public yAxisTickFormatting = (value: any) => `${this.currencyPipe.transform(value, 'USD', 'symbol', '0.0')}`;

  async ngOnInit(): Promise<void> {
    this.investmentId = this.sharedService.getFormParamValue.investmentId;

    this.investorsService.account.pipe(takeUntil(this.destroy$)).subscribe((value: any) => {
      this.investorId = this.investorsService.accountValue?.investorId || 0;
      this.loadDashboard();
    });

    this.investorId = this.investorsService.accountValue?.investorId || 0;
  }

  loadDashboard(): void {
    this.getCurrentInvestments();
    this.getMonthlyDistribution();
    this.getTransactionHistory();
    this.getFundDocuments();
    this.getQuarterlyUpdates();

    if (this.investorId) {
      this.investmentService
        .getInvestmentLookup({
          investorId: this.investorId,
          excludeClosed: true,
        })
        .subscribe((response: any) => {
          if (response.success) {
            this.investmentLookup = response.payload;
          }
        });
    }
  }

  private getCurrentInvestments(): void {
    this.dashboardService
      .getCurrentInvestmentOverview({
        investorId: this.investorId,
        investmentId: this.investmentId,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.currentInvestmentOverviewData = data.payload;
        }
      });
  }

  private getMonthlyDistribution(): void {
    this.isMonthlyDistLoading = true;
    this.yAxisTicks = [] as any;
    this.dashboardService
      .getMonthlyDistribution({
        investorId: this.investorId,
        investmentId: this.investmentId,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.isMonthlyDistLoading = false;
          this.monthlyDistributionData = data.payload?.monthlyDistributions;
          if (this.monthlyDistributionData && this.monthlyDistributionData.length > 0) {
            const band = data.payload?.band || 1000;

            const zeroCount = this.monthlyDistributionData.filter((month: any) => month.value === 0);
            if (zeroCount.length === this.monthlyDistributionData.length) {
              this.monthlyDistributionData = [];
            }

            let maxValue = Math.max(...this.monthlyDistributionData.map((o: any) => o.value));

            maxValue = maxValue + 1.2;

            for (let i = 0; i <= maxValue + band; i += band) {
              this.yAxisTicks.push(i);
            }
          }
        }
        this.isMonthlyDistLoading = false;
      });
  }

  private getTransactionHistory(): void {
    this.loading = true;
    this.dashboardService
      .getTransactionHistory({
        investorId: this.investorId,
        investmentId: this.investmentId,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.transactionHistoryData = data.payload;
          if (this.transactionHistoryData) {
            this.totalRecords = data.payload.length;
          }
        }
        this.loading = false;
      });
  }

  private getFundDocuments(): void {
    this.dashboardService
      .getDocumentsByType({
        investorId: this.investorId,
        investmentId: this.investmentId,
        documentType: DocumentType.FundDocuments,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.fundDocumentsData = data.payload;
        }
      });
  }

  private getQuarterlyUpdates(): void {
    this.dashboardService
      .getDocumentsByType({
        investorId: this.investorId,
        investmentId: this.investmentId,
        documentType: DocumentType.QuarterlyUpdates,
      })
      .subscribe((data: any) => {
        if (data.success) {
          this.quarterlyUpdatesData = data.payload;
        }
      });
  }

  async downloadFile(file: any): Promise<void> {
    this.toast.default(`Downloading started`, 'Success!', {
      icon: 'download',
    });
    await this.documentService.getDocument({
      documentKey: file.documentKey,
    });
  }
}
