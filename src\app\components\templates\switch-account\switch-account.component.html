<button
  nbButton
  ghost
  *ngIf="showHelp() && users && users.length > 1"
  [nbSpinner]="loading"
  status="default"
  nbPopoverTrigger="click"
  nbPopoverPlacement="bottom"
  [nbPopover]="templateRef"
>
  <nb-icon icon="switchIcon" pack="custom"></nb-icon> <span class="hide-on-mobile"> Switch Account </span>
</button>

<ng-template #templateRef>
  <div size="small" class="list">
    <nb-list>
      <nb-list-item *ngFor="let user of users">
        <div class="user-list-item" [ngClass]="user.selected ? 'selected' : ''" (click)="switchAccount(user)">
          <nb-user color="#002c24" [name]="user.name" status="success" [title]="user.entityType"> </nb-user>
          <div class="forward-icon">
            <nb-icon icon="arrow-ios-forward-outline" status="primary"></nb-icon>
          </div>
        </div>
      </nb-list-item>
    </nb-list>
  </div>
</ng-template>
