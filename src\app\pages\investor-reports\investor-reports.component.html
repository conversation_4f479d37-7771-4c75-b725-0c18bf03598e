<!-- Header Section -->
<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>Reports</h5>
      <p class="subtitle">Report Dashboard</p>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto">
    <!-- Theme and Export buttons -->
    <div class="header-actions">
      <button nbButton ghost status="info" class="theme-btn">
        <nb-icon icon="color-palette-outline"></nb-icon>
        Theme
      </button>
      <button nbButton status="primary" (click)="exportReport()" class="export-btn">
        <nb-icon icon="download-outline"></nb-icon>
        Export
      </button>
    </div>
  </div>
</div>

<!-- Report Type Selection -->
<div class="report-type-section">
  <h6>Report Type</h6>
  <div class="report-type-buttons">
    <button
      *ngFor="let reportType of reportTypes"
      [class]="'report-type-btn ' + (selectedReportType === reportType.id ? 'active' : '')"
      (click)="onReportTypeChange(reportType.id)"
      nbButton
      ghost
    >
      <nb-icon [icon]="reportType.icon"></nb-icon>
      {{ reportType.name }}
    </button>
  </div>
</div>

<!-- Filters Section -->
<div class="filters-section">
  <h6>Filters</h6>
  <div class="flex flex-wrap -mx-2" *ngIf="!isInvestor">
    <div class="lg:w-3/12 px-2 sm:w-full px-2 my-[15px] items-rows">
      <div class="filter-group">
        <label>From Date</label>
        <input type="date" nbInput fullWidth placeholder="dd/mm/yyyy" (change)="applyFilters()" />
      </div>
    </div>

    <div class="lg:w-3/12 px-2 sm:w-full px-2 my-[15px] items-rows">
      <div class="filter-group">
        <label>To Date</label>
        <input type="date" nbInput fullWidth placeholder="dd/mm/yyyy" (change)="applyFilters()" />
      </div>
    </div>

    <div class="lg:w-3/12 px-2 sm:w-full px-2 my-[15px] items-rows">
      <div class="filter-group">
        <label>Reference Number</label>
        <input type="text" nbInput fullWidth placeholder="Enter reference..." (input)="filterGlobal($event)" />
      </div>
    </div>

    <div class="lg:w-3/12 px-2 my-[15px] text-right">
      <button nbButton status="primary" (click)="applyFilters()" class="apply-filters-btn">
        <nb-icon icon="funnel-outline"></nb-icon>
        Apply Filters
      </button>
    </div>
  </div>
</div>

<!-- Report Data Section -->
<div class="report-data-section">
  <div class="report-header">
    <h6>Report Data</h6>
    <span class="record-count">{{ totalRecords }} records</span>
  </div>

  <div *ngIf="reports">
    <nb-card>
      <nb-card-body>
        <p-table
          #dt
          [filterDelay]="700"
          [value]="reports"
          [lazy]="true"
          [loading]="loading"
          (onLazyLoad)="nextPage($event)"
          [paginator]="false"
          [rows]="200"
          [totalRecords]="totalRecords"
          [showCurrentPageReport]="true"
          currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
          [rowsPerPageOptions]="[10, 25, 50]"
          [scrollable]="true"
          scrollWidth="flex"
          scrollHeight="flex"
          [globalFilterFields]="[
            'id',
            'description',
            'transactionType',
            'investment',
            'investor',
            'dateCreated',
            'amount',
          ]"
          sortField="id"
          [sortOrder]="-1"
        >
          <!-- Account Activity Table Headers (matching your image) -->
          <ng-template pTemplate="header" *ngIf="selectedReportType === 'account-activity'">
            <tr class="account-activity-header">
              <th style="min-width: 100px">Transaction Date</th>
              <th style="min-width: 120px">Checkout or Loan Amount</th>
              <th style="min-width: 150px">Transaction Amount</th>
              <th style="min-width: 100px">Serv Fees</th>
              <th style="min-width: 80px">GST</th>
              <th style="min-width: 100px">Interest</th>
              <th style="min-width: 150px">Distribution Principal</th>
              <th style="min-width: 100px">Charges</th>
              <th style="min-width: 80px">Other</th>
              <th style="min-width: 100px">Escrow</th>
            </tr>
          </ng-template>

          <!-- Investment Portfolio Table Headers -->
          <ng-template pTemplate="header" *ngIf="selectedReportType === 'investment-portfolio'">
            <tr>
              <th style="min-width: 100px" [pSortableColumn]="'id'">
                <div>
                  <div>ID</div>
                  <p-sortIcon [field]="'id'"></p-sortIcon>
                </div>
              </th>
              <th style="width: 250px" [pSortableColumn]="'investment'">
                <div>
                  <div>Investment</div>
                  <p-sortIcon [field]="'investment'"></p-sortIcon>
                </div>
              </th>
              <th style="min-width: 150px">Portfolio Value</th>
              <th style="min-width: 100px">Units</th>
              <th style="min-width: 100px">Unit Price</th>
              <th style="min-width: 150px">Date</th>
            </tr>
          </ng-template>

          <!-- Funding Activity Table Headers -->
          <ng-template pTemplate="header" *ngIf="selectedReportType === 'funding-activity'">
            <tr>
              <th style="min-width: 100px" [pSortableColumn]="'id'">
                <div>
                  <div>ID</div>
                  <p-sortIcon [field]="'id'"></p-sortIcon>
                </div>
              </th>
              <th style="width: 250px">Funding Type</th>
              <th style="min-width: 150px">Funding Amount</th>
              <th style="min-width: 100px">Status</th>
              <th style="min-width: 150px">Date</th>
            </tr>
          </ng-template>

          <!-- Trust Activity Table Headers -->
          <ng-template pTemplate="header" *ngIf="selectedReportType === 'trust-activity'">
            <tr>
              <th style="min-width: 100px" [pSortableColumn]="'id'">
                <div>
                  <div>ID</div>
                  <p-sortIcon [field]="'id'"></p-sortIcon>
                </div>
              </th>
              <th style="width: 250px">Trust Activity</th>
              <th style="min-width: 150px">Trust Amount</th>
              <th style="min-width: 150px">Date</th>
            </tr>
          </ng-template>

          <!-- Account Activity Table Body (matching your image structure) -->
          <ng-template pTemplate="body" let-report *ngIf="selectedReportType === 'account-activity'">
            <tr>
              <td>{{ report.transactionDate | date: "dd/MM/yyyy" }}</td>
              <td>{{ report.checkoutOrLoan }}</td>
              <td>{{ report.transactionAmount | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.servFees | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.gst | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.interest | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.distributionPrincipal | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.charges | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.other | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.escrow | currency: "USD" : "symbol" : "1.2-2" }}</td>
            </tr>
          </ng-template>

          <!-- Investment Portfolio Table Body -->
          <ng-template pTemplate="body" let-report *ngIf="selectedReportType === 'investment-portfolio'">
            <tr>
              <td>{{ report.id }}</td>
              <td>{{ report.investment }}</td>
              <td>{{ report.portfolioValue | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.units }}</td>
              <td>{{ report.unitPrice | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.paymentDate | date: "dd/MM/yyyy" }}</td>
            </tr>
          </ng-template>

          <!-- Funding Activity Table Body -->
          <ng-template pTemplate="body" let-report *ngIf="selectedReportType === 'funding-activity'">
            <tr>
              <td>{{ report.id }}</td>
              <td>{{ report.fundingType }}</td>
              <td>{{ report.fundingAmount | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>
                <span class="status-badge" [class.completed]="report.status === 'Completed'">
                  {{ report.status }}
                </span>
              </td>
              <td>{{ report.paymentDate | date: "dd/MM/yyyy" }}</td>
            </tr>
          </ng-template>

          <!-- Trust Activity Table Body -->
          <ng-template pTemplate="body" let-report *ngIf="selectedReportType === 'trust-activity'">
            <tr>
              <td>{{ report.id }}</td>
              <td>{{ report.trustActivity }}</td>
              <td>{{ report.trustAmount | currency: "USD" : "symbol" : "1.2-2" }}</td>
              <td>{{ report.trustDate | date: "dd/MM/yyyy" }}</td>
            </tr>
          </ng-template>

          <!-- Empty message template -->
          <ng-template pTemplate="emptymessage" let-columns>
            <tr>
              <td style="text-align: center; display: block" [attr.colspan]="10">
                No {{ selectedReportType.replace("-", " ") }} data available.
              </td>
            </tr>
          </ng-template>
        </p-table>
      </nb-card-body>
    </nb-card>
  </div>
</div>
