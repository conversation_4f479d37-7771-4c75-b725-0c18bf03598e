<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5 class="underline underline-offset-[5px] !font-normal decoration-lime">Reports</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2" *ngIf="!isInvestor">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        fullWidth
        placeholder="Report Type"
        name="reportType"
        id="reportType"
        [selected]="selectedReportType"
        (selectedChange)="onReportTypeChange($event)"
      >
        <nb-option *ngFor="let reportType of reportTypes" [value]="reportType.id">
          {{ reportType.name }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <input nbInput placeholder="Date Range" [nbDatepicker]="dateRageReportPicker" class="w-[250px]" />
      <nb-rangepicker #dateRageReportPicker></nb-rangepicker>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Enter reference..." (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>
  </div>

  <div class="lg:w-3/12 px-2 my-[15px] text-right">
    <button nbButton ghost status="primary" *ngIf="!isManager" (click)="exportReport()">
      <i class="pi pi-download"></i>
    </button>
  </div>
</div>

<div *ngIf="reports">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="reports"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="false"
        [rows]="200"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="[
          'id',
          'description',
          'transactionType',
          'investment',
          'investor',
          'dateCreated',
          'amount',
          'investmentId',
          'transactionTypeId',
        ]"
        sortField="id"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <!-- Investment Portfolio Headers -->
          <tr *ngIf="selectedReportType === 'investment-portfolio'">
            <th style="min-width: 120px" [pSortableColumn]="'loanAccount'">
              <div>
                <div>Loan Account</div>
                <p-sortIcon [field]="'loanAccount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 200px" [pSortableColumn]="'borrowerName'">
              <div>
                <div>Borrower Name</div>
                <p-sortIcon [field]="'borrowerName'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'pctOwned'">
              <div>
                <div>Pct Owned</div>
                <p-sortIcon [field]="'pctOwned'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'interestRate'">
              <div>
                <div>Interest Rate</div>
                <p-sortIcon [field]="'interestRate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'maturityDate'">
              <div>
                <div>Maturity Date</div>
                <p-sortIcon [field]="'maturityDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'termLeft'">
              <div>
                <div>Term Left</div>
                <p-sortIcon [field]="'termLeft'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'nextPayment'">
              <div>
                <div>Next Payment</div>
                <p-sortIcon [field]="'nextPayment'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'regularPayment'">
              <div>
                <div>Regular Payment</div>
                <p-sortIcon [field]="'regularPayment'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'loanBalance'">
              <div>
                <div>Loan Balance</div>
                <p-sortIcon [field]="'loanBalance'"></p-sortIcon>
              </div>
            </th>
          </tr>

          <!-- Account Activity Headers -->
          <tr *ngIf="selectedReportType === 'account-activity'">
            <th style="min-width: 120px" [pSortableColumn]="'transactionDate'">
              <div>
                <div>Transaction Date</div>
                <p-sortIcon [field]="'transactionDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'checkoutOrLoan'">
              <div>
                <div>Checkout or Loan Amount</div>
                <p-sortIcon [field]="'checkoutOrLoan'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'loanAccount'">
              <div>
                <div>Loan Account</div>
                <p-sortIcon [field]="'loanAccount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'transactionAmount'">
              <div>
                <div>Transaction Amount</div>
                <p-sortIcon [field]="'transactionAmount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'servFees'">
              <div>
                <div>Serv. Fees</div>
                <p-sortIcon [field]="'servFees'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 80px" [pSortableColumn]="'gst'">
              <div>
                <div>GST</div>
                <p-sortIcon [field]="'gst'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'interest'">
              <div>
                <div>Interest</div>
                <p-sortIcon [field]="'interest'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'distributionPrincipal'">
              <div>
                <div>Distribution Principal</div>
                <p-sortIcon [field]="'distributionPrincipal'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'charges'">
              <div>
                <div>Charges</div>
                <p-sortIcon [field]="'charges'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 80px" [pSortableColumn]="'other'">
              <div>
                <div>Other</div>
                <p-sortIcon [field]="'other'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'escrow'">
              <div>
                <div>Escrow</div>
                <p-sortIcon [field]="'escrow'"></p-sortIcon>
              </div>
            </th>
          </tr>

          <!-- Default Headers for other report types -->
          <tr *ngIf="selectedReportType !== 'investment-portfolio' && selectedReportType !== 'account-activity'">
            <th style="min-width: 100px; max-width: 100px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>

            <th style="width: 250px" [pSortableColumn]="'description'">
              <div>
                <div>Description</div>
                <p-sortIcon [field]="'description'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'paymentDate'">
              <div>
                <div>Date</div>
                <p-sortIcon [field]="'paymentDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'amount'">
              <div>
                <div>Amount</div>
                <p-sortIcon [field]="'amount'"></p-sortIcon>
              </div>
            </th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-report>
          <!-- Investment Portfolio Body -->
          <tr *ngIf="selectedReportType === 'investment-portfolio'">
            <td style="min-width: 120px">{{ report.loanAccount }}</td>
            <td style="min-width: 200px">{{ report.borrowerName }}</td>
            <td style="min-width: 100px">{{ report.pctOwned | number: "1.3-3" }}%</td>
            <td style="min-width: 100px">{{ report.interestRate | number: "1.3-3" }}%</td>
            <td style="min-width: 120px">{{ report.maturityDate | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 100px">{{ report.termLeft }}</td>
            <td style="min-width: 120px">{{ report.nextPayment | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 120px">{{ report.regularPayment | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 120px">{{ report.loanBalance | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>

          <!-- Account Activity Body -->
          <tr *ngIf="selectedReportType === 'account-activity'">
            <td style="min-width: 120px">{{ report.transactionDate | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 120px">{{ report.checkoutOrLoan }}</td>
            <td style="min-width: 120px">{{ report.loanAccount }}</td>
            <td style="min-width: 150px">{{ report.transactionAmount | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.servFees | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 80px">{{ report.gst | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.interest | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 150px">{{ report.distributionPrincipal | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.charges | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 80px">{{ report.other | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.escrow | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>

          <!-- Default Body for other report types -->
          <tr *ngIf="selectedReportType !== 'investment-portfolio' && selectedReportType !== 'account-activity'">
            <td style="min-width: 100px; max-width: 100px">{{ report.id }}</td>
            <td style="width: 250px">{{ report.description }}</td>
            <td style="min-width: 150px">{{ report.paymentDate | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 100px">{{ report.amount | currency: "USD" : "symbol" : "1.0" }}</td>
          </tr>
        </ng-template>

        <!-- Current Portfolio Yield Total Row - Outside of body template -->
        <ng-template #footer *ngIf="selectedReportType === 'investment-portfolio' && reports && reports.length > 0">
          <tr class="font-bold">
            <td style="min-width: 120px"></td>
            <td style="min-width: 200px"></td>
            <td style="min-width: 100px"></td>
            <td style="min-width: 100px"></td>
            <td style="min-width: 120px"></td>
            <td style="min-width: 100px; text-align: left">Current Portfolio Yield:</td>
            <td style="min-width: 120px">{{ getTotalInterestRate() | number: "1.3-3" }}%</td>
            <td style="min-width: 120px">
              {{ getTotalRegularPayment() | currency: "USD" : "symbol" : "1.2-2" }}
            </td>
            <td style="min-width: 120px">
              {{ getTotalLoanBalance() | currency: "USD" : "symbol" : "1.2-2" }}
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td
              style="text-align: center; display: block"
              [attr.colspan]="
                selectedReportType === 'investment-portfolio' ? 9 : selectedReportType === 'account-activity' ? 11 : 4
              "
            >
              No reports yet.
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
