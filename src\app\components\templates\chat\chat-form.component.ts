/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { GlobalMessageType } from '@core/helpers';
import { FacilityFilter } from '@core/models/response/facilities-filter.response';
import {
  NbButtonModule,
  NbCheckboxModule,
  NbComponentOrCustomStatus,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
  NbTooltipModule,
} from '@nebular/theme';

/**
 * Chat form component.
 *
 * Show a message form with a send message button.
 *
 * ```ts
 * <nb-chat-form showButton="true" buttonIcon="nb-send">
 * </nb-chat-form>
 * ```
 *
 * When `[dropFiles]="true"` handles files drag&drop with a file preview.
 *
 * Drag & drop available for files and images:
 * @stacked-example(Drag & Drop Chat, chat/chat-drop.component)
 *
 * New message could be tracked outside by using `(send)` output.
 *
 * ```ts
 * <nb-chat-form (send)="onNewMessage($event)">
 * </nb-chat-form>
 *
 * // ...
 *
 * onNewMessage({ message: string, files: any[] }) {
 *   this.service.sendToServer(message, files);
 * }
 * ```
 */
@Component({
  selector: 'app-chat-form',
  templateUrl: 'chat-form.component.html',
  styleUrls: ['chat-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbIconModule,
    NbCheckboxModule,
    NbSelectModule,
    NbTooltipModule,
    NbFormFieldModule,
    NbInputModule,
    NbButtonModule,
  ],
})
export class NbChatFormComponent {
  @ViewChild('fileDropRef') fileDropRef!: ElementRef;
  status: NbComponentOrCustomStatus = 'basic';
  inputFocus = false;
  inputHover = false;
  droppedFiles: any[] = [];

  imgDropTypes = ['image/png', 'image/jpeg', 'image/gif'];

  @Input() message = '';

  @Input() globalMessageType: GlobalMessageType = GlobalMessageType.Investor;

  @Input() facilities: FacilityFilter[] = [];

  @Input() selectedFacilityId!: number;

  @Input() selectedFacility!: FacilityFilter | undefined;

  @Input() isAdmin = false;

  @Input() showFilter = true;

  @Input() isInternalNote = false;

  @Input() messagePlaceholder = 'Write a comment...';

  @Input() buttonTitle = '';

  @Input() buttonIcon = 'paper-plane-outline';

  @Input() showButton = true;

  @Input() dropFiles = false;

  @Input() disabled = false;

  @Input() dropFilePlaceholder = 'Drop file to send';

  @Output() send = new EventEmitter<{
    message: string;
    files: File[];
    isInternalNote: boolean;
    selectedFacility?: FacilityFilter;
  }>();

  @HostBinding('class.file-over') fileOver = false;
  formRecordId: any;
  userId: any;
  orgId: any;
  files: any;

  constructor(
    protected cd: ChangeDetectorRef,
    protected domSanitizer: DomSanitizer,
    private toast: NbToastrService,
  ) {}

  get hasGlobalMessageTypeAsset(): boolean {
    return this.globalMessageType === GlobalMessageType.Asset;
  }

  @HostListener('drop', ['$event'])
  onDrop(event: any) {
    if (this.dropFiles) {
      event.preventDefault();
      event.stopPropagation();

      this.fileOver = false;
      if (event.dataTransfer && event.dataTransfer.files) {
        this.prepareFilesList(event.dataTransfer.files);

        // for (const file of event.dataTransfer.files) {
        //   const res = file;
        //   // if (this.imgDropTypes.includes(file.type)) {
        //   //   const fr = new FileReader();
        //   //   fr.onload = (e: any) => {
        //   //     res.src = e.target.result;
        //   //     res.urlStyle = this.domSanitizer.bypassSecurityTrustStyle(`url(${res.src})`);
        //   //     this.cd.detectChanges();
        //   //   };

        //   //   fr.readAsDataURL(file);
        //   // }
        //   this.droppedFiles.push(res);
        // }
      }
    }
  }

  isInternalNoteToggle() {
    this.isInternalNote = !this.isInternalNote;
  }

  removeFile(file: any) {
    const index = this.droppedFiles.indexOf(file);
    if (index >= 0) {
      this.droppedFiles.splice(index, 1);
    }
  }

  onFacilityChange(facilityId: number): void {
    const selected = this.facilities.find((x) => x.id === facilityId);
    if (selected) {
      this.selectedFacility = selected;
    } else {
      this.selectedFacility = undefined;
    }
  }

  @HostListener('dragover')
  onDragOver() {
    if (this.dropFiles) {
      this.fileOver = true;
    }
  }

  @HostListener('dragleave')
  onDragLeave() {
    if (this.dropFiles) {
      this.fileOver = false;
    }
  }

  sendMessage(): void {
    if (this.droppedFiles.length || String(this.message).trim().length) {
      if (this.droppedFiles.length > 0 && !this.message) {
        this.message = 'Attached';
      }

      this.send.emit({
        message: this.message,
        files: this.droppedFiles,
        isInternalNote: this.isInternalNote,
        selectedFacility: this.selectedFacility,
      });
      this.message = '';
      this.droppedFiles = [];
      this.fileDropRef.nativeElement.value = '';
    }
  }

  setStatus(status: NbComponentOrCustomStatus): void {
    if (this.status !== status) {
      this.status = status;
      this.cd.detectChanges();
    }
  }

  getInputStatus(): NbComponentOrCustomStatus {
    if (this.fileOver) {
      return this.getHighlightStatus();
    }

    if (this.inputFocus || this.inputHover) {
      return this.status;
    }

    return 'basic';
  }

  getButtonStatus(): NbComponentOrCustomStatus {
    return this.getHighlightStatus();
  }

  protected getHighlightStatus(): NbComponentOrCustomStatus {
    if (this.status === 'basic' || this.status === 'control') {
      return 'primary';
    }

    return this.status;
  }

  onFileDropped(event: any): void {
    this.prepareFilesList(event);
  }

  fileBrowseHandler(event: any): void {
    this.prepareFilesList(event.target.files);
    event.target.value = ''; // Clear the input value to allow re-uploading the same file
  }

  prepareFilesList(files: any[]): void {
    const allowedExtensions = /(\.jpg|\.jpeg|\.png|\.pdf|\.xls|\.xlsx|\.doc|\.docx|\.odt)$/i;
    for (const file of files) {
      if (file) {
        if (!allowedExtensions.exec(file.name)) {
          this.toast.danger('File type not supported', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          return;
        }
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 1024 * 10) {
          this.toast.danger('Please ensure the file size does not exceed 10MB.', 'Upload Error!');
          this.fileDropRef.nativeElement.value = '';
          return;
        }
        this.droppedFiles.push(file);
      }
    }
  }
}
