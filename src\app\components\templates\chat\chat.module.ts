/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */

import { ModuleWithProviders, NgModule } from '@angular/core';

import { NbChatComponent } from './chat.component';
import { NbChatMessageComponent } from './chat-message.component';
import { NbChatFormComponent } from './chat-form.component';
import { NbChatMessageTextComponent } from './chat-message-text.component';
import { NbChatMessageFileComponent } from './chat-message-file.component';
import { NbChatMessageQuoteComponent } from './chat-message-quote.component';
import { NbChatMessageMapComponent } from './chat-message-map.component';
import { NbChatOptions } from './chat.options';
import { NbButtonModule, NbIconModule, NbInputModule } from '@nebular/theme';
// import { NbSharedModule } from '@nebular/theme/components/shared/shared.module';

const NB_CHAT_COMPONENTS = [
  // NbChatComponent,
  // NbChatMessageComponent,
  // NbChatFormComponent,
  // NbChatMessageTextComponent,
  // NbChatMessageFileComponent,
  // NbChatMessageQuoteComponent,
  // NbChatMessageMapComponent,
];

@NgModule({
  imports: [
    // NbSharedModule,
    // NbIconModule,
    // NbInputModule,
    // NbButtonModule,
  ],
  declarations: [
    // ...NB_CHAT_COMPONENTS,
  ],
  exports: [
    // ...NB_CHAT_COMPONENTS,
  ],
})
export class NbChatModule {
  static forRoot(options?: NbChatOptions): ModuleWithProviders<NbChatModule> {
    return {
      ngModule: NbChatModule,
      providers: [{ provide: NbChatOptions, useValue: options || {} }],
    };
  }

  static forChild(options?: NbChatOptions): ModuleWithProviders<NbChatModule> {
    return {
      ngModule: NbChatModule,
      providers: [{ provide: NbChatOptions, useValue: options || {} }],
    };
  }
}
