<p-drawer
  [(visible)]="visible"
  position="bottom"
  [showCloseIcon]="false"
  (onHide)="onHide()"
  styleClass="!h-[calc(100vh-200px)] !w-[96%] !left-[2%] rounded-t-[13px]"
>
  <div class="flex flex-wrap -mx-2">
    <div class="hide-chat" style="transition: all 1s" [ngClass]="{ 'hide-chat': openChat, 'w-full px-2': !openChat }">
      <div class="chat-user-list">
        <div style="display: flex; align-items: center; justify-content: space-between">
          <h6 style="margin: 10px 0px">
            <button nbButton status="primary" ghost shape="round" class="chat-button" (click)="onHide()">
              <nb-icon icon="arrow-ios-back-outline"></nb-icon>
            </button>
            Messages
          </h6>
          <div class="display-flex">
            <!-- <p-selectButton *ngIf="isAdmin()" [options]="stateOptions" [(ngModel)]="globalMessageType"
                            optionLabel="label" (onChange)="onMessageTypeChange($event)" optionValue="value">
                        </p-selectButton> -->
            <div *ngIf="isInvestorStaffUsers() && hasGlobalMessageTypeInvestor()">
              <button
                *ngIf="this.filterParams.sortOrder === 'asc'"
                nbButton
                ghost
                status="primary"
                (click)="sortInvestorList('desc')"
              >
                <i class="pi pi-sort-amount-up-alt"></i>
              </button>
              <button
                *ngIf="this.filterParams.sortOrder === 'desc'"
                nbButton
                ghost
                status="primary"
                (click)="sortInvestorList('asc')"
              >
                <i class="pi pi-sort-amount-down"></i>
              </button>
            </div>

            <div *ngIf="hasGlobalMessageTypeAsset()">
              <button
                *ngIf="this.assetFilterParams.sortOrder === 'asc'"
                nbButton
                ghost
                status="primary"
                (click)="sortAssetUsersList('desc')"
              >
                <i class="pi pi-sort-amount-up-alt"></i>
              </button>
              <button
                *ngIf="this.assetFilterParams.sortOrder === 'desc'"
                nbButton
                ghost
                status="primary"
                (click)="sortAssetUsersList('asc')"
              >
                <i class="pi pi-sort-amount-down"></i>
              </button>
            </div>
          </div>
        </div>
        <hr class="w-full rounded-[17px] mt-2 mb-2 border-gray-200" />
        <div *ngIf="isInvestorStaffUsers()">
          <nb-form-field>
            <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
            <input type="text" fullWidth placeholder="Search" (input)="filterInvestors($event)" nbInput />
          </nb-form-field>
        </div>
      </div>

      <div class="list scrollable" #scrollable>
        <nb-list style="width: 100%" *ngIf="hasGlobalMessageTypeInvestor()">
          <nb-list-item *ngFor="let investor of investors" class="{{ 'selected' + investor.investorId }}">
            <div
              class="user-list-item"
              [ngClass]="investor.investorId === investorId ? 'selected' : ''"
              (click)="getInvestorMessages(investor)"
            >
              <div>
                <i pBadge *ngIf="!investor.isRead" severity="danger"></i>

                <nb-user
                  color="#002c24"
                  name="{{ investor.contactName }}"
                  status="success"
                  [title]="investor.entityName"
                >
                </nb-user>
              </div>
              <div class="chat-time">
                {{ timeAgo(investor.dateCreated) }}
              </div>
            </div>
          </nb-list-item>
        </nb-list>

        <nb-list style="width: 100%" *ngIf="hasGlobalMessageTypeAsset()">
          <nb-list-item *ngFor="let user of assetTypeUsers" class="{{ 'selected' + user.userId }}">
            <div
              class="user-list-item"
              [ngClass]="user.userId === userId ? 'selected' : ''"
              (click)="getAssetUserMessages(user)"
            >
              <div>
                <i pBadge *ngIf="!user.isRead" severity="danger"></i>

                <nb-user color="#002c24" name="{{ user.contactName }}" status="success" [title]="user.contactOrgName">
                </nb-user>
              </div>
              <div class="chat-time">
                {{ timeAgo(user.dateCreated) }}
              </div>
            </div>
          </nb-list-item>
        </nb-list>
      </div>
    </div>
    <div
      [ngClass]="{ 'sm:max-w-2/3 px-[15px]': openChat, 'w-full px-2': !openChat }"
      style="transition: all 1s"
      right
      *ngIf="openChat"
      [style.opacity]="!openChat ? '0' : '1'"
      [style.width]="!openChat ? '0' : '100%'"
      [style.visibility]="!openChat ? 'hidden' : 'visible'"
    >
      <div class="show-on-mobile">
        <div style="display: flex; margin-bottom: 10px">
          <button
            nbButton
            status="primary"
            ghost
            shape="round"
            *ngIf="openChat"
            class="chat-button"
            (click)="openChat = !openChat"
          >
            <nb-icon icon="arrow-ios-back-outline"></nb-icon>
          </button>
          <nb-user
            *ngIf="selectedInvestor"
            color="#002c24"
            name="{{ selectedInvestor.contactName }}"
            status="success"
            [title]="selectedInvestor.entityName"
          >
          </nb-user>
        </div>
      </div>
      <div class="chat-back">
        <app-investor-chat
          *ngIf="investorId && hasGlobalMessageTypeInvestor()"
          [showFilter]="false"
          [investorId]="investorId"
          [isInternalNote]="false"
        >
        </app-investor-chat>

        <app-asset-chat
          *ngIf="userId && hasGlobalMessageTypeAsset()"
          [globalMessageType]="globalMessageType"
          [showFilter]="false"
          [userId]="userId"
          [isInternalNote]="false"
          [contactOrgId]="selectedAssetUser.contactOrgId"
        >
        </app-asset-chat>
      </div>
    </div>
  </div>
</p-drawer>
