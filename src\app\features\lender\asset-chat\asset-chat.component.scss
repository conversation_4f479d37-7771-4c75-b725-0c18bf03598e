@use "themes" as *;

:host {
  --user-picture-box-background-color: #0f3b33;
  // --chat-padding: 45px 10px;
}

nb-user {
  margin-left: 7px;
}

.filter-bar {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  height: 50px;
  align-items: flex-start;
}

.add-doc-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 9px;
}

.file-container {
  width: fit-content;
  border: 2px dashed #c0c4c7;
  border-radius: 30px;
  height: 38px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px;
  margin: 4px;

  :hover {
    cursor: pointer;
  }

  p {
    font-size: 16px;
    font-weight: 400;
    color: #c0c4c7;
  }

  input {
    opacity: 0;
    position: absolute;
    border: 1px solid red;
    border-radius: 21px;
    width: 0px;
  }

  input:hover {
    cursor: pointer;
  }

  label {
    color: white;
    width: 183px;
    height: 44px;
    border-radius: 21.5px;
    background-color: #db202f;
    padding: 8px 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #38424c;
  }
}

hr.solid {
  margin: 2px 58px 30px -15px;
  width: 109%;
}

.files-list {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  .single-file {
    .name {
      font-size: 14px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
    }

    .info {
      display: flex;
      padding: 8px;
      border: 2px solid #c0c4c7;
      border-radius: 30px;
      justify-content: space-around;
      align-items: center;
      margin: 7px;
    }
  }
}
