import { AfterViewChecked, ChangeDetector<PERSON><PERSON>, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedService } from '@core/services/shared.service';
import { InvestorDocumentsComponent } from '@features/investors-management/investor-document/investor-documents/investor-documents.component';

@Component({
  selector: 'app-documents',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss'],
  standalone: true,
  imports: [CommonModule, InvestorDocumentsComponent],
})
export class DocumentsComponent implements OnInit, OnDestroy, AfterViewChecked {
  constructor(
    private sharedService: SharedService,
    private cdr: ChangeDetectorRef,
  ) {}

  async ngOnInit(): Promise<void> {}

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  ngOnD<PERSON>roy(): void {}

  isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }
}
