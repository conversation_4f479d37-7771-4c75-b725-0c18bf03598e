<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5>User Management</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="lg:w-9/12 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        fullWidth
        placeholder="Status"
        name="statuses"
        id="value"
        (selectedChange)="dt.filter($event, 'statusId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let source of statusData" [value]="source.id">
          {{ source.statusName }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <nb-select
        fullWidth
        placeholder="Role"
        name="role"
        id="value"
        (selectedChange)="dt.filter($event, 'roleId', 'equals')"
      >
        <nb-option [value]="null">All</nb-option>
        <nb-option *ngFor="let role of roleData" [value]="role.id">
          {{ role.roleName }}
        </nb-option>
      </nb-select>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input type="text" fullWidth placeholder="Search" (input)="filterGlobal($event)" nbInput />
      </nb-form-field>
    </div>
  </div>
  <div class="lg:w-3/12 px-2 my-[15px]">
    <button class="float-right" nbButton status="primary" (click)="addNewUser()">
      <nb-icon icon="plus-outline"></nb-icon> Add new user
    </button>
  </div>
</div>

<p-skeleton *ngIf="!users"></p-skeleton>

<div *ngIf="users">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="users"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="true"
        [rows]="10"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollHeight="flex"
        scrollWidth="flex"
        [globalFilterFields]="['userId', 'firstName', 'lastName', 'entity', 'orgName', 'roleName']"
        sortField="lastLogin"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 120px" [pSortableColumn]="'firstName'">
              <div>
                <div>First Name</div>
                <p-sortIcon [field]="'firstName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 120px" [pSortableColumn]="'lastName'">
              <div>
                <div>Last Name</div>
                <p-sortIcon [field]="'lastName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 250px" [pSortableColumn]="'email'">
              <div>
                <div>Email</div>
                <p-sortIcon [field]="'email'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'mobile'">
              <div>
                <div>Mobile</div>
                <p-sortIcon [field]="'mobile'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'dateCreated'">
              <div>
                <div>Onboard Date</div>
                <p-sortIcon [field]="'dateCreated'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 125px" [pSortableColumn]="'roleName'">
              <div>
                <div>User Role</div>
                <p-sortIcon [field]="'roleName'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 125px" [pSortableColumn]="'lastLogin'">
              <div>
                <div>Last Login</div>
                <p-sortIcon [field]="'lastLogin'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'statusName'">
              <div>
                <div>Status</div>
                <p-sortIcon [field]="'statusName'"></p-sortIcon>
              </div>
            </th>
            <th style="min-width: 200px" [hidden]="hideActions()">
              <div>
                <div>Action</div>
              </div>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-user>
          <tr>
            <td style="min-width: 120px">{{ user.firstName }}</td>
            <td style="min-width: 120px">{{ user.lastName }}</td>
            <td style="min-width: 250px">{{ user.email }}</td>
            <td style="min-width: 125px">{{ user.mobile }}</td>
            <td style="min-width: 125px">{{ user.dateCreated | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 125px">{{ user.roleName }}</td>
            <td style="min-width: 125px">{{ timeAgo(user.lastLogin) }}</td>
            <td style="min-width: 100px">
              <span class="user-status" [ngClass]="user.statusName"> {{ user.statusName }} </span>
            </td>
            <td style="min-width: 200px" [hidden]="hideActions()">
              <button
                nbButton
                ghost
                shape="round"
                status="default"
                class="button-icon"
                (click)="editUser(user)"
                nbTooltip="Edit"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="editIcon" pack="custom"></nb-icon>
              </button>

              <button
                *ngIf="user.showLock"
                nbButton
                status="default"
                ghost
                class="button-icon"
                shape="round"
                (click)="confirmDisable(user)"
                nbTooltip="Deactivate"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="lockIcon" pack="custom"></nb-icon>
              </button>

              <button
                *ngIf="!user.showLock"
                nbButton
                status="default"
                ghost
                class="button-icon"
                shape="round"
                (click)="activeUser(user)"
                nbTooltip="Reactivate"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="unlockIcon" pack="custom"></nb-icon>
              </button>

              <button
                *ngIf="user.showDelete"
                class="button-icon"
                status="default"
                nbButton
                shape="round"
                ghost
                (click)="deleteUserConfirm(user)"
                nbTooltip="Archive"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="archiveIcon" pack="custom"></nb-icon>
              </button>

              <button
                *ngIf="user.statusName === 'Pending'"
                status="default"
                class="button-icon"
                nbButton
                shape="round"
                ghost
                (click)="copyLink(user)"
                nbTooltip="Copy Link"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
                <nb-icon icon="copyIcon" pack="custom"></nb-icon>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td style="text-align: center; display: block" *ngIf="!eventFilters" [attr.colspan]="10">No users found</td>
            <td style="text-align: center; display: block" *ngIf="eventFilters" [attr.colspan]="10">
              Sorry, your search did not return any matching results. Please try again
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
