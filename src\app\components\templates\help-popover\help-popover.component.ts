import { Component, OnInit } from '@angular/core';
import { SharedService } from '@core/services/shared.service';
import { CommonModule } from '@angular/common';
import { NbButtonModule, NbPopoverModule } from '@nebular/theme';

@Component({
  selector: 'app-help-popover',
  templateUrl: './help-popover.component.html',
  styleUrls: ['./help-popover.component.scss'],
  standalone: true,
  imports: [CommonModule, NbPopoverModule, NbButtonModule],
})
export class HelpPopoverComponent implements OnInit {
  constructor(private sharedService: SharedService) {}

  ngOnInit(): void {}

  showHelp(): boolean {
    return this.sharedService.isInvestor();
  }
}
