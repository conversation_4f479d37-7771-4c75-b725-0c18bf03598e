<form class="w-full px-2" [formGroup]="financialsForm" (ngSubmit)="onSubmit()">
  <div class="w-full px-2 my-[15px]">
    <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Bank Account Name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="bankAccountName"
        status="{{ submitted && f.bankAccountName.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.bankAccountName.errors" class="invalid-feedback">
        <div *ngIf="f.bankAccountName.errors.required">Bank Account Name is required.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Bank </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="bank"
        status="{{ submitted && f.bank.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.bank.errors" class="invalid-feedback">
        <div *ngIf="f.bank.errors.required">Bank is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> BSB </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        mask="0{6}"
        placeholder=""
        maxlength="6"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="bsb"
        status="{{ submitted && f.bsb.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.bsb.errors" class="invalid-feedback">
        <div *ngIf="f.bsb.errors.required">BSB is required.</div>
        <div *ngIf="f.bsb.errors.mask">Invalid BSB format.</div>
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label><strong> Account Number </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
      <input
        nbInput
        fullWidth
        placeholder=""
        mask="0*"
        maxlength="20"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="accountNo"
        status="{{ submitted && f.accountNo.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.accountNo.errors" class="invalid-feedback">
        <div *ngIf="f.accountNo.errors.required">Account Number is required.</div>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap -mx-2">
    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label
        ><strong> Tax File Number </strong>
        <strong *ngIf="validator('taxFileNo')" class="text-lime required"> &nbsp; * </strong></label
      >
      <input
        nbInput
        fullWidth
        shape="semi-round"
        mask="0*"
        maxlength="30"
        type="text"
        fieldSize="large"
        formControlName="taxFileNo"
        status="{{ submitted && f.taxFileNo.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.taxFileNo.errors" class="invalid-feedback">
        <div *ngIf="f.taxFileNo.errors.required">Tax File Number is required.</div>
        <!-- <div *ngIf="f.taxFileNo.errors.mask">Invalid BSB format.</div> -->
      </div>
    </div>

    <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
      <label
        ><strong> Tax File Number Exemption Code </strong>
        <strong *ngIf="validator('taxFileNoExcemptionCode')" class="text-lime required"> &nbsp; * </strong></label
      >
      <input
        nbInput
        fullWidth
        placeholder=""
        mask="A*"
        maxlength="30"
        shape="semi-round"
        type="text"
        fieldSize="large"
        formControlName="taxFileNoExcemptionCode"
        status="{{ submitted && f.taxFileNoExcemptionCode.errors ? 'danger' : 'basic' }}"
      />
      <div *ngIf="submitted && f.taxFileNoExcemptionCode.errors" class="invalid-feedback">
        <div *ngIf="f.taxFileNoExcemptionCode.errors.required">Tax File Number Exemption Code is required.</div>
      </div>
    </div>
  </div>

  <div class="w-full px-2 my-[15px]">
    <button class="float-right" [nbSpinner]="loading" nbButton status="primary" style="min-width: 135px">UPDATE</button>
  </div>

  <!-- [disabled]="!userForm.valid" -->
</form>
