<div *ngIf="!financialsData && !financialsForm">
   
  <div class="p-d-flex p-mb-3">
    <p-skeleton size="3rem" styleClass="p-mr-2"></p-skeleton>
    &nbsp;
    <div>
      <p-skeleton width="10rem" height="50px" styleClass="p-mb-2"></p-skeleton>
    </div>
  </div>
  <p-skeleton width="100%" height="150px"></p-skeleton>
</div>

<form [formGroup]="financialsForm" autocomplete="off">
  <div class="sm:w-full px-2 my-[15px]">
    <div class="flex flex-wrap -mx-2" *ngIf="error">
      <div class="my-[15px]">
        <nb-alert accent="danger">{{ error }}</nb-alert>
      </div>
    </div>

    <div formArrayName="financials">
      <div *ngFor="let financial of financials?.controls; let i = index">
        <div [formGroupName]="i">
          <!-- <div class="flex flex-wrap -mx-2" *ngIf="i !== 0" style="border-top: 1px solid #e9ecef;margin: 14px 0px;">
                    </div> -->

          <div class="flex flex-wrap -mx-2 array-row my-[15px]">
            <div class="w-full flex flex-wrap">
              <div class="w-full sm:w-8/12 md:w-10/12 px-2 title-row">
                <div *ngIf="editMode">
                  <img
                    *ngIf="financial.value.editMode && getValue(i, 'iconUrl')"
                    style="min-width: 50px; height: 50px"
                    [src]="getValue(i, 'iconUrl')"
                    nbPopoverTrigger="click"
                    nbPopoverPlacement="right"
                    [nbPopoverContext]="i"
                    [nbPopover]="templateRef"
                  />

                  <img
                    *ngIf="!financial.value.editMode && getValue(i, 'iconUrl')"
                    [src]="getValue(i, 'iconUrl')"
                    style="min-width: 50px; height: 50px"
                  />

                  <button
                    *ngIf="financial.value.editMode && !getValue(i, 'iconUrl')"
                    style="background: #f8f8f8; margin-right: 5px; margin-left: 10px"
                    type="button"
                    nbButton
                    ghost
                    shape="semi-round"
                    status="primary"
                    nbPopoverTrigger="click"
                    nbPopoverPlacement="right"
                    [nbPopoverContext]="i"
                    [nbPopover]="templateRef"
                  >
                    <nb-icon icon="plus-outline"> </nb-icon>
                  </button>

                  <button
                    *ngIf="!financial.value.editMode && !getValue(i, 'iconUrl')"
                    disabled
                    style="background: #f8f8f8; margin-right: 5px; margin-left: 10px"
                    type="button"
                    nbButton
                    ghost
                    shape="semi-round"
                    status="primary"
                  >
                    <nb-icon icon="plus-outline"> </nb-icon>
                  </button>

                  <div
                    *ngIf="getFinancialsValidity(i, 'title') === 'danger'"
                    class="invalid-feedback caption status-danger"
                  >
                    Icon is Required.
                  </div>
                </div>
                <div *ngIf="!editMode && getValue(i, 'iconUrl')">
                  <img [src]="getValue(i, 'iconUrl')" style="min-width: 50px; height: 50px" />
                </div>

                <div class="">
                  <h6 *ngIf="!editMode">{{ getValue(i, "title") }}</h6>
                  <input
                    placeholder="Add Title"
                    *ngIf="editMode"
                    type="text"
                    shape="semi-round"
                    nbInput
                    id="title{{ i }}"
                    name="title"
                    formControlName="title"
                    required
                    [status]="getFinancialsValidity(i, 'title')"
                  />
                  <div
                    *ngIf="getFinancialsValidity(i, 'title') === 'danger'"
                    class="invalid-feedback caption status-danger"
                  >
                    Title is Required.
                  </div>
                </div>
              </div>
              <div *ngIf="isAdmin()" class="w-full sm:w-4/12 md:w-2/12 px-2 text-end mb-[5px]">
                <!-- <nb-checkbox
                [disabled]="financial.invalid"
                class="float-right"
                id="data+ {{ i }}"
                *ngIf="financial.value.editMode"
                (checkedChange)="enableEditMode(financial, false)"
                checked
                nbTooltip="Save"
                nbTooltipStatus="control"
                nbTooltipPlacement="bottom"
              >
              </nb-checkbox> -->
                <button
                  [disabled]="financial.invalid"
                  *ngIf="financial.value.editMode"
                  nbButton
                  status="primary"
                  style="min-width: 120px"
                  (click)="enableEditMode(financial, false)"
                >
                  UPDATE
                </button>
                <button
                  *ngIf="!financial.value.editMode"
                  nbButton
                  ghost
                  shape="round"
                  status="default"
                  class="button-icon float-right"
                  (click)="enableEditMode(financial, true)"
                  nbTooltip="Edit"
                  nbTooltipStatus="control"
                  nbTooltipPlacement="bottom"
                >
                  <nb-icon icon="editIcon" pack="custom"></nb-icon>
                </button>
              </div>
            </div>
            <div class="w-full px-2">
              <div *ngIf="!editMode" class="html-contant" [innerHTML]="getValue(i, 'description')"></div>
              <!-- <textarea placeholder="Write something here....." *ngIf="editMode" rows="5" type="text"
                                shape="semi-round" nbInput fieldSize="large" fullWidth id="description{{i}}"
                                name="description" formControlName="description" required
                                [status]="getFinancialsValidity(i, 'description')"> -->
              <!-- </textarea> -->
              <div *ngIf="editMode">
                <app-ckeditor id="description{{ i }}" *ngIf="financial.value.editMode" formControlName="description">
                </app-ckeditor>

                <div
                  *ngIf="!financial.value.editMode && getValue(i, 'description')"
                  class="html-contant cke_editable"
                  [innerHTML]="getValue(i, 'description')"
                ></div>

                <div class="disable-html" *ngIf="!getValue(i, 'description') && !financial.value.editMode">
                  <span style="color: #bfc6d0"> Write something here..... </span>
                </div>

                <div
                  *ngIf="getFinancialsValidity(i, 'description') === 'danger'"
                  class="invalid-feedback caption status-danger"
                >
                  Description is Required.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-wrap -mx-2" *ngIf="editMode">
      <!-- <div class="w-6/12 px-2 my-[15px]">
                <button class="bg-velvet-700 hover:bg-velvet-600 text-white" nbButton status="default" type="button" (click)="addFinancials()">
                    ADD NEW ENTRY
                    <nb-icon icon="plus-outline">
                    </nb-icon>
                </button>
            </div> -->
      <!-- <div class="w-6/12 px-2 my-[15px]">
                <button *ngIf="financials.controls.length > 0" class="float-right" [nbSpinner]="loading" nbButton
                    status="primary">
                    UPDATE
                </button>
            </div> -->
    </div>
  </div>
</form>

<ng-template #templateRef let-index>
  <div style="display: flex">
    <span *ngFor="let icon of iconsData" class="icon-list-item" (click)="selectIcon(index, icon)">
      <img [src]="icon.value" />
    </span>
  </div>
</ng-template>
